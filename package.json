{"name": "aceprep", "version": "0.1.0", "private": true, "scripts": {"predev": "npx prisma generate", "dev": "NODE_OPTIONS='--max-old-space-size=8192' next dev", "prebuild": "npx prisma generate", "build": "NODE_OPTIONS='--max-old-space-size=8192' next build", "start": "next start", "lint": "next lint", "commit": "git-cz", "format": "npx prettier . --write", "prisma:format": "npx prisma format", "prisma:studio": "npx prisma studio", "prisma:generate": "npx prisma generate", "migrate:assessment": "npx ts-node scripts/migrate-assessment-criteria.ts", "migrate:assessment:rollback": "npx ts-node scripts/migrate-assessment-criteria.ts rollback", "preview-email": "email dev"}, "dependencies": {"@aws-sdk/client-rekognition": "^3.830.0", "@aws-sdk/client-s3": "^3.474.0", "@aws-sdk/s3-request-presigner": "^3.474.0", "@azure/openai": "^1.0.0-beta.12", "@camped-layout/sub-base": "^1.0.3", "@camped-tools/prettier-config": "^0.0.3", "@camped-ui/avatar": "^2.0.2", "@camped-ui/badge": "^2.0.2", "@camped-ui/breadcrumb": "^2.0.3", "@camped-ui/button": "^2.0.2", "@camped-ui/calendar": "^2.1.2", "@camped-ui/card": "^2.0.2", "@camped-ui/checkbox": "^2.1.0", "@camped-ui/collapsible": "^2.1.0", "@camped-ui/command": "^2.0.2", "@camped-ui/dialog": "^2.0.2", "@camped-ui/dropdown-menu": "^2.0.2", "@camped-ui/file-uploader": "^1.1.1", "@camped-ui/form": "^2.0.2", "@camped-ui/input": "^2.0.2", "@camped-ui/label": "^2.0.2", "@camped-ui/lib": "^2.0.2", "@camped-ui/navigation-menu": "^2.0.2", "@camped-ui/popover": "^2.0.2", "@camped-ui/progress": "^2.1.0", "@camped-ui/radio-group": "^2.1.0", "@camped-ui/select": "^2.0.2", "@camped-ui/separator": "^2.0.2", "@camped-ui/sheet": "^2.0.2", "@camped-ui/skeleton": "^2.0.2", "@camped-ui/slider": "^2.1.0", "@camped-ui/switch": "^2.1.0", "@camped-ui/table": "^2.0.2", "@camped-ui/tabs": "^2.0.2", "@camped-ui/tag-input": "^1.0.5", "@camped-ui/textarea": "^2.0.2", "@camped-ui/tooltip": "^2.0.2", "@codesandbox/sandpack-react": "^2.12.1", "@daily-co/daily-js": "^0.79.0", "@daily-co/realtime-ai-daily": "^0.2.3", "@datadog/datadog-api-client": "^1.22.0", "@deepgram/sdk": "^3.4.0", "@dytesdk/react-ui-kit": "^2.0.4", "@dytesdk/react-web-core": "^2.1.0", "@ffmpeg/core": "^0.11.0", "@ffmpeg/ffmpeg": "^0.11.6", "@google-cloud/speech": "^6.6.1", "@google-cloud/storage": "^7.11.1", "@google-cloud/vertexai": "^1.5.0", "@google/generative-ai": "^0.21.0", "@headlessui/react": "^1.7.14", "@hello-pangea/dnd": "^17.0.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.1.0", "@internationalized/date": "^3.5.6", "@langchain/core": "^0.3.13", "@livekit/components-react": "^2.9.9", "@livekit/components-styles": "^1.1.6", "@livekit/krisp-noise-filter": "0.3.4", "@monaco-editor/react": "^4.5.2", "@next-auth/prisma-adapter": "^1.0.7", "@next/bundle-analyzer": "^14.0.4", "@prisma/client": "^4.15.0", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-toggle-group": "^1.0.4", "@react-email/components": "^0.0.11", "@react-email/render": "0.0.9-canary.2", "@reduxjs/toolkit": "^2.2.7", "@storyblok/react": "^2.4.4", "@streampot/client": "^0.0.18", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "4.32.1", "@tanstack/react-table": "^8.9.3", "@tiptap/extension-font-family": "^2.5.8", "@tiptap/extension-link": "^2.5.8", "@tiptap/extension-text-style": "^2.5.8", "@tiptap/extension-underline": "^2.5.8", "@tiptap/pm": "^2.3.0", "@tiptap/react": "^2.3.0", "@tiptap/starter-kit": "^2.3.0", "@types/node": "20.2.5", "@types/react": "18.2.7", "@types/react-dom": "18.2.4", "@upstash/ratelimit": "^0.4.2", "@upstash/redis": "^1.20.6", "@vercel/analytics": "^1.0.2", "aws-sdk": "^2.1438.0", "axios": "^1.6.8", "base-64": "^1.0.0", "better-react-mathjax": "^2.0.4-beta1", "chart.js": "^4.4.1", "clone-deep": "^4.0.1", "clsx": "^1.2.1", "copy-to-clipboard": "^3.3.3", "date-fns": "^3.6.0", "eslint": "8.41.0", "eslint-config-next": "13.4.4", "eventsource-parser": "^1.0.0", "formidable": "^2.1.1", "formidable-serverless": "^1.1.1", "framer-motion": "^10.12.5", "generate-api-key": "^1.0.2", "graphql": "^16.8.1", "graphql-request": "^6.1.0", "hls.js": "^1.6.5", "js-cookie": "^3.0.5", "livekit-client": "^2.13.5", "livekit-server-sdk": "^2.13.0", "lucide-react": "0.427.0", "monaco-editor": "^0.52.2", "nanoid": "^4.0.2", "next": "^14.0.4", "next-auth": "^4.22.1", "next-pwa": "^5.6.0", "next-themes": "^0.2.1", "nextra": "^2.13.3", "nextra-theme-docs": "^2.13.3", "nodemailer": "^6.9.3", "openai": "^4.19.1", "pdf-parse": "1.1.1", "pdfjs-dist": "3.10.111", "prettier-plugin-sql": "^0.18.1", "prettier-plugin-tailwindcss": "^0.6.5", "prisma-docs-generator": "^0.8.0", "raw-body": "^2.5.2", "razorpay": "^2.9.4", "react": "18.2.0", "react-aria": "^3.35.1", "react-audio-player": "^0.17.0", "react-audio-voice-recorder": "^2.2.0", "react-chartjs-2": "^5.2.0", "react-day-picker": "^9.1.4", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-email": "^1.10.0", "react-hook-form": "^7.49.3", "react-hot-toast": "^2.4.1", "react-lazyload-youtube": "^1.0.8", "react-markdown": "^8.0.7", "react-pdf": "^7.3.3", "react-resizable-panels": "^1.0.7", "react-stately": "^3.33.0", "react-textarea-autosize": "^8.5.3", "react-webcam": "7.2.0", "realtime-ai": "^0.2.3", "realtime-ai-react": "^0.2.2", "recharts": "^2.13.0", "runes": "^0.4.3", "rxjs": "^7.8.1", "server-only": "^0.0.1", "sharp": "^0.34.2", "simple-peer": "^9.11.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "storyblok-rich-text-react-renderer": "^2.9.0", "stripe": "^12.17.0", "tailwind-merge": "^1.12.0", "tailwindcss-animate": "^1.0.5", "typescript": "5.0.4", "video-react": "^0.16.0", "xlsx": "^0.18.5", "zustand": "^4.5.4"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.27.4", "@babel/runtime": "^7.27.6", "@tailwindcss/typography": "^0.5.9", "@trivago/prettier-plugin-sort-imports": "^4.2.0", "@types/formidable": "^2.0.5", "autoprefixer": "^10.4.14", "commitizen": "4.3.0", "cz-conventional-changelog": "3.3.0", "postcss": "^8.4.24", "prettier": "3.0.3", "prisma": "^4.15.0", "sonner": "^1.4.41", "tailwindcss": "^3.3.2"}, "resolutions": {"@codesandbox/sandpack-client": "2.10.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"], "*.{js,jsx,ts,tsx,json,html,md,css}": ["prettier --write"]}, "packageManager": "yarn@1.22.22"}