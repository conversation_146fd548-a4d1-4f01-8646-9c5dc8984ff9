/**
 * Test utility for video conversion workflow
 * This file can be used to test the HLS to MP4 conversion functionality
 */

import { convertTailoredPracticeVideo } from './video-conversion';
import { getVideoProcessingConfig, validateVideoProcessingConfig, checkFFmpegAvailability } from './video-processing-config';
import { createComprehensiveFallbackFeedback } from './video-feedback-fallback';

/**
 * Test the video processing configuration
 */
export async function testVideoProcessingConfig(): Promise<{
  success: boolean;
  errors: string[];
  config?: any;
}> {
  try {
    console.log('🔧 Testing video processing configuration...');
    
    const config = getVideoProcessingConfig();
    const errors = validateVideoProcessingConfig(config);
    
    if (errors.length > 0) {
      console.error('❌ Configuration validation failed:', errors);
      return { success: false, errors };
    }
    
    console.log('✅ Configuration validation passed');
    console.log('📋 Configuration:', {
      tempDir: config.tempDir,
      maxFileSizeMB: config.maxFileSizeMB,
      timeoutMinutes: config.timeoutMinutes,
      bucketName: config.bucketName,
      hasCredentials: !!config.gcpCredentials.projectId,
    });
    
    return { success: true, errors: [], config };
  } catch (error) {
    console.error('❌ Configuration test failed:', error);
    return { success: false, errors: [error.message] };
  }
}

/**
 * Test FFmpeg availability
 */
export async function testFFmpegAvailability(): Promise<{
  success: boolean;
  available: boolean;
  error?: string;
}> {
  try {
    console.log('🎬 Testing FFmpeg availability...');
    
    const available = await checkFFmpegAvailability();
    
    if (available) {
      console.log('✅ FFmpeg is available');
      return { success: true, available: true };
    } else {
      console.log('❌ FFmpeg is not available');
      return { success: true, available: false };
    }
  } catch (error) {
    console.error('❌ FFmpeg test failed:', error);
    return { success: false, available: false, error: error.message };
  }
}

/**
 * Test fallback feedback generation
 */
export function testFallbackFeedback(): {
  success: boolean;
  feedback?: any;
  error?: string;
} {
  try {
    console.log('🔄 Testing fallback feedback generation...');
    
    const testQuestion = "Tell me about a challenging project you worked on.";
    const testRole = "Software Engineer";
    const testLevel = "Mid Level";
    const testErrorType = "conversion_error";
    
    const feedback = createComprehensiveFallbackFeedback(
      testQuestion,
      testRole,
      testLevel,
      testErrorType
    );
    
    // Validate feedback structure
    const requiredFields = [
      'transcript',
      'overall_score',
      'short_summary',
      'communication_skills',
      'proctoring_assessment',
      'interview_performance',
      'recommendations',
      'areas_for_improvement'
    ];
    
    for (const field of requiredFields) {
      if (!(field in feedback)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    console.log('✅ Fallback feedback generation successful');
    console.log('📋 Generated feedback structure:', {
      hasTranscript: !!feedback.transcript,
      recommendationsCount: feedback.recommendations.length,
      improvementsCount: feedback.areas_for_improvement.length,
      shortSummaryLength: feedback.short_summary.length,
    });
    
    return { success: true, feedback };
  } catch (error) {
    console.error('❌ Fallback feedback test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test video conversion workflow (dry run without actual conversion)
 */
export async function testVideoConversionWorkflow(practiceId: string): Promise<{
  success: boolean;
  steps: { [key: string]: boolean };
  errors: string[];
}> {
  const steps = {
    configValidation: false,
    ffmpegAvailability: false,
    fallbackGeneration: false,
  };
  const errors: string[] = [];
  
  try {
    console.log(`🧪 Testing video conversion workflow for practice ID: ${practiceId}`);
    
    // Test 1: Configuration validation
    const configTest = await testVideoProcessingConfig();
    steps.configValidation = configTest.success;
    if (!configTest.success) {
      errors.push(...configTest.errors);
    }
    
    // Test 2: FFmpeg availability
    const ffmpegTest = await testFFmpegAvailability();
    steps.ffmpegAvailability = ffmpegTest.success && ffmpegTest.available;
    if (!ffmpegTest.success) {
      errors.push(ffmpegTest.error || 'FFmpeg test failed');
    } else if (!ffmpegTest.available) {
      errors.push('FFmpeg is not available on this system');
    }
    
    // Test 3: Fallback feedback generation
    const fallbackTest = testFallbackFeedback();
    steps.fallbackGeneration = fallbackTest.success;
    if (!fallbackTest.success) {
      errors.push(fallbackTest.error || 'Fallback feedback test failed');
    }
    
    const allStepsSuccessful = Object.values(steps).every(step => step);
    
    console.log('📊 Test Results:', {
      overall: allStepsSuccessful ? '✅ PASS' : '❌ FAIL',
      steps,
      errorCount: errors.length,
    });
    
    if (errors.length > 0) {
      console.log('❌ Errors found:', errors);
    }
    
    return {
      success: allStepsSuccessful,
      steps,
      errors,
    };
    
  } catch (error) {
    console.error('❌ Workflow test failed:', error);
    errors.push(error.message);
    return {
      success: false,
      steps,
      errors,
    };
  }
}

/**
 * Run all tests
 */
export async function runAllTests(practiceId?: string): Promise<void> {
  console.log('🚀 Starting comprehensive video conversion tests...\n');
  
  try {
    // Test configuration
    await testVideoProcessingConfig();
    console.log('');
    
    // Test FFmpeg
    await testFFmpegAvailability();
    console.log('');
    
    // Test fallback feedback
    testFallbackFeedback();
    console.log('');
    
    // Test full workflow if practice ID provided
    if (practiceId) {
      await testVideoConversionWorkflow(practiceId);
    } else {
      console.log('ℹ️  Skipping workflow test (no practice ID provided)');
    }
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error);
  }
}

// Export for use in development/testing
export default {
  testVideoProcessingConfig,
  testFFmpegAvailability,
  testFallbackFeedback,
  testVideoConversionWorkflow,
  runAllTests,
};
