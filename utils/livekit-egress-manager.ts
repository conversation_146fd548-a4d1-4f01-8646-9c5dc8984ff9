/**
 * LiveKit Egress Recording Manager
 * Utility functions to manage LiveKit egress recordings
 */

export interface EgressRecordingOptions {
  roomName: string;
  layout?: 'speaker' | 'grid' | 'single-speaker';
  width?: number;
  height?: number;
  videoCodec?: 'H264_MAIN' | 'H264_HIGH' | 'VP8' | 'VP9';
  audioBitrate?: number;
  videoBitrate?: number;
  outputType?: 'file' | 'stream';
}

export interface EgressRecordingResult {
  success: boolean;
  egressId?: string;
  roomName?: string;
  status?: string;
  customBaseUrl?: string;
  error?: string;
  details?: string;
}

export interface EgressStatusResult {
  success: boolean;
  egress?: any;
  error?: string;
  details?: string;
}

/**
 * Start a LiveKit egress recording
 */
export async function startEgressRecording(
  options: EgressRecordingOptions
): Promise<EgressRecordingResult> {
  try {
    const response = await fetch('/api/livekit-egress/start-recording', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(options),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to start recording');
    }

    return result;
  } catch (error) {
    console.error('❌ Failed to start egress recording:', error);
    return {
      success: false,
      error: 'Failed to start recording',
      details: error.message,
    };
  }
}

/**
 * Stop a LiveKit egress recording
 */
export async function stopEgressRecording(egressId: string): Promise<EgressRecordingResult> {
  try {
    const response = await fetch('/api/livekit-egress/stop-recording', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ egressId }),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to stop recording');
    }

    return result;
  } catch (error) {
    console.error('❌ Failed to stop egress recording:', error);
    return {
      success: false,
      error: 'Failed to stop recording',
      details: error.message,
    };
  }
}

/**
 * Get the status of a LiveKit egress recording
 */
export async function getEgressStatus(egressId: string): Promise<EgressStatusResult> {
  try {
    const response = await fetch(`/api/livekit-egress/start-recording?egressId=${egressId}`, {
      method: 'GET',
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to get recording status');
    }

    return result;
  } catch (error) {
    console.error('❌ Failed to get egress status:', error);
    return {
      success: false,
      error: 'Failed to get recording status',
      details: error.message,
    };
  }
}

/**
 * Enhanced recording manager class for complex scenarios
 */
export class LiveKitEgressManager {
  private egressId: string | null = null;
  private roomName: string;
  private options: EgressRecordingOptions;

  constructor(roomName: string, options: Partial<EgressRecordingOptions> = {}) {
    this.roomName = roomName;
    this.options = {
      roomName,
      layout: 'speaker',
      width: 1920,
      height: 1080,
      videoCodec: 'H264_MAIN',
      audioBitrate: 128,
      videoBitrate: 4500,
      outputType: 'file',
      ...options,
    };
  }

  /**
   * Start recording
   */
  async startRecording(): Promise<EgressRecordingResult> {
    if (this.egressId) {
      console.warn('⚠️ Recording already in progress');
      return {
        success: false,
        error: 'Recording already in progress',
      };
    }

    const result = await startEgressRecording(this.options);
    
    if (result.success && result.egressId) {
      this.egressId = result.egressId;
      console.log('🎬 Recording started with egress ID:', this.egressId);
    }

    return result;
  }

  /**
   * Stop recording
   */
  async stopRecording(): Promise<EgressRecordingResult> {
    if (!this.egressId) {
      console.warn('⚠️ No active recording to stop');
      return {
        success: false,
        error: 'No active recording',
      };
    }

    const result = await stopEgressRecording(this.egressId);
    
    if (result.success) {
      console.log('🛑 Recording stopped for egress ID:', this.egressId);
      this.egressId = null;
    }

    return result;
  }

  /**
   * Get current recording status
   */
  async getStatus(): Promise<EgressStatusResult> {
    if (!this.egressId) {
      return {
        success: false,
        error: 'No active recording',
      };
    }

    return await getEgressStatus(this.egressId);
  }

  /**
   * Get current egress ID
   */
  getEgressId(): string | null {
    return this.egressId;
  }

  /**
   * Check if recording is active
   */
  isRecording(): boolean {
    return this.egressId !== null;
  }
}

/**
 * Create a recording manager instance
 */
export function createEgressManager(
  roomName: string, 
  options?: Partial<EgressRecordingOptions>
): LiveKitEgressManager {
  return new LiveKitEgressManager(roomName, options);
}
