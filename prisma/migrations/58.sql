-- AlterTable
ALTER TABLE "coding_problems"
ADD COLUMN "createdById" TEXT,
ADD COLUMN "organizationId" TEXT;

-- AlterTable
ALTER TABLE "custom_questions"
ADD COLUMN "organizationId" TEXT,
ADD COLUMN "videoUrl" TEXT,
ADD COLUMN "referenceVideoUrl" TEXT;

-- AlterTable
ALTER TABLE "frontend_problems"
ADD COLUMN "createdById" TEXT,
ADD COLUMN "organizationId" TEXT;

-- AlterTable
ALTER TABLE "mcq_problems"
ADD COLUMN "createdById" TEXT,
ADD COLUMN "organizationId" TEXT;

-- AddForeignKey
ALTER TABLE "custom_questions"
ADD CONSTRAINT "custom_questions_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "custom_questions"
ADD CONSTRAINT "custom_questions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coding_problems"
ADD CONSTRAINT "coding_problems_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coding_problems"
ADD CONSTRAINT "coding_problems_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "frontend_problems"
ADD CONSTRAINT "frontend_problems_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "frontend_problems"
ADD CONSTRAINT "frontend_problems_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "mcq_problems"
ADD CONSTRAINT "mcq_problems_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "mcq_problems"
ADD CONSTRAINT "mcq_problems_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE;
