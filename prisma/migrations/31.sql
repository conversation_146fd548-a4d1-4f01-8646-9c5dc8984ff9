-- CreateE<PERSON>
CREATE TYPE "EventType" AS ENUM ('INTERVIEWATHON', 'CODING_PRACTICE', 'FRONTEND_PRACTICE', 'MOCK_INTERVIEW', 'CAREER_PRACTICE','TAILORED_PRACTICE');

-- CreateTable
CREATE TABLE "leaderboard" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "score" INTEGER,
    "eventId" TEXT NOT NULL,
    "eventType" "EventType" NOT NULL,

    CONSTRAINT "leaderboard_pkey" PRIMARY KEY ("id")
);


-- AddForeignKey
ALTER TABLE "leaderboard" ADD CONSTRAINT "leaderboard_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddF<PERSON>ign<PERSON><PERSON>
ALTER TABLE "leaderboard" ADD CONSTRAINT "leaderboard_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

