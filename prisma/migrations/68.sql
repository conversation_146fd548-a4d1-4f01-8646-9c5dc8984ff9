-- DropFore<PERSON><PERSON><PERSON>
ALTER TABLE "video_meeting_interview"
DROP CONSTRAINT "video_meeting_interview_careerPracticeId_fkey";

-- AlterTable
ALTER TABLE "video_meeting_interview"
ADD COLUMN "eventId" TEXT,
ALTER COLUMN "careerPracticeId"
DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "video_meeting_interview"
ADD CONSTRAINT "video_meeting_interview_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "event_details" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddF<PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "video_meeting_interview"
ADD CONSTRAINT "video_meeting_interview_careerPracticeId_fkey" FOREIGN KEY ("careerPracticeId") REFERENCES "career_practice" ("id") ON DELETE SET NULL ON UPDATE CASCADE;
