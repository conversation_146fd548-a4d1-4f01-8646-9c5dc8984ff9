-- CreateE<PERSON>
CREATE TYPE "VideoCallStatusType" AS ENUM('PENDING', 'COMPLETED', 'CANCELLED');

-- CreateTable
CREATE TABLE "video_meeting_interview" (
  "id" TEXT NOT NULL,
  "meetingId" TEXT,
  "meetingStatus" "VideoCallStatusType" NOT NULL DEFAULT 'PENDING',
  "participants" JSONB,
  "scheduleTime" TIMESTAMP(3) NOT NULL,
  "meetingType" TEXT NOT NULL,
  "careerPracticeId" TEXT NOT NULL,
  "createdById" TEXT,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  "organizationId" TEXT,
  "userId" TEXT,
  "interviewerId" TEXT,
  CONSTRAINT "video_meeting_interview_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "video_meeting_interview_careerPracticeId_idx" ON "video_meeting_interview" ("careerPracticeId");

-- AddForeignKey
ALTER TABLE "video_meeting_interview"
ADD CONSTRAINT "video_meeting_interview_careerPracticeId_fkey" FOREIGN KEY ("careerPracticeId") REFERENCES "career_practice" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "video_meeting_interview"
ADD CONSTRAINT "video_meeting_interview_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "video_meeting_interview"
ADD CONSTRAINT "video_meeting_interview_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization" ("id") ON DELETE SET NULL ON UPDATE CASCADE;
