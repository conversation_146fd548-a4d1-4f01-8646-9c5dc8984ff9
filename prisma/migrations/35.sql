-- CreateEnum
CREATE TYPE "IntegrationType" AS ENUM('SLACK');

-- CreateTable
CREATE TABLE "integration" (
  "id" TEXT NOT NULL,
  "platform" "IntegrationType" NOT NULL,
  "accountId" TEXT NOT NULL,
  "accountName" TEXT,
  "accessToken" TEXT NOT NULL,
  "refreshToken" TEXT,
  "config" JSONB,
  "page" JSONB,
  "organizationId" TEXT NOT NULL,
  CONSTRAINT "integration_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "integration_organizationId_platform_idx" ON "integration" ("organizationId", "platform");

-- CreateIndex
CREATE UNIQUE INDEX "integration_platform_organizationId_key" ON "integration" ("platform", "organizationId");

-- AddForeign<PERSON><PERSON>
ALTER TABLE "integration"
ADD CONSTRAINT "integration_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
