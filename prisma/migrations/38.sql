-- CreateTable
CREATE TABLE "MembershipOnEventDetails" (
  "id" TEXT NOT NULL,
  "eventId" TEXT,
  "membershipId" TEXT,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "MembershipOnEventDetails_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "MembershipOnEventDetails"
ADD CONSTRAINT "MembershipOnEventDetails_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "event_details" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MembershipOnEventDetails"
ADD CONSTRAINT "MembershipOnEventDetails_membershipId_fkey" FOREIGN KEY ("membershipId") REFERENCES "membership" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- CreateIndex
CREATE UNIQUE INDEX "MembershipOnEventDetails_membershipId_eventId_key" ON "MembershipOnEventDetails" ("membershipId", "eventId");
