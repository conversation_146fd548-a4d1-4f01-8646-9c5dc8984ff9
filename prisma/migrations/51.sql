-- CreateEnum
CREATE TYPE "DepartmentRole" AS ENUM('MEMBER', 'ADMIN', 'STUDENT');

-- CreateTable
CREATE TABLE "department_membership_mapping" (
  "id" TEXT NOT NULL,
  "departmentId" TEXT,
  "membershipId" TEXT,
  "role" "DepartmentRole",
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "department_membership_mapping_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "department_membership_mapping_membershipId_departmentId_key" ON "department_membership_mapping" ("membershipId", "departmentId");

-- DropForeignKey
ALTER TABLE "membership"
DROP CONSTRAINT "membership_departmentId_fkey";

-- AlterTable
ALTER TABLE "membership"
DROP COLUMN "departmentId";

-- AddForeignKey
ALTER TABLE "department_membership_mapping"
ADD CONSTRAINT "department_membership_mapping_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "department" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "department_membership_mapping"
ADD CONSTRAINT "department_membership_mapping_membershipId_fkey" FOREIGN KEY ("membershipId") REFERENCES "membership" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
