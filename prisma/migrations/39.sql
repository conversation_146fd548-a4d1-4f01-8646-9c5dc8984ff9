-- DropFore<PERSON>Key
ALTER TABLE "MembershipOnEventDetails"
DROP CONSTRAINT "MembershipOnEventDetails_eventId_fkey";

-- DropForeignKey
ALTER TABLE "MembershipOnEventDetails"
DROP CONSTRAINT "MembershipOnEventDetails_membershipId_fkey";

-- AddForeignKey
ALTER TABLE "MembershipOnEventDetails"
ADD CONSTRAINT "MembershipOnEventDetails_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "event_details" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MembershipOnEventDetails"
ADD CONSTRAINT "MembershipOnEventDetails_membershipId_fkey" FOREIGN KEY ("membershipId") REFERENCES "membership" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AlterTable
ALTER TABLE "event_details"
ADD COLUMN "createdById" TEXT;

-- AddForeignKey
ALTER TABLE "event_details"
ADD CONSTRAINT "event_details_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
