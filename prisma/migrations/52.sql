-- CreateTable
CREATE TABLE "group" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "organizationId" TEXT NOT NULL,
  "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP(3) NOT NULL,
  "departmentId" TEXT NOT NULL,
  CONSTRAINT "group_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "group_membership_mapping" (
  "id" TEXT NOT NULL,
  "role" "DepartmentRole" NOT NULL,
  "groupId" TEXT NOT NULL,
  "membershipId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "group_membership_mapping_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "group_organizationId_idx" ON "group" ("organizationId");

-- AddForeignKey
ALTER TABLE "group"
ADD CONSTRAINT "group_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "group"
ADD CONSTRAINT "group_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "department" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "group_membership_mapping"
ADD CONSTRAINT "group_membership_mapping_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "group" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "group_membership_mapping"
ADD CONSTRAINT "group_membership_mapping_membershipId_fkey" FOREIGN KEY ("membershipId") REFERENCES "membership" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
