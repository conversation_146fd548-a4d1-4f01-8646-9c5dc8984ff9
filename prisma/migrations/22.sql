

-- CreateTable
CREATE TABLE "email_templates" (
    "id" TEXT NOT NULL,
    "emailType" TEXT NOT NULL,
    "emailContent" TEXT NOT NULL,
    "emailSubject" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "email_templates_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "email_templates" ADD CONSTRAINT "email_templates_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

