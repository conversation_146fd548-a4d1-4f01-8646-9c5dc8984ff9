-- AlterEnum
ALTER TYPE "MembershipRole"
ADD VALUE 'SUPER_ADMIN';

-- AlterTable
ALTER TABLE "membership"
ADD COLUMN "departmentId" TEXT;

-- CreateTable
CREATE TABLE "department" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "organizationId" TEXT NOT NULL,
  "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "department_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "department_organizationId_idx" ON "department" ("organizationId");

-- AddForeignKey
ALTER TABLE "membership"
ADD CONSTRAINT "membership_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "department" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddF<PERSON><PERSON>Key
ALTER TABLE "department"
ADD CONSTRAINT "department_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
