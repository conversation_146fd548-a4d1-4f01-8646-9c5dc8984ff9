-- Add face verification fields to career_practice table
ALTER TABLE "career_practice" 
ADD COLUMN "faceVerificationScore" DOUBLE PRECISION,
ADD COLUMN "faceVerificationStatus" TEXT,
ADD COLUMN "faceVerificationData" JSONB;

-- Add comments for documentation
COMMENT ON COLUMN "career_practice"."faceVerificationScore" IS 'AWS Rekognition confidence score (0-100)';
COMMENT ON COLUMN "career_practice"."faceVerificationStatus" IS 'Verification status: verified, failed, or pending';
COMMENT ON COLUMN "career_practice"."faceVerificationData" IS 'Complete verification details including score, matches, timestamp';

-- Create index for faster queries on verification status
CREATE INDEX "career_practice_face_verification_status_idx" ON "career_practice"("faceVerificationStatus");

-- Create index for faster queries on verification score
CREATE INDEX "career_practice_face_verification_score_idx" ON "career_practice"("faceVerificationScore");
