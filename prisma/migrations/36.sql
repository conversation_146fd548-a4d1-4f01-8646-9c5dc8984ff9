-- CreateEnum
CREATE TYPE "WebhookSource" AS ENUM('USER');

-- CreateTable
CREATE TABLE "webhook" (
  "id" TEXT NOT NULL,
  "name" TEXT,
  "url" TEXT NOT NULL,
  "organizationId" TEXT NOT NULL,
  "source" "WebhookSource" NOT NULL DEFAULT 'USER',
  "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP(3) NOT NULL,
  CONSTRAINT "webhook_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "webhook_organizationId_idx" ON "webhook" ("organizationId");

-- AddForeignKey
ALTER TABLE "webhook"
ADD CONSTRAINT "webhook_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
