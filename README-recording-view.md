# LiveKit Recording View Page

A custom recording template page for LiveKit egress that captures the entire screen including the SimpleVoiceAssistant component.

## 📍 Page Location

The recording view is available at: `/lk-recording-view`

## 🔗 URL Parameters

The page accepts these URL parameters (automatically provided by LiveKit egress):

| Parameter | Required | Description | Example |
|-----------|----------|-------------|---------|
| `url` | ✅ | LiveKit server WebSocket URL | `wss://your-livekit.com` |
| `token` | ✅ | Access token for the room | `eyJ0eXAi...` |
| `layout` | ❌ | Recording layout (default: 'speaker') | `speaker`, `grid`, `single-speaker` |
| `room` | ❌ | Room name for display | `interview-room-123` |

## 🌐 Example URL

```
https://your-app.com/lk-recording-view?url=wss%3A%2F%2Fyour-livekit.com&token=eyJ0eXAi...&layout=speaker&room=interview-123
```

## 🐍 Python Integration

Use the provided Python example (`examples/python-egress-recording.py`) to start recordings:

```python
import asyncio
from livekit import api

# Set your custom_base_url to point to this page
custom_base_url = "https://your-app.com/lk-recording-view"

# Start recording
request = api.RoomCompositeEgressRequest(
    room_name="your-room",
    layout="speaker",
    custom_base_url=custom_base_url,
    # ... other options
)

egress_info = await egress_client.start_room_composite_egress(request)
```

## 🎬 How It Works

1. **LiveKit Egress** launches a headless browser
2. **Browser navigates** to your `/lk-recording-view` page with parameters
3. **Page connects** to LiveKit room using provided token
4. **SimpleVoiceAssistant** component renders in full screen
5. **Recording starts** when page logs `START_RECORDING` to console
6. **Full screen captured** including all UI elements
7. **Recording stops** when page logs `END_RECORDING` or egress is stopped

## 🔧 Features

- ✅ Full screen recording of SimpleVoiceAssistant
- ✅ Automatic connection to LiveKit room
- ✅ Proper recording start/stop signaling
- ✅ Error handling and status display
- ✅ Responsive design for different screen sizes
- ✅ Loading states and connection feedback

## 🚀 Ready to Use

The page is ready to use with your Python egress recording setup. Just:

1. Deploy your app with the recording view page
2. Use the page URL as `custom_base_url` in your Python egress requests
3. LiveKit will handle the rest automatically

## 🔍 Testing

To test the recording view manually:

1. Get a valid LiveKit token for a room
2. Visit: `/lk-recording-view?url=YOUR_LIVEKIT_URL&token=YOUR_TOKEN`
3. The page should connect and show the SimpleVoiceAssistant interface
4. Check browser console for `START_RECORDING` message

## 📝 Notes

- The page waits 3 seconds after connection before signaling recording start
- This ensures the UI is fully loaded and stable
- The page handles connection errors and displays helpful error messages
- Recording will capture exactly what users see during interviews
