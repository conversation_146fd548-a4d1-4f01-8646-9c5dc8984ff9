# LiveKit Egress Recording Setup

This document explains how to set up and use the custom LiveKit egress recording system that captures the entire screen including the SimpleVoiceAssistant component.

## Overview

The system consists of:

1. **Custom Recording View** (`/lk-recording-view`) - A dedicated page that serves as the recording template
2. **Egress API Endpoints** - Server-side APIs to start/stop recordings
3. **Recording Manager Utility** - Helper functions to manage recordings
4. **Enhanced Interview Component** - Updated interview component with recording capabilities

## Architecture

```
LiveKit Egress Service
    ↓
Custom Recording View (/lk-recording-view)
    ↓
SimpleVoiceAssistant Component
    ↓
Full Screen Recording Output
```

## Setup

### 1. Environment Variables

Ensure these environment variables are set:

```env
# LiveKit Configuration
LIVEKIT_API_KEY=your_api_key
LIVEKIT_API_SECRET=your_api_secret
LIVEKIT_URL=wss://your-livekit-server.com

# Recording Output (GCP)
NEXT_PUBLIC_GCP_BUCKET=your-gcp-bucket
NEXT_PUBLIC_GCP_PROJECT_ID=your-project-id
NEXT_PUBLIC_PRIVATE_KEY=your-service-account-private-key
NEXT_PUBLIC_CLIENT_ID=your-client-id
NEXT_PUBLIC_CLIENT_EMAIL=your-service-account-email

# Application
NEXT_PUBLIC_API_BASE_URL=https://your-app-domain.com
```

### 2. Recording View Page

The recording view is available at `/lk-recording-view` and accepts these URL parameters:

- `url` (required): LiveKit server WebSocket URL
- `token` (required): Access token for the room
- `layout` (optional): Recording layout (default: 'speaker')
- `room` (optional): Room name

Example URL:
```
https://your-app.com/lk-recording-view?url=wss%3A%2F%2Fyour-livekit.com&token=eyJ0eXAi...&layout=speaker&room=interview-123
```

## Usage

### Method 1: Using the Enhanced Interview Component

```tsx
import EnhancedAIInterview from '@/components/enhanced-ai-interview';

export default function InterviewPage() {
  const handleRecordingStarted = (egressId: string) => {
    console.log('Recording started:', egressId);
    // Store egressId for later reference
  };

  const handleRecordingStopped = (egressId: string) => {
    console.log('Recording stopped:', egressId);
    // Handle recording completion
  };

  const handleRecordingError = (error: string) => {
    console.error('Recording error:', error);
    // Handle recording errors
  };

  return (
    <EnhancedAIInterview
      token={yourLiveKitToken}
      roomName="interview-room-123"
      enableEgressRecording={true}
      interviewData={{
        role: 'Software Developer',
        level: 'Senior',
        event: 'Technical Interview'
      }}
      onRecordingStarted={handleRecordingStarted}
      onRecordingStopped={handleRecordingStopped}
      onRecordingError={handleRecordingError}
    />
  );
}
```

### Method 2: Using the Recording Manager Directly

```tsx
import { createEgressManager } from '@/utils/livekit-egress-manager';

export default function CustomInterviewPage() {
  const [egressManager, setEgressManager] = useState(null);

  useEffect(() => {
    const manager = createEgressManager('interview-room-123', {
      layout: 'speaker',
      width: 1920,
      height: 1080,
      videoCodec: 'H264_MAIN',
    });
    setEgressManager(manager);
  }, []);

  const startRecording = async () => {
    if (egressManager) {
      const result = await egressManager.startRecording();
      if (result.success) {
        console.log('Recording started:', result.egressId);
      } else {
        console.error('Failed to start recording:', result.error);
      }
    }
  };

  const stopRecording = async () => {
    if (egressManager) {
      const result = await egressManager.stopRecording();
      if (result.success) {
        console.log('Recording stopped');
      } else {
        console.error('Failed to stop recording:', result.error);
      }
    }
  };

  // ... rest of your component
}
```

### Method 3: Direct API Calls

```tsx
// Start recording
const startRecording = async () => {
  const response = await fetch('/api/livekit-egress/start-recording', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      roomName: 'interview-room-123',
      layout: 'speaker',
      width: 1920,
      height: 1080,
    }),
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('Recording started:', result.egressId);
  }
};

// Stop recording
const stopRecording = async (egressId: string) => {
  const response = await fetch('/api/livekit-egress/stop-recording', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ egressId }),
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('Recording stopped');
  }
};
```

## API Endpoints

### POST /api/livekit-egress/start-recording

Start a new egress recording.

**Request Body:**
```json
{
  "roomName": "interview-room-123",
  "layout": "speaker",
  "width": 1920,
  "height": 1080,
  "videoCodec": "H264_MAIN",
  "audioBitrate": 128,
  "videoBitrate": 4500,
  "outputType": "file"
}
```

**Response:**
```json
{
  "success": true,
  "egressId": "EG_abc123",
  "roomName": "interview-room-123",
  "status": "EGRESS_STARTING",
  "customBaseUrl": "https://your-app.com/lk-recording-view",
  "message": "Recording started successfully"
}
```

### POST /api/livekit-egress/stop-recording

Stop an active egress recording.

**Request Body:**
```json
{
  "egressId": "EG_abc123"
}
```

**Response:**
```json
{
  "success": true,
  "egressId": "EG_abc123",
  "status": "EGRESS_COMPLETE",
  "endedAt": "2024-01-15T10:30:00Z",
  "message": "Recording stopped successfully"
}
```

### GET /api/livekit-egress/start-recording?egressId=EG_abc123

Get the status of an egress recording.

**Response:**
```json
{
  "success": true,
  "egress": {
    "egressId": "EG_abc123",
    "roomName": "interview-room-123",
    "status": "EGRESS_ACTIVE",
    "startedAt": "2024-01-15T10:00:00Z"
  }
}
```

## Recording Output

Recordings are saved to your configured GCP bucket in the following structure:
```
recordings/livekit-egress/interview-room-123-1642248000000.mp4
```

## Troubleshooting

### Common Issues

1. **Recording fails to start**
   - Check LiveKit environment variables
   - Verify GCP credentials and bucket permissions
   - Ensure the recording view page is accessible

2. **Recording view shows error**
   - Verify URL parameters are correctly encoded
   - Check LiveKit token validity
   - Ensure room exists and is active

3. **Recording quality issues**
   - Adjust width/height parameters
   - Modify video codec settings
   - Check network bandwidth

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will include stack traces in API error responses.

## Best Practices

1. **Always handle recording errors gracefully**
2. **Store egress IDs for later reference**
3. **Monitor recording status during long interviews**
4. **Clean up resources when interviews end**
5. **Test recording functionality in development environment**

## Integration with Existing Flow

The new egress recording system can be integrated alongside your existing recording mechanisms. You can:

1. Use egress recording as the primary method
2. Keep existing recording as a fallback
3. Run both systems in parallel for redundancy

Choose the approach that best fits your reliability requirements.
