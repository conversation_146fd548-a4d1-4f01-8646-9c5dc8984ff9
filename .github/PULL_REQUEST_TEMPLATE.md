### Pull Request Preparation

#### Branch Rebasing

- [ ] ✅ Branch rebased onto the base/original branch
- [ ] ❌ No

#### Code Building and Formatting

- [ ] ✅ Code successfully built (yarn build)
- [ ] ✅ Code formatted properly (yarn format)

---

### Pull Request Category

<!-- Please select the relevant category for this pull request: -->

- [ ] 🐛 Bugfix
- [ ] ✨ Feature
- [ ] 💡 Code Style Update (formatting, renaming)
- [ ] ♻️ Refactoring (no functional changes, no API changes)
- [ ] 🛠️ Build-Related Changes
- [ ] 📚 Documentation Content Changes
- [ ] 🚀 Performance

---

### 📎 Tracking Information

<!-- [Plane Issue No] -->

- **Issue/Task Id:**

---

### 📝 Description

<!-- Clearly describe the work incorporated in this pull request. -->

-

### 🧐 Root Cause

<!-- Briefly describe the root cause/analysis -->

-

### 🛠️ Solution

<!-- Please describe the behavior or changes that are being added by this PR. -->

- ***

### 💥 Potentially Affected Areas

<!-- Highlight any areas of the codebase that might be influenced by these changes. -->

- ***

### 🧪 Browser Testing

<!-- Specify where you've tested the code changes in the following browsers: -->

- [ ] 🌐 Chrome
- [ ] 🦊 Firefox
- [ ] 🌍 Edge
- [ ] 🍏 Safari
- [ ] 🦁 Brave
- [ ] 🤖 Android Chrome
- [ ] 📱 iPhone Safari

---

### 📌 Additional Information

<!-- Provide any other important information, such as screenshots showing the component before and after the change. -->

-
