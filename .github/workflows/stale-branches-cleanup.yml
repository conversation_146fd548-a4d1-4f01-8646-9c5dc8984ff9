name: Cleanup Stale Branches

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 1 * *'

jobs:
  cleanup-stale-branches:
    runs-on: ubuntu-latest
    steps:
      - name: Cleanup Stale Branches
        uses: cbrgm/cleanup-stale-branches-action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
          ignore-branches: 'main,development,release-*'
          ignore-prefixes: 'feature/,bugfix/'
          last-commit-age-days: 90
          dry-run: true
          rate-limit: true
