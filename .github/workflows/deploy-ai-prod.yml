name: Deploy AI PROD
on:
  workflow_dispatch:
    inputs:
      production:
        description: 'Set to true for production deployment, false for non-production'
        required: false
        default: 'false'

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: "!contains(github.event.head_commit.message, '[skip ci]')"
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16' # Use the required Node.js version

      - name: Install Vercel CLI
        run: npm install -g vercel

      - name: Deploy to Vercel Action
        uses: BetaHuhn/deploy-to-vercel-action@v1
        with:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_AI_PROJECT_ID }}
          VERCEL_SCOPE: ${{ secrets.VERCEL_SCOPE }}
