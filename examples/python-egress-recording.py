#!/usr/bin/env python3
"""
Example Python script for starting LiveKit egress recording
using the custom recording view page.

This script demonstrates how to:
1. Start a room composite egress recording
2. Use the custom recording view as the template
3. Monitor recording status
4. Stop the recording

Requirements:
pip install livekit-server-sdk
"""

import asyncio
import os
from urllib.parse import urlencode
from livekit import api

# Configuration
LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')
LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')
LIVEKIT_URL = os.getenv('LIVEKIT_URL')  # e.g., 'wss://your-livekit-server.com'

# Your app's base URL where the recording view is hosted
APP_BASE_URL = os.getenv('NEXT_PUBLIC_API_BASE_URL', 'http://localhost:3000')

# GCP Storage configuration (optional - can also be configured server-side)
GCP_BUCKET = os.getenv('NEXT_PUBLIC_GCP_BUCKET')
GCP_CREDENTIALS_JSON = {
    "type": "service_account",
    "project_id": os.getenv('NEXT_PUBLIC_GCP_PROJECT_ID'),
    "private_key": os.getenv('NEXT_PUBLIC_PRIVATE_KEY', '').replace('\\n', '\n'),
    "client_id": os.getenv('NEXT_PUBLIC_CLIENT_ID'),
    "client_email": os.getenv('NEXT_PUBLIC_CLIENT_EMAIL'),
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs"
}


async def create_recording_token(room_name: str, participant_identity: str = "recorder") -> str:
    """Create a token for the recording participant."""
    token = api.AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
    token.with_identity(participant_identity)
    token.with_name("Recording Bot")
    token.with_grants(api.VideoGrants(
        room_join=True,
        room=room_name,
        can_publish=True,
        can_subscribe=True,
        can_publish_data=True,
        recorder=True  # Special permission for recording
    ))
    return token.to_jwt()


def build_custom_base_url(room_name: str, layout: str = "speaker") -> str:
    """Build the custom base URL for the recording view."""
    # The recording view page will receive these parameters from LiveKit egress
    # LiveKit will automatically append: url, token, layout parameters
    base_url = f"{APP_BASE_URL}/lk-recording-view"
    
    # You can add custom parameters if needed
    params = {
        'room': room_name,
        # 'custom_param': 'value'  # Add any custom parameters here
    }
    
    if params:
        base_url += '?' + urlencode(params)
    
    return base_url


async def start_egress_recording(room_name: str, layout: str = "speaker") -> str:
    """Start a room composite egress recording."""
    
    # Initialize the egress client
    egress_client = api.EgressService(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
    
    # Build the custom recording URL
    custom_base_url = build_custom_base_url(room_name, layout)
    
    print(f"🎬 Starting egress recording for room: {room_name}")
    print(f"📺 Custom recording view: {custom_base_url}")
    
    # Configure the egress request
    request = api.RoomCompositeEgressRequest(
        room_name=room_name,
        layout=layout,
        custom_base_url=custom_base_url,
        
        # Video settings
        video_only=False,
        audio_only=False,
        
        # Advanced encoding options
        advanced=api.EncodingOptions(
            width=1920,
            height=1080,
            depth=24,
            framerate=30,
            audio_codec=api.AudioCodec.OPUS,
            video_codec=api.VideoCodec.H264_MAIN,
            key_frame_interval=2.0,
        ),
        
        # Output to GCP bucket
        file=api.GCPUpload(
            bucket=GCP_BUCKET,
            filepath=f"recordings/livekit-egress/{room_name}-{int(asyncio.get_event_loop().time())}.mp4",
            credentials=str(GCP_CREDENTIALS_JSON).replace("'", '"')  # Convert to JSON string
        )
    )
    
    # Start the recording
    egress_info = await egress_client.start_room_composite_egress(request)
    
    print(f"✅ Recording started!")
    print(f"   Egress ID: {egress_info.egress_id}")
    print(f"   Status: {egress_info.status}")
    print(f"   Room: {egress_info.room_name}")
    
    return egress_info.egress_id


async def stop_egress_recording(egress_id: str):
    """Stop an active egress recording."""
    
    egress_client = api.EgressService(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
    
    print(f"🛑 Stopping egress recording: {egress_id}")
    
    # Stop the recording
    egress_info = await egress_client.stop_egress(egress_id)
    
    print(f"✅ Recording stopped!")
    print(f"   Status: {egress_info.status}")
    print(f"   Duration: {egress_info.ended_at - egress_info.started_at if egress_info.ended_at else 'Unknown'}")
    
    return egress_info


async def get_egress_status(egress_id: str):
    """Get the status of an egress recording."""
    
    egress_client = api.EgressService(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
    
    # List egress with the specific ID
    egress_list = await egress_client.list_egress(egress_id=egress_id)
    
    if egress_list:
        egress_info = egress_list[0]
        print(f"📊 Egress Status: {egress_info.status}")
        print(f"   Room: {egress_info.room_name}")
        print(f"   Started: {egress_info.started_at}")
        if egress_info.ended_at:
            print(f"   Ended: {egress_info.ended_at}")
        return egress_info
    else:
        print(f"❌ No egress found with ID: {egress_id}")
        return None


async def main():
    """Example usage of the egress recording functions."""
    
    if not all([LIVEKIT_API_KEY, LIVEKIT_API_SECRET, LIVEKIT_URL]):
        print("❌ Missing required environment variables:")
        print("   LIVEKIT_API_KEY, LIVEKIT_API_SECRET, LIVEKIT_URL")
        return
    
    room_name = "interview-room-123"
    
    try:
        # Start recording
        egress_id = await start_egress_recording(room_name, layout="speaker")
        
        # Monitor status (optional)
        print("\n⏳ Waiting 10 seconds before checking status...")
        await asyncio.sleep(10)
        await get_egress_status(egress_id)
        
        # In a real scenario, you'd wait for the interview to complete
        print("\n⏳ Recording for 30 seconds (simulate interview)...")
        await asyncio.sleep(30)
        
        # Stop recording
        await stop_egress_recording(egress_id)
        
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())
