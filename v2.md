AcePrep Platform Enhancement: Generic Interview Framework

## Table of Contents
- [Overview](#overview)
- [Background](#background)
- [Core Requirements](#core-requirements)
  - [Database Schema Updates](#database-schema-updates)
  - [UI Component Updates](#ui-component-updates)
  - [Assessment Templates](#assessment-templates)
  - [Question Banks and AI Prompts](#question-banks-and-ai-prompts)
  - [UI Text and Label Updates](#ui-text-and-label-updates)
  - [Reporting and Analytics](#reporting-and-analytics)
- [Prompt Engineering Requirements](#prompt-engineering-requirements)
  - [Current Prompt Architecture](#current-prompt-architecture)
  - [Required Prompt Modifications](#required-prompt-modifications)
  - [Purpose-Specific Terminology Mapping](#purpose-specific-terminology-mapping)
- [Implementation Approach](#implementation-approach)
- [Testing Strategy](#testing-strategy)
  - [Regression Testing Plan](#regression-testing-plan)
- [Success Criteria](#success-criteria)
- [Future Enhancements](#future-enhancements)

## Overview

This document outlines the comprehensive requirements for enhancing the AcePrep platform from a hiring-focused interview system to a generic interview framework supporting multiple interview purposes, including admissions, visa readiness interviews, scholarships, and more.

## Background

AcePrep is currently designed primarily for technical hiring interviews. The platform includes features for:
- Configuring interview details
- Supporting various question types (coding, frontend, MCQ, custom)
- Evaluating candidates with customizable assessment criteria
- Conducting video interviews
- Managing user roles and organizations

To expand the platform's applicability, we need to make it purpose-agnostic while maintaining its powerful assessment capabilities.

## Core Requirements

### Database Schema Updates

#### 1.1 EventDetails Model Enhancement
- Add a new `interviewPurpose` field to distinguish between different types of interviews
- Rename hiring-specific fields to be more generic (e.g., `jobDescription` to `description`)
- Make role-specific fields optional or adaptable

```prisma
model EventDetails {
  id                       String                     @id @default(cuid())
  organizationId           String
  role                     String
  level                    String
  description              String?                    // Renamed from jobDescription to be more generic
  skillsAndKeywords        String[]
  isProctor                Boolean?                   @default(true)
  name                     String?
  questions                Json?
  round                    String?
  evaluation               String?                    @default("hard")
  timing                   Json?
  interviewPurpose         InterviewPurpose           @default(HIRING) // New field
  employment_type          String?
  references_id            String?
  isPlacement              Boolean?                   @default(false)
  isAiQuestion             Boolean?                   @default(true)
  customTopics             String?
  assessmentCriteria       Json?                      
  assessmentTemplateId     String?                    
  assessmentTemplate       AssessmentTemplate?        @relation(fields: [assessmentTemplateId], references: [id])
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime?                  @updatedAt
  organization             Organization               @relation(fields: [organizationId], references: [id])
  careerPractices          CareerPractice[]
  status                   InterviewStatus            @default(ACTIVE)
  aiQuestionCount          Int                        @default(0)
  MembershipOnEventDetails MembershipOnEventDetails[]
  createdById              String?
  createdBy                User?                      @relation(fields: [createdById], references: [id])
  VideoCallInterview       VideoCallInterview[]

  @@map("event_details")
}
```

#### 1.2 New Enum for Interview Purposes
Create a new enum `InterviewPurpose` with the following options:
```prisma
enum InterviewPurpose {
  HIRING
  ADMISSIONS
  VISA
  SCHOLARSHIP
  PROMOTION
  PERFORMANCE_REVIEW
  CUSTOM
}
```

#### 1.3 Schema Migration
- Create necessary database migrations
- Ensure backward compatibility with existing data
- Add indexes for improved query performance on new fields

### UI Component Updates

#### 2.1 Interview Creation Flow
- Add a new first step for selecting interview purpose
- Make subsequent steps adapt based on the selected purpose
- Update field labels and descriptions dynamically

```tsx
// Updated CreateEventScreen component flow
const steps = [
  {
    title: 'Select Interview Purpose',
    component: (
      <InterviewPurposeSelector 
        purpose={interviewPurpose} 
        setPurpose={setInterviewPurpose} 
      />
    ),
    validatePage: () => true, // Always valid as long as a purpose is selected
  },
  {
    title: getPurposeSpecificLabel(interviewPurpose, 'roleDetailsTitle'),
    component: (
      <AddRoleDetails 
        eventId={eventId} 
        level={level} 
        isInterviewthon={isInterviewthon} 
        purpose={interviewPurpose}
      />
    ),
    // Rest of the component...
```

#### 2.2 New Interview Purpose Selector Component
Create a radio group component for selecting the interview purpose:

```tsx
// Components for purpose selection
export const InterviewPurposeSelector = ({ purpose, setPurpose }) => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Select Interview Purpose</h2>
        <p className="text-muted-foreground">Choose the type of interview you want to conduct.</p>
      </div>
      
      <RadioGroup value={purpose} onValueChange={setPurpose} className="space-y-4">
        {purposeOptions.map((option) => (
          <div key={option.id} className="flex items-start space-x-3 p-4 border rounded-md hover:bg-accent">
            <RadioGroupItem value={option.id} id={option.id} />
            <div className="space-y-1">
              <Label htmlFor={option.id} className="font-medium">{option.label}</Label>
              <p className="text-sm text-muted-foreground">{option.description}</p>
            </div>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

// Purpose options configuration
const purposeOptions = [
  { id: 'HIRING', label: 'Hiring Interview', description: 'For recruiting and evaluating job candidates' },
  { id: 'ADMISSIONS', label: 'Admissions Interview', description: 'For educational institution admission process' },
  { id: 'VISA', label: 'Visa Readiness Interview', description: 'For preparing candidates for visa interviews' },
  { id: 'SCHOLARSHIP', label: 'Scholarship Interview', description: 'For evaluating scholarship applicants' },
  { id: 'PROMOTION', label: 'Promotion Interview', description: 'For internal promotion evaluation' },
  { id: 'PERFORMANCE_REVIEW', label: 'Performance Review', description: 'For employee performance assessment' },
  { id: 'CUSTOM', label: 'Custom Interview', description: 'For other interview purposes' },
];
```

#### 2.3 Dynamic Form Field Labels
- Create a utility function to return purpose-specific labels for form fields:

```tsx
// Utility for purpose-specific labels
export const getPurposeSpecificLabel = (purpose, fieldKey) => {
  const labelMap = {
    roleDetailsTitle: {
      HIRING: 'Add Role Details',
      ADMISSIONS: 'Add Program Details',
      VISA: 'Add Visa Interview Details',
      SCHOLARSHIP: 'Add Scholarship Details',
      PROMOTION: 'Add Promotion Details',
      PERFORMANCE_REVIEW: 'Add Review Details',
      CUSTOM: 'Add Interview Details',
    },
    roleLabel: {
      HIRING: 'Role/Position',
      ADMISSIONS: 'Program/Course',
      VISA: 'Visa Type',
      SCHOLARSHIP: 'Scholarship Program',
      PROMOTION: 'Target Position',
      PERFORMANCE_REVIEW: 'Current Position',
      CUSTOM: 'Interview Title',
    },
    // Add more field mappings as needed
  };
  
  return labelMap[fieldKey]?.[purpose] || labelMap[fieldKey]?.CUSTOM || 'Label';
};
```

#### 2.4 Assessment Criteria Adaptability
- Update assessment criteria forms to show purpose-relevant options
- Pre-populate with suggested criteria based on the interview purpose

### Assessment Templates

#### 3.1 Purpose-Specific Templates
Create default assessment templates for each interview purpose:

**Admissions Assessment Template:**
```json
{
  "name": "Standard Admissions Assessment",
  "description": "Standard criteria for evaluating admission candidates",
  "roleType": "CUSTOM",
  "criteria": [
    {
      "name": "Academic Potential",
      "description": "Candidate's demonstrated academic ability and potential for success",
      "scaleType": "NUMERIC",
      "maxValue": 5,
      "weight": 30
    },
    {
      "name": "Communication Skills",
      "description": "Ability to articulate thoughts clearly and engage in meaningful discussion",
      "scaleType": "NUMERIC",
      "maxValue": 5,
      "weight": 20
    },
    {
      "name": "Critical Thinking",
      "description": "Ability to analyze problems and think logically",
      "scaleType": "NUMERIC",
      "maxValue": 5,
      "weight": 20
    },
    {
      "name": "Program Fit",
      "description": "Alignment between candidate's goals and program offerings",
      "scaleType": "NUMERIC",
      "maxValue": 5,
      "weight": 20
    },
    {
      "name": "Personal Qualities",
      "description": "Character, motivation, and other relevant personal attributes",
      "scaleType": "NUMERIC",
      "maxValue": 5,
      "weight": 10
    }
  ]
}
```

**Visa Interview Readiness Template:**
```json
{
  "name": "Visa Interview Readiness",
  "description": "Criteria for evaluating visa interview preparedness",
  "roleType": "CUSTOM",
  "criteria": [
    {
      "name": "Documentation Knowledge",
      "description": "Understanding of required documentation and procedures",
      "scaleType": "NUMERIC",
      "maxValue": 5,
      "weight": 25
    },
    {
      "name": "Purpose Clarity",
      "description": "Ability to clearly articulate travel purpose and intentions",
      "scaleType": "NUMERIC",
      "maxValue": 5,
      "weight": 25
    },
    {
      "name": "Financial Preparedness",
      "description": "Demonstration of financial stability and planning",
      "scaleType": "NUMERIC",
      "maxValue": 5,
      "weight": 20
    },
    {
      "name": "Return Intent Evidence",
      "description": "Ability to demonstrate ties to home country and intent to return",
      "scaleType": "NUMERIC",
      "maxValue": 5,
      "weight": 20
    },
    {
      "name": "Communication Under Pressure",
      "description": "Ability to remain composed and communicate clearly",
      "scaleType": "NUMERIC",
      "maxValue": 5,
      "weight": 10
    }
  ]
}
```

#### 3.2 Custom Template Builder
- Allow organizations to create and save custom templates for each purpose
- Enable template sharing across the organization

### Question Banks and AI Prompts

#### 4.1 Purpose-Specific Question Banks
Develop question banks tailored to each interview purpose:

**Admissions Questions:**
- Educational background questions
- Career goal questions
- Program fit questions
- Personal motivation questions

**Visa Interview Questions:**
- Travel purpose questions
- Financial status questions
- Return intention questions
- Background verification questions

#### 4.2 AI Question Generation Updates
- Modify AI prompts to generate questions appropriate to the interview purpose
- Update question evaluation algorithms to match purpose-specific criteria

### UI Text and Label Updates

#### 5.1 Purpose-Neutral Terminology
- Replace hiring-specific terminology throughout the UI
- Update tooltips and help text to be purpose-agnostic

#### 5.2 Dynamic UI Elements
- Create a system for dynamically changing UI elements based on purpose
- Include purpose-specific iconography and visual cues

#### 5.3 Email Templates
- Update notification and reminder email templates to be purpose-appropriate
- Add customization options for organizations to tailor messaging

### Reporting and Analytics

#### 6.1 Purpose-Specific Insights
- Adapt analytics dashboards to show purpose-relevant metrics
- Create comparison views across different interview purposes

#### 6.2 Custom Reports
- Allow organizations to create custom reports for each interview purpose
- Enable cross-purpose analysis for comprehensive insights

## Prompt Engineering Requirements

### Current Prompt Architecture

The AcePrep platform uses a strategy pattern for prompt management:
- `prompt-service/index.ts` provides a central `generatePrompt` function
- `prompt-service/strategy-mapping.ts` maps strategy names to implementation functions
- Strategy implementations are organized by use case in `prompt-service/strategies/`
- Prompt templates are stored in `prompt-service/constants/`

### Required Prompt Modifications

#### 1. Video Question Generation

**Current Implementation:**
```typescript
export const VIDEO_QUESTION_PROMPT_STRATEGY = `Create 10 interview questions for an AI interview round for a given role. Role: {role}, level: {level} and description:{jobDescription}. output should follow the json format below {{"questions":[{{id:id should be unique v4 uuid,"question":"question1","description":"question1 description"}},{{id:id should be unique v4 uuid,"question":"question2","description":"question2 description"}}]}} `;
```

**Required Modifications:**
```typescript
export const VIDEO_QUESTION_PROMPT_STRATEGY = `Create 10 interview questions for an AI interview round for a {interviewPurpose} interview. 
${
  interviewPurpose === 'HIRING' 
    ? 'Role: {role}, level: {level} and description: {description}' 
    : interviewPurpose === 'ADMISSIONS' 
      ? 'Program: {role}, education level: {level} and description: {description}'
      : interviewPurpose === 'VISA'
        ? 'Visa type: {role}, travel purpose: {level} and context: {description}'
        : 'Title: {role}, level: {level} and description: {description}'
}. 
output should follow the json format below {{"questions":[{{id:id should be unique v4 uuid,"question":"question1","description":"question1 description"}},{{id:id should be unique v4 uuid,"question":"question2","description":"question2 description"}}]}} `;
```

#### 2. Assessment Prompts

Update the `generateDynamicAssessmentPrompt` function to include purpose context:

```typescript
export const generateDynamicAssessmentPrompt = (input: DynamicAssessmentInput): string => {
  const {
    role,
    level,
    candidateName,
    description, // Renamed from jobDescription
    requiredSkills,
    coreValues,
    assessmentCriteria,
    assessmentTemplate,
    interviewPurpose = 'HIRING', // Default to HIRING for backward compatibility
  } = input;

  // Get purpose-specific terminology
  const terminology = getPurposeTerminology(interviewPurpose);
  
  // Use custom criteria or template criteria
  const criteria = assessmentCriteria || assessmentTemplate?.criteria || [];

  // Generate criteria-specific instructions
  const criteriaInstructions = criteria.map((criterion, index) => {
    const scaleDescription = generateScaleDescription(criterion);
    return `${index + 1}. **${criterion.name}**
   - Description: ${criterion.description}
   - Scale: ${scaleDescription}
   - Category: ${criterion.category}`;
  }).join('\n\n');

  return `You are a senior AI interviewer and ${terminology.expertRole} conducting a comprehensive assessment of this AI-powered interview recording. Your primary objective is to rigorously evaluate whether the ${terminology.candidateType} is a good fit for the ${role} ${terminology.positionType} at ${level} level.

Context:
- ${terminology.roleLabel}: ${role}
- ${terminology.levelLabel}: ${level}
- ${terminology.candidateLabel}: ${candidateName}
- ${terminology.descriptionLabel}: ${description}
- ${terminology.skillsLabel}: ${requiredSkills}
- Organization Core Values: ${coreValues}

This is an AI-conducted ${terminology.interviewType}. Analyze the ${terminology.candidateType}'s responses, communication style, ${terminology.knowledgeType}, and overall presentation.
`;
};

// Helper function to get purpose-specific terminology
function getPurposeTerminology(purpose) {
  const terminologyMap = {
    'HIRING': {
      expertRole: 'hiring expert',
      candidateType: 'candidate',
      candidateLabel: 'Candidate',
      positionType: 'position',
      roleLabel: 'Role',
      levelLabel: 'Experience Level',
      descriptionLabel: 'Job Description',
      skillsLabel: 'Required Skills',
      knowledgeType: 'technical knowledge',
      interviewType: 'job interview'
    },
    'ADMISSIONS': {
      expertRole: 'admissions officer',
      candidateType: 'applicant',
      candidateLabel: 'Applicant',
      positionType: 'program',
      roleLabel: 'Program',
      levelLabel: 'Education Level',
      descriptionLabel: 'Program Description',
      skillsLabel: 'Required Qualifications',
      knowledgeType: 'academic potential',
      interviewType: 'admissions interview'
    },
    'VISA': {
      expertRole: 'visa officer',
      candidateType: 'applicant',
      candidateLabel: 'Applicant',
      positionType: 'visa application',
      roleLabel: 'Visa Type',
      levelLabel: 'Travel Purpose',
      descriptionLabel: 'Travel Context',
      skillsLabel: 'Required Documentation',
      knowledgeType: 'visa requirements understanding',
      interviewType: 'visa preparation interview'
    },
    // Add other purpose types...
  };
  
  return terminologyMap[purpose] || terminologyMap['HIRING'];
}
```

#### 3. New Purpose-Specific Prompt Strategies

Create new strategies for different interview purposes:

```typescript
// Example: Admissions question prompt strategy
export const ADMISSIONS_QUESTION_PROMPT_STRATEGY = `Create 10 interview questions for an admissions interview for the program: {role}, education level: {level} and program description: {description}. 

The questions should evaluate the applicant's:
1. Academic background and preparation
2. Interest and understanding of the program
3. Career goals and how they align with the program
4. Personal qualities and fit with the institution
5. Critical thinking and problem-solving abilities

output should follow the json format below {{"questions":[{{id:id should be unique v4 uuid,"question":"question1","description":"question1 description"}},{{id:id should be unique v4 uuid,"question":"question2","description":"question2 description"}}]}} `;
```

```typescript
// Example: Visa interview question prompt strategy
export const VISA_QUESTION_PROMPT_STRATEGY = `Create 10 realistic visa interview questions for a {role} visa application with travel purpose: {level} and context: {description}. 

The questions should focus on:
1. Purpose of travel and trip details
2. Ties to home country and return intentions
3. Financial arrangements and sponsorship
4. Background and previous travel history
5. Knowledge of visa requirements and procedures

Output should follow the json format below {{"questions":[{{id:id should be unique v4 uuid,"question":"question1","description":"question1 description"}},{{id:id should be unique v4 uuid,"question":"question2","description":"question2 description"}}]}} `;
```

#### 4. Prompt Strategy Mapping Updates

Update `strategy-mapping.ts` to include new purpose-specific strategies:

```typescript
export const strategyMap = {
  // Existing strategies...
  
  // New purpose-specific strategies
  ADMISSIONS_QUESTION_PROMPT_STRATEGY: AdmissionsQuestionStrategy,
  ADMISSIONS_ASSESSMENT_PROMPT_STRATEGY: AdmissionsAssessmentStrategy,
  VISA_QUESTION_PROMPT_STRATEGY: VisaQuestionStrategy,
  VISA_ASSESSMENT_PROMPT_STRATEGY: VisaAssessmentStrategy,
  SCHOLARSHIP_QUESTION_PROMPT_STRATEGY: ScholarshipQuestionStrategy,
  SCHOLARSHIP_ASSESSMENT_PROMPT_STRATEGY: ScholarshipAssessmentStrategy,
  CUSTOM_INTERVIEW_PROMPT_STRATEGY: CustomInterviewStrategy,
  CUSTOM_ASSESSMENT_PROMPT_STRATEGY: CustomAssessmentStrategy,
  
  // Purpose-aware versions of existing strategies
  PURPOSE_AWARE_VIDEO_QUESTION_PROMPT_STRATEGY: PurposeAwareVideoQuestionStrategy,
  PURPOSE_AWARE_ASSESSMENT_PROMPT_STRATEGY: PurposeAwareAssessmentStrategy,
};
```

### Purpose-Specific Terminology Mapping

To ensure consistent terminology across different interview purposes:

| Term | Hiring | Admissions | Visa | Scholarship |
|------|--------|------------|------|-------------|
| Person | Candidate | Applicant | Applicant | Applicant |
| Role | Position/Role | Program/Course | Visa Type | Scholarship Program |
| Level | Experience Level | Education Level | Purpose of Travel | Academic Level |
| Description | Job Description | Program Description | Travel Context | Scholarship Details |
| Skills | Required Skills | Required Qualifications | Required Documentation | Eligibility Criteria |
| Evaluation | Hiring Assessment | Admissions Assessment | Visa Readiness Assessment | Scholarship Worthiness Assessment |

## Implementation Approach

### Phase 1: Database and Core UI Updates
1. Update database schema with new fields and enums
2. Create database migrations
3. Develop interview purpose selector component
4. Modify the interview creation flow to include purpose selection
5. Update form fields to dynamically adapt to the selected purpose

### Phase 2: Assessment and Question Updates
1. Create purpose-specific assessment templates
2. Develop purpose-specific question banks
3. Update AI question generation prompts
4. Modify assessment evaluation logic to match purpose-specific criteria

### Phase 3: UI Refinement and Testing
1. Update all UI text and labels to be purpose-neutral or dynamic
2. Create purpose-specific email templates
3. Implement purpose-specific iconography and visual cues
4. Conduct thorough testing for each interview purpose

### Phase 4: Analytics and Reporting
1. Update analytics dashboards with purpose-specific metrics
2. Develop custom reporting capabilities for each purpose
3. Create cross-purpose analysis tools

## Testing Strategy

### Regression Testing Plan

#### 1. Baseline Testing
- Create a set of baseline interview recordings and transcripts
- Generate assessments using current prompts
- Store results for comparison

#### 2. Prompt Validation Testing
- For each modified prompt:
  - Generate sample outputs
  - Verify format compliance
  - Check for terminology consistency
  - Ensure all required fields are present

#### 3. Comparative Testing
- For each baseline interview:
  - Generate new assessments with updated prompts
  - Compare assessment quality and comprehensiveness
  - Verify no critical information is lost
  - Ensure feedback quality remains high

#### 4. Purpose-Specific Testing
- Create test cases for each new interview purpose
- Validate that generated questions are appropriate for the purpose
- Verify assessment criteria alignment with purpose goals
- Test edge cases with minimal input information

#### 5. Performance Testing
- Measure token usage for each prompt variant
- Ensure prompt size remains within API limits
- Verify response times are acceptable
- Test with different model versions if applicable

#### 6. UI Flow Testing
- Test the complete interview creation flow for each purpose
- Verify purpose-specific labels and terminology
- Test all dynamic UI elements
- Ensure proper validation for each purpose

#### 7. Data Migration Testing
- Test backwards compatibility with existing interviews
- Verify proper handling of null/undefined purpose values
- Test search and filtering with the new purpose field

## Success Criteria

The enhanced platform will be considered successful when:

1. Organizations can successfully create and conduct interviews for all supported purposes
2. User feedback indicates that the UI and terminology are appropriate for each purpose
3. Assessment results accurately reflect purpose-specific evaluation criteria
4. Analytics provide meaningful insights for each interview purpose
5. The system can be easily extended to support additional interview purposes in the future
6. All AI-generated content is appropriate and relevant for each interview purpose
7. No regression in functionality for existing hiring interviews



### Advanced Features
- Purpose-specific AI coaching for interviewees
- Multi-purpose interview sequences
- Custom workflow builders for each purpose
- Integration with purpose-specific external systems