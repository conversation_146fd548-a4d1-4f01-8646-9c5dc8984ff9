'use client';

import { useEffect, useState, useRef } from 'react';
import SimpleVoiceAssistant from '@/components/SimpleVoiceAssistant';
import useBeforeUnload from '@/hooks/client/useBeforeUnload';
import { AppLogo } from '@/layout/logo';
import { RoomContext } from '@livekit/components-react';
import '@livekit/components-styles';
import { LocalAudioTrack, Room, RoomEvent, TrackPublication, Track } from 'livekit-client';
import { createEgressManager, LiveKitEgressManager } from '@/utils/livekit-egress-manager';
import { Button } from '@camped-ui/button';

const URL = process.env.NEXT_PUBLIC_LIVEKIT_URL;

if (!URL) {
  throw new Error('Missing LiveKit URL');
}

interface InterviewData {
  role?: string;
  level?: string;
  event?: string;
  jobDescription?: string;
}

interface EnhancedAIInterviewProps {
  token: string;
  roomName: string;
  interviewData?: InterviewData;
  enableEgressRecording?: boolean;
  onRecordingStarted?: (egressId: string) => void;
  onRecordingStopped?: (egressId: string) => void;
  onRecordingError?: (error: string) => void;
}

export default function EnhancedAIInterview({
  token,
  roomName,
  interviewData,
  enableEgressRecording = false,
  onRecordingStarted,
  onRecordingStopped,
  onRecordingError,
}: EnhancedAIInterviewProps) {
  const [room] = useState(() => new Room());
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);
  
  // Egress recording state
  const [isRecording, setIsRecording] = useState(false);
  const [recordingStatus, setRecordingStatus] = useState<string>('');
  const egressManagerRef = useRef<LiveKitEgressManager | null>(null);

  useBeforeUnload();

  useEffect(() => {
    // Initialize egress manager if recording is enabled
    if (enableEgressRecording) {
      egressManagerRef.current = createEgressManager(roomName, {
        layout: 'speaker',
        width: 1920,
        height: 1080,
        videoCodec: 'H264_MAIN',
      });
    }

    const handleDeviceError = (error: Error) => {
      console.error('Media device error:', error);
      setError(`Device error: ${error.message}`);
    };

    room.on(RoomEvent.MediaDevicesError, handleDeviceError);

    // Handle microphone track with noise filtering
    room.on(RoomEvent.LocalTrackPublished, async (trackPublication) => {
      if (trackPublication.source === Track.Source.Microphone && trackPublication.track instanceof LocalAudioTrack) {
        const { KrispNoiseFilter, isKrispNoiseFilterSupported } = await import('@livekit/krisp-noise-filter');
        if (!isKrispNoiseFilterSupported()) {
          console.log('Krisp noise filter is not supported');
          return;
        }
        try {
          await trackPublication.track?.setProcessor(KrispNoiseFilter());
          console.log('Krisp noise filter applied');
        } catch (e) {
          console.warn("Background Noise reduction cannot be enabled");
        }
      }
    });

    onConnectButtonClicked();

    return () => {
      room.off(RoomEvent.MediaDevicesError, handleDeviceError);
      if (isConnected) {
        room.disconnect();
      }
      // Stop recording if active
      if (egressManagerRef.current?.isRecording()) {
        stopRecording();
      }
    };
  }, [room, isConnected, roomName, enableEgressRecording]);

  const onConnectButtonClicked = async () => {
    try {
      await room.connect(URL, token, { autoSubscribe: true });
      await Promise.all([
        room.localParticipant.setMicrophoneEnabled(true),
        room.localParticipant.setCameraEnabled(true),
      ]);
      setIsConnected(true);
      setStartTime(new Date());
      
      // Auto-start recording if enabled
      if (enableEgressRecording && egressManagerRef.current) {
        setTimeout(() => {
          startRecording();
        }, 3000); // Wait 3 seconds for connection to stabilize
      }
    } catch (err) {
      console.error('Connection failed:', err);
      setError('Failed to join the interview room');
    }
  };

  const startRecording = async () => {
    if (!egressManagerRef.current) {
      console.error('Egress manager not initialized');
      return;
    }

    try {
      setRecordingStatus('Starting recording...');
      const result = await egressManagerRef.current.startRecording();
      
      if (result.success && result.egressId) {
        setIsRecording(true);
        setRecordingStatus('Recording active');
        onRecordingStarted?.(result.egressId);
        console.log('🎬 Egress recording started:', result.egressId);
      } else {
        throw new Error(result.error || 'Failed to start recording');
      }
    } catch (error) {
      console.error('❌ Failed to start recording:', error);
      setRecordingStatus('Recording failed');
      onRecordingError?.(error.message);
    }
  };

  const stopRecording = async () => {
    if (!egressManagerRef.current) {
      console.error('Egress manager not initialized');
      return;
    }

    try {
      setRecordingStatus('Stopping recording...');
      const egressId = egressManagerRef.current.getEgressId();
      const result = await egressManagerRef.current.stopRecording();
      
      if (result.success) {
        setIsRecording(false);
        setRecordingStatus('Recording stopped');
        if (egressId) {
          onRecordingStopped?.(egressId);
        }
        console.log('🛑 Egress recording stopped');
      } else {
        throw new Error(result.error || 'Failed to stop recording');
      }
    } catch (error) {
      console.error('❌ Failed to stop recording:', error);
      setRecordingStatus('Stop recording failed');
      onRecordingError?.(error.message);
    }
  };

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="rounded-lg bg-red-50 p-4 text-lg text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <main data-lk-theme="default" className="flex h-screen flex-col bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Interview Header with Recording Status */}
      <div className="border-b border-white/20 backdrop-blur-sm bg-white/80 dark:bg-slate-900/80">
        <div className="flex w-full items-center justify-between px-4 py-2 sm:py-4">
          <AppLogo />
          
          {/* Recording Status and Controls */}
          {enableEgressRecording && (
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-400'}`} />
                <span className="text-sm font-medium">
                  {recordingStatus || (isRecording ? 'Recording' : 'Not Recording')}
                </span>
              </div>
              
              <div className="flex gap-2">
                {!isRecording ? (
                  <Button
                    onClick={startRecording}
                    size="sm"
                    className="bg-red-600 hover:bg-red-700 text-white"
                    disabled={!isConnected}
                  >
                    Start Recording
                  </Button>
                ) : (
                  <Button
                    onClick={stopRecording}
                    size="sm"
                    variant="outline"
                    className="border-red-600 text-red-600 hover:bg-red-50"
                  >
                    Stop Recording
                  </Button>
                )}
              </div>
            </div>
          )}
          
          {/* Interview Info */}
          <div className="text-right">
            <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
              {interviewData?.role || 'AI Interview'}
            </div>
            <div className="text-xs text-slate-500 dark:text-slate-400">
              {interviewData?.level || 'Assessment'}
            </div>
          </div>
        </div>
      </div>

      {/* Main Interview Content */}
      <div className="flex-1 overflow-hidden">
        <RoomContext.Provider value={room}>
          <SimpleVoiceAssistant
            onConnectButtonClicked={onConnectButtonClicked}
            onCompleteHref="/interview-complete"
            interviewId={roomName}
            interviewData={interviewData}
          />
        </RoomContext.Provider>
      </div>
    </main>
  );
}
