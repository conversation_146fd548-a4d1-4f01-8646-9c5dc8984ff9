'use client';

import { useContext, useEffect, useRef, useState } from 'react';
import * as React from 'react';
import { useRouter } from 'next/navigation';
import { CloseIcon } from '@/components/CloseIcon';
import { NoAgentNotification } from '@/components/NoAgentNotification';
import { EnhancedProctoringDashboard } from '@/components/proctoring/enhanced-proctoring-dashboard';
import { useProctoring } from '@/hooks/client/useProctoring';
import useBeforeUnload from '@/hooks/client/useBeforeUnload';
import { updateRecordProctor } from '@/services/apicall';
import { Button } from '@camped-ui/button';
import { Icon } from '@/icons';
import {
  BarVisualizer,
  DisconnectButton,
  RoomAudioRenderer,
  RoomContext,
  TrackReferenceOrPlaceholder,
  VideoTrack,
  VoiceAssistantControlBar,
  useLocalParticipant,
  useTrackTranscription,
  useTracks,
  useVoiceAssistant,
} from '@livekit/components-react';
import { AnimatePresence, motion } from 'framer-motion';
import { RoomEvent, Track } from 'livekit-client';

// Timer Component
function InterviewTimer({ startTime }: { startTime: Date | null }) {
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    if (!startTime) {
      console.log('Timer: No start time set');
      return;
    }

    console.log('Timer: Starting timer with start time:', startTime);
    const interval = setInterval(() => {
      const now = new Date();
      const elapsed = Math.floor((now.getTime() - startTime.getTime()) / 1000);
      setElapsedTime(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Always show timer, even if no start time (for debugging)
  return (
    <div className="flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 px-4 py-2 rounded-lg border border-blue-200 dark:border-blue-800 shadow-lg">
      <div className="h-2 w-2 rounded-full bg-red-500 animate-pulse"></div>
      <span className="text-sm font-mono font-semibold text-blue-700 dark:text-blue-300">
        {startTime ? formatTime(elapsedTime) : '00:00'}
      </span>
     
    </div>
  );
}

interface InterviewData {
  role?: string;
  level?: string;
  event?: string;
  jobDescription?: string;
}

export default function SimpleVoiceAssistant(props: {
  onConnectButtonClicked: () => void;
  onCompleteHref: string;
  interviewId?: string;
  interviewData?: InterviewData;
}) {
  const { state: agentState } = useVoiceAssistant();
  const screenTracks = useTracks([Track.Source.ScreenShare]);

  // Proctoring setup
  const isProctoringEnabled = true; // Enable proctoring for AI interviews
  const [proctoring, setProctoring] = useState<any>({
    fullScreen: 0,
    tabSwitch: 0,
    eyeTracking: null,
    emotionAnalysis: null,
    objectDetection: null,
    multiPerson: null,
    voiceAnalysis: null,
    timestamp: Date.now(),
  });
  const [showProctoringDashboard, setShowProctoringDashboard] = useState(false);
  const [isDashboardMinimized, setIsDashboardMinimized] = useState(false);
  const [interviewStartTime, setInterviewStartTime] = useState<Date | null>(null);

  const proctoringData = useProctoring({
    forceFullScreen: isProctoringEnabled,
    preventTabSwitch: isProctoringEnabled,
    preventContextMenu: isProctoringEnabled,
    preventUserSelection: isProctoringEnabled,
    preventCopy: isProctoringEnabled,
  });

  const { fullScreen, tabFocus } = proctoringData;

  // Auto-trigger fullscreen for proctoring
  useEffect(() => {
    if (isProctoringEnabled && fullScreen.status === 'off') {
      fullScreen.trigger();
    }
  }, [isProctoringEnabled, fullScreen.status]);

  // Start timer when interview begins
  useEffect(() => {
    console.log('Agent state changed:', agentState, 'Start time:', interviewStartTime);
    if ((agentState === 'listening' || agentState === 'speaking') && !interviewStartTime) {
      console.log('Starting interview timer');
      setInterviewStartTime(new Date());
    }
  }, [agentState, interviewStartTime]);

  // Save proctoring data before page unload
  useBeforeUnload(() => {
    if (props.interviewId) {
      updateRecordProctor({ id: props.interviewId, data: proctoring });
    }
  });

  // Periodic save of enhanced proctoring data
  useEffect(() => {
    if (!isProctoringEnabled || !props.interviewId) return;

    const saveInterval = setInterval(() => {
      // Update proctoring state with latest data
      setProctoring((prevProctoring) => {
        const updatedProctoring = {
          ...prevProctoring,
          eyeTracking: proctoringData.eyeTracking,
          emotionAnalysis: proctoringData.emotionAnalysis,
          objectDetection: proctoringData.objectDetection,
          multiPerson: proctoringData.multiPerson,
          voiceAnalysis: proctoringData.voiceAnalysis,
          fullScreen: fullScreen.status === 'off' ? (prevProctoring?.fullScreen ?? 0) + 1 : (prevProctoring?.fullScreen ?? 0),
          tabSwitch: !tabFocus.status ? (prevProctoring?.tabSwitch ?? 0) + 1 : (prevProctoring?.tabSwitch ?? 0),
          timestamp: Date.now(),
        };

        // Save to database
        console.log('💾 AI Interview - Saving proctoring data to database:', updatedProctoring);
        updateRecordProctor({ id: props.interviewId, data: updatedProctoring }).catch(console.error);

        return updatedProctoring;
      });
    }, 10000); // Save every 10 seconds

    return () => clearInterval(saveInterval);
  }, [isProctoringEnabled, props.interviewId, proctoringData, fullScreen.status, tabFocus.status]);

  // Enhanced proctoring violation handler
  const handleProctoringViolation = React.useCallback((violationType: string, severity: 'low' | 'medium' | 'high' | 'critical') => {
    console.warn(`🚨 AI Interview - Proctoring violation: ${violationType} (${severity})`);

    // Update proctoring data based on violation type
    setProctoring((prevProctoring) => {
      const updatedProctoring = {
        ...prevProctoring,
        eyeTracking: proctoringData.eyeTracking,
        emotionAnalysis: proctoringData.emotionAnalysis,
        objectDetection: proctoringData.objectDetection,
        multiPerson: proctoringData.multiPerson,
        voiceAnalysis: proctoringData.voiceAnalysis,
        fullScreen: fullScreen.status === 'off' ? (prevProctoring?.fullScreen ?? 0) + 1 : (prevProctoring?.fullScreen ?? 0),
        tabSwitch: !tabFocus.status ? (prevProctoring?.tabSwitch ?? 0) + 1 : (prevProctoring?.tabSwitch ?? 0),
        timestamp: Date.now(),
      };

      // Save immediately on violation
      if (props.interviewId) {
        updateRecordProctor({ id: props.interviewId, data: updatedProctoring }).catch(console.error);
      }

      return updatedProctoring;
    });
  }, [proctoringData, props.interviewId, fullScreen.status, tabFocus.status]);

  // Note: elapsed state and timer removed as it's not being used in the UI

  return (
    <>
      <AnimatePresence mode="wait">
        {agentState === 'disconnected' ? (
          <motion.div
            key="disconnected"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col items-center justify-center space-y-8 p-8"
          >
            {/* Welcome Card */}
            {/* <div className="max-w-2xl text-center space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-center">
                  <div className="h-16 w-16 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center">
                    <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                </div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Ready to Start Your Interview?
                </h2>
                {props.interviewData?.role && (
                  <div className="space-y-2">
                    <p className="text-lg text-gray-600 dark:text-gray-300">
                      Position: <span className="font-semibold text-blue-600 dark:text-blue-400">{props.interviewData.role}</span>
                    </p>
                    {props.interviewData.level && (
                      <p className="text-md text-gray-500 dark:text-gray-400">
                        Level: {props.interviewData.level}
                      </p>
                    )}
                  </div>
                )}
                <p className="text-gray-600 dark:text-gray-300 max-w-lg mx-auto">
                  Click the button below to begin your AI-powered interview. Make sure your camera and microphone are ready.
                </p>
              </div>
            </div>

            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="group relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-4 text-white font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              onClick={() => props.onConnectButtonClicked()}
            >
              <span className="relative z-10 flex items-center gap-3">
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Start Interview
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </motion.button> */}
          </motion.div>
        ) : (
          <motion.div
            key="connected"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="flex h-screen flex-col items-center gap-4 p-4 pr-[400px]"
          >
            {/* Interview Timer */}
            <div className="w-full max-w-5xl flex justify-center">
              <InterviewTimer startTime={interviewStartTime} />
            </div>

            {/* Video Section */}
            <div className="flex w-full max-w-5xl items-center justify-center gap-2 lg:flex-row my-2">
              <div className="flex flex-col items-center gap-3">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Flinkk AI Interviewer</h3>
                <AgentVisualizer />
              </div>

              <div className="flex flex-col items-center gap-3">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">You</h3>
                <div className="flex flex-col gap-3">
                  <LocalParticipantVideo />
                  {screenTracks.length > 0 && (
                    <div className="relative h-[200px] w-[300px] overflow-hidden rounded-xl shadow-lg">
                      {React.createElement(VideoTrack as any, {
                        trackRef: screenTracks[0],
                        className: 'w-full h-full object-contain',
                      })}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Controls */}
            <div className="w-full max-w-5xl">
              <ControlBar
                onConnectButtonClicked={props.onConnectButtonClicked}
                onCompleteHref={props.onCompleteHref}
                interviewId={props.interviewId}
                proctoringData={proctoringData}
                proctoring={proctoring}
              />
            </div>
             {/* Interview Status */}
            <div className="w-full max-w-5xl">
              <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl p-3 shadow-lg border border-white/20">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="h-3 w-3 rounded-full bg-green-500 animate-pulse"></div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Interview in Progress. Please say "hi" to start
                    </span>
                  </div>
                  {props.interviewData?.role && (
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {props.interviewData.role} • {props.interviewData.level}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <RoomAudioRenderer />
            <NoAgentNotification state={agentState} />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat-style Transcription View - Fixed to right side */}
      {agentState !== 'disconnected' && <TranscriptionView />}

      {/* Enhanced Proctoring Dashboard */}
      {showProctoringDashboard && isProctoringEnabled && (
        <EnhancedProctoringDashboard
          proctoringData={proctoringData}
          isMinimized={isDashboardMinimized}
          onToggleMinimize={() => setIsDashboardMinimized(!isDashboardMinimized)}
          onViolationAlert={handleProctoringViolation}
        />
      )}

      {/* Hidden video element for object detection */}
      {showProctoringDashboard && proctoringData.proctoringVideoRef && (
        <div className="hidden">
          <video
            ref={proctoringData.proctoringVideoRef}
            autoPlay
            muted
            playsInline
            style={{ width: '640px', height: '480px' }}
          />
        </div>
      )}
    </>
  );
}

export function AgentVisualizer() {
  const { state: agentState, videoTrack, audioTrack } = useVoiceAssistant();

  if (videoTrack) {
    return (
      <div className="h-[400px] w-[400px] overflow-hidden rounded-xl shadow-lg border-4 border-white/20">
        {React.createElement(VideoTrack as any, { trackRef: videoTrack, className: 'w-full h-full object-cover' })}
      </div>
    );
  }
  return (
    <div className="h-[400px] w-[400px] bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl shadow-lg border-4 border-white/20 flex flex-col items-center justify-center relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent transform -skew-x-12"></div>
      </div>

      {/* AI Avatar */}
      <div className="relative z-10 flex flex-col items-center gap-4">
        <div className="h-20 w-20 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
          <svg className="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>

        <div className="text-center">
          <h4 className="text-white font-semibold text-lg">Flinkk AI</h4>
          <p className="text-white/80 text-sm">
            {agentState === 'listening' ? 'Listening...' :
             agentState === 'thinking' ? 'Processing...' :
             agentState === 'speaking' ? 'Speaking...' : 'Ready'}
          </p>
        </div>

        {/* Audio Visualizer */}
        <div className="w-full max-w-[300px]">
          <BarVisualizer
            state={agentState}
            barCount={7}
            trackRef={audioTrack}
            className="agent-visualizer"
            options={{ minHeight: 4, maxHeight: 40 }}
          />
        </div>
      </div>
    </div>
  );
}

export function LocalParticipantVideo() {
  const room = useContext(RoomContext);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!room) return;
    let isMounted = true;
    let pollingInterval: NodeJS.Timeout;
    const currentVideoRef = videoRef.current; // Capture ref value for cleanup

    // Register RPC method to end interview
    const endInterview = async (data: any): Promise<string> => {
      room.disconnect();
      return 'Interview ended';

    };

    room.localParticipant.registerRpcMethod(
      "client.endInterview",
      endInterview
    );

    const setupCamera = async () => {
      try {
        await room.localParticipant.setCameraEnabled(true);

        let attempt = 0;
        const maxAttempts = 25;

        pollingInterval = setInterval(() => {
          if (!isMounted) return;
          const cameraPub = room.localParticipant.getTrackPublication(Track.Source.Camera);
          if (cameraPub?.track?.mediaStreamTrack) {
            clearInterval(pollingInterval);
            attachVideo(cameraPub.track.mediaStreamTrack);
          } else {
            attempt++;
            if (attempt >= maxAttempts) {
              clearInterval(pollingInterval);
              console.error('Camera track not found after polling.');
              setError('Camera track not found. Please try again.');
            }
          }
        }, 200);
      } catch (err) {
        console.error('Camera setup failed:', err);
        if (isMounted) {
          setError('Failed to enable camera. Please allow permissions.');
        }
      }
    };

    const attachVideo = (mediaStreamTrack: MediaStreamTrack) => {
      if (!videoRef.current) return;
      const stream = new MediaStream([mediaStreamTrack]);
      videoRef.current.srcObject = stream;
      videoRef.current.play().catch((err) => {
        console.error('Video playback failed:', err);
        setError('Video playback failed');
      });
    };

    setupCamera();

    return () => {
      isMounted = false;
      clearInterval(pollingInterval);
      if (currentVideoRef?.srcObject) {
        (currentVideoRef.srcObject as MediaStream).getTracks().forEach((t) => t.stop());
        currentVideoRef.srcObject = null;
      }
    };
  }, [room]);

  return (
    <div className="relative h-[400px] w-[400px] overflow-hidden rounded-xl bg-black shadow-lg border-4 border-white/20">
      <video ref={videoRef} autoPlay muted playsInline className="h-full w-full object-cover" />
      {error && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/80 backdrop-blur-sm p-6 text-white">
          <div className="text-center space-y-4">
            <div className="h-12 w-12 rounded-full bg-red-500/20 flex items-center justify-center mx-auto">
              <svg className="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <p className="text-lg font-semibold">Camera Error</p>
              <p className="mt-2 text-center text-sm text-gray-300">{error}</p>
            </div>
            <button
              onClick={() => {
                setError(null);
                room?.localParticipant.setCameraEnabled(true).catch(console.error);
              }}
              className="mt-4 rounded-lg bg-blue-600 px-6 py-2 font-medium transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-black"
            >
              Retry Camera
            </button>
          </div>
        </div>
      )}
      <div className="absolute bottom-3 left-3 rounded-lg bg-black/60 backdrop-blur-sm px-3 py-1 text-sm font-medium text-white border border-white/20">
        You
      </div>
      {/* Recording indicator */}
      <div className="absolute top-3 right-3 flex items-center gap-2 rounded-lg bg-red-500/90 backdrop-blur-sm px-3 py-1 text-xs font-medium text-white">
        <div className="h-2 w-2 rounded-full bg-white animate-pulse"></div>
        REC
      </div>
    </div>
  );
}

export function ControlBar({
  onConnectButtonClicked,
  onCompleteHref,
  interviewId,
  proctoringData,
  proctoring
}: {
  onConnectButtonClicked: () => void;
  onCompleteHref: string;
  interviewId?: string;
  proctoringData?: any;
  proctoring?: any;
}) {
  const { state: agentState } = useVoiceAssistant();
  const router = useRouter();
  const room = useContext(RoomContext);

  const handleCompleteInterview = async () => {
    if (interviewId) {
      try {
        // Save final proctoring data before completing
        if (proctoringData && proctoring) {
          const finalProctoringData = {
            ...proctoring,
            eyeTracking: proctoringData.eyeTracking,
            emotionAnalysis: proctoringData.emotionAnalysis,
            objectDetection: proctoringData.objectDetection,
            multiPerson: proctoringData.multiPerson,
            voiceAnalysis: proctoringData.voiceAnalysis,
            fullScreen: proctoring.fullScreen || 0,
            tabSwitch: proctoring.tabSwitch || 0,
            timestamp: Date.now(),
          };
          console.log('💾 AI Interview Final - saving proctoring data:', finalProctoringData);
          await updateRecordProctor({ id: interviewId, data: finalProctoringData });
        }

         fetch('/api/livekit-connect/complete', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            careerPracticeId: interviewId,
          }),
        });
      } catch (error) {
        console.error('Error completing interview:', error);
      }
    }
    router.push(onCompleteHref);
  };

  useEffect(() => {
    const handleDisconnect = () => {
      handleCompleteInterview();
    };

    room?.on(RoomEvent.Disconnected, handleDisconnect);
    return () => {
      room?.off(RoomEvent.Disconnected, handleDisconnect);
    };
  }, [room, router, interviewId]);

  return (
    <div className="relative flex items-center justify-center gap-4 p-4">
      {agentState === 'disconnected' ? (
        <motion.button
          onClick={onConnectButtonClicked}
          className="rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-3 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        >
          Start a conversation
        </motion.button>
      ) : (
        <div className="flex items-center justify-center gap-4 w-full">
          {/* Custom styled control bar */}
          
            <VoiceAssistantControlBar controls={{ leave: false }} />
        

          {/* End Interview Button */}
          <div className="flex items-center">
            {React.createElement(DisconnectButton as any, {
              className: "flex items-center gap-2 bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-xl font-medium shadow-lg transition-all duration-200"
            }, (
              <>
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                End Interview
              </>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export function TranscriptionView() {
  const combinedTranscriptions = useCombinedTranscriptions();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [combinedTranscriptions]);

  return (
    <div className="fixed right-0 top-0 h-screen w-96 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-l border-gray-200 dark:border-gray-700 shadow-2xl z-50">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <div className="h-3 w-3 rounded-full bg-green-500 animate-pulse"></div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Live Transcription
          </h3>
        </div>
       
      </div>

      {/* Chat Messages */}
      <div ref={containerRef} className="flex-1 h-full overflow-y-auto p-4 pb-20">
        <div className="flex flex-col gap-4">
          {combinedTranscriptions.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <svg className="h-8 w-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p className="text-sm">Conversation will appear here</p>
              </div>
            </div>
          ) : (
            combinedTranscriptions.map((segment) => (
              <div
                id={segment.id}
                key={segment.id}
                className={`flex ${segment.role === 'assistant' ? 'justify-start' : 'justify-end'}`}
              >
                <div
                  className={`max-w-[80%] rounded-2xl px-4 py-3 ${
                    segment.role === 'assistant'
                      ? 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-tl-sm'
                      : 'bg-blue-600 text-white rounded-tr-sm'
                  }`}
                >
                  <div className="flex items-start gap-2">
                    {segment.role === 'assistant' && (
                      <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center mt-0.5">
                        <svg className="h-3 w-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                    <div className="flex-1">
                      <p className="text-sm leading-relaxed">{segment.text}</p>
                    </div>
                    {segment.role === 'user' && (
                      <div className="flex-shrink-0 w-6 h-6 rounded-full bg-white/20 flex items-center justify-center mt-0.5">
                        <svg className="h-3 w-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

     
    </div>
  );
}

export function useLocalMicTrack() {
  const { microphoneTrack, localParticipant } = useLocalParticipant();

  const micTrackRef: TrackReferenceOrPlaceholder = React.useMemo(() => {
    return {
      participant: localParticipant,
      source: Track.Source.Microphone,
      publication: microphoneTrack,
    };
  }, [localParticipant, microphoneTrack]);

  return micTrackRef;
}

export function useCombinedTranscriptions() {
  const { agentTranscriptions } = useVoiceAssistant();
  const micTrackRef = useLocalMicTrack();
  const { segments: userTranscriptions } = useTrackTranscription(micTrackRef);

  const combinedTranscriptions = React.useMemo(() => {
    return [
      ...agentTranscriptions.map((val) => ({ ...val, role: 'assistant' })),
      ...userTranscriptions.map((val) => ({ ...val, role: 'user' })),
    ].sort((a, b) => a.firstReceivedTime - b.firstReceivedTime);
  }, [agentTranscriptions, userTranscriptions]);

  return combinedTranscriptions;
}
