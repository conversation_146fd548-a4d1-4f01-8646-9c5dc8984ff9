'use client';

import { useEffect, useState } from 'react';

import { Icon } from '@/icons';
import { getCookie } from '@/utils/cookies';

import { Button } from '@camped-ui/button';
import { FormControl, FormItem, FormLabel } from '@camped-ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@camped-ui/popover';

interface AIEvent {
  id: string;
  name: string;
  role: string;
  level: string;
  status: string;
  _count: {
    careerPractices: number;
  };
}

interface AIEventsSelectorProps {
  selectedEventId?: string;
  onEventSelect: (eventId: string, eventName: string) => void;
}

export const AIEventsSelector = ({
  selectedEventId,
  onEventSelect,
}: AIEventsSelectorProps) => {
  const [aiEvents, setAiEvents] = useState<AIEvent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [openPopover, setOpenPopover] = useState(false);
  const organizationId = getCookie('aceprepTenantId');

  const selectedEvent = aiEvents.find((event) => event.id === selectedEventId);

  useEffect(() => {
    if (openPopover && aiEvents.length === 0) {
      fetchAIEvents();
    }
  }, [openPopover]);

  const fetchAIEvents = async () => {
    if (!organizationId) {
      setError('Organization ID not found');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/get-ai-events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch AI events');
      }

      const data = await response.json();
      setAiEvents(data.items || []);

      if (data.items?.length === 0) {
        setError('No AI events available');
      }
    } catch (err) {
      console.error('Error fetching AI events:', err);
      setError('Failed to load AI events');
    } finally {
      setLoading(false);
    }
  };

  const handleEventSelect = (event: AIEvent) => {
    onEventSelect(event.id, event.name);
    setOpenPopover(false);
  };

  return (
    <FormItem className="space-y-3">
      <FormLabel className="text-sm font-medium text-gray-900 dark:text-white">
        Select AI Event
      </FormLabel>
      <FormControl>
        <Popover open={openPopover} onOpenChange={setOpenPopover}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={openPopover}
              className="w-full justify-between"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <Icon name="Loader" className="h-4 w-4 animate-spin" />
                  <span>Loading AI events...</span>
                </div>
              ) : selectedEvent ? (
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-purple-500"></div>
                  <span className="truncate">{selectedEvent.name}</span>
                  <span className="text-xs text-gray-500">
                    ({selectedEvent.role} - {selectedEvent.level})
                  </span>
                </div>
              ) : (
                <span className="text-gray-500">Select an AI event...</span>
              )}
              <Icon name="ChevronsUpDown" className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <div className="max-h-60 overflow-y-auto">
              {error ? (
                <div className="p-4 text-center text-sm text-red-600">
                  <Icon name="AlertCircle" className="mx-auto mb-2 h-8 w-8" />
                  {error}
                </div>
              ) : aiEvents.length === 0 && !loading ? (
                <div className="p-4 text-center text-sm text-gray-500">
                  <Icon name="Search" className="mx-auto mb-2 h-8 w-8" />
                  No AI events found
                </div>
              ) : (
                <div className="space-y-1 p-2">
                  {aiEvents.map((event) => (
                    <button
                      key={event.id}
                      onClick={() => handleEventSelect(event)}
                      className={`flex w-full items-center justify-between rounded-lg p-3 text-left text-sm transition-all duration-200 hover:bg-purple-50 hover:text-purple-700 dark:hover:bg-purple-950/20 ${
                        selectedEventId === event.id
                          ? 'bg-purple-100 text-purple-700 dark:bg-purple-950/30'
                          : ''
                      }`}
                    >
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full bg-purple-500"></div>
                          <span className="font-medium">{event.name}</span>
                        </div>
                        <div className="mt-1 text-xs text-gray-500">
                          {event.role} - {event.level} • {event._count.careerPractices} candidates
                        </div>
                      </div>
                      {selectedEventId === event.id && (
                        <Icon name="Check" className="h-4 w-4 text-purple-600" />
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </FormControl>
    </FormItem>
  );
};
