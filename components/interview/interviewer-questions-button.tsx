'use client';

import { useState } from 'react';
import { toast } from 'sonner';

import { Icon } from '@/icons';

import { Button } from '@camped-ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@camped-ui/dialog';
import { Textarea } from '@camped-ui/textarea';
import { Badge } from '@camped-ui/badge';

interface InterviewerQuestionsButtonProps {
  candidateId: string;
  interviewId: string;
  candidateName: string;
  interviewName: string;
  roundType?: string;
  hasQuestions: boolean;
  currentQuestions?: string;
  interviewStatus: string;
}

export const InterviewerQuestionsButton = ({
  candidateId,
  interviewId,
  candidateName,
  interviewName,
  roundType,
  hasQuestions,
  currentQuestions = '',
  interviewStatus
}: InterviewerQuestionsButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [questions, setQuestions] = useState(currentQuestions);
  const [isLoading, setIsLoading] = useState(false);

  const isCompleted = interviewStatus === 'COMPLETED';

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/interview/${interviewId}/update-interviewer-questions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interviewerQuestions: questions.trim() || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update questions');
      }

      toast.success('Questions updated successfully');
      setIsOpen(false);
      // Refresh the page to show updated data
      window.location.reload();
    } catch (error) {
      console.error('Error updating questions:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update questions');
    } finally {
      setIsLoading(false);
    }
  };

  const getQuestionsCount = () => {
    if (!questions.trim()) return 0;
    return questions.split('\n').filter((q: string) => q.trim()).length;
  };

  if (isCompleted) {
    return hasQuestions ? (
      <Badge variant="secondary" className="text-xs flex items-center gap-1">
        <Icon name="MessageSquare" className="h-3 w-3" />
        {currentQuestions.split('\n').filter((q: string) => q.trim()).length} questions
      </Badge>
    ) : null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant={hasQuestions ? "default" : "outline"} 
          size="sm"
          className="text-xs flex items-center gap-1"
        >
          <Icon name="MessageSquare" className="h-3 w-3" />
          {hasQuestions ? (
            <>
              Questions
              <Badge variant="secondary" className="ml-1 text-xs px-1">
                {currentQuestions.split('\n').filter((q: string) => q.trim()).length}
              </Badge>
            </>
          ) : (
            'Add Questions'
          )}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icon name="MessageSquare" className="h-5 w-5" />
            Questions for {candidateName}
          </DialogTitle>
          <div className="text-sm text-gray-600 space-y-1">
            <p>Interview: {interviewName}</p>
            {roundType && (
              <Badge variant="outline" className="text-xs">
                {roundType.charAt(0).toUpperCase() + roundType.slice(1)} Round
              </Badge>
            )}
          </div>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">
              Interviewer Questions (one per line)
            </label>
            <Textarea
              placeholder="Enter specific questions for this candidate:&#10;&#10;Can you elaborate on your React experience?&#10;How do you handle state management?&#10;Tell me about your previous project challenges."
              value={questions}
              onChange={(e) => setQuestions(e.target.value)}
              className="min-h-[150px]"
              rows={8}
            />
            <div className="flex items-center justify-between mt-2 text-sm text-gray-500">
              <span>Enter each question on a new line</span>
              <span>{getQuestionsCount()} question{getQuestionsCount() !== 1 ? 's' : ''}</span>
            </div>
          </div>

          {/* Questions Preview */}
          {getQuestionsCount() > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Preview:</h4>
              <div className="space-y-2 max-h-40 overflow-y-auto border rounded p-3 bg-gray-50">
                {questions.split('\n').filter((q: string) => q.trim()).map((question: string, index: number) => (
                  <div key={index} className="flex items-start gap-2 text-sm">
                    <span className="font-medium text-blue-600 min-w-[20px]">{index + 1}.</span>
                    <span className="text-gray-800">{question.trim()}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-500">
              These questions will be sent to the AI along with general interview topics
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setIsOpen(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSave}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Icon name="Loader2" className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Questions'
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
