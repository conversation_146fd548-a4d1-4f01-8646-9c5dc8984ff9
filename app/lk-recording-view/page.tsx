'use client';

import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import SimpleVoiceAssistant from '@/components/SimpleVoiceAssistant';
import { RoomContext } from '@livekit/components-react';
import '@livekit/components-styles';
import { Room, RoomEvent } from 'livekit-client';

// This page serves as the custom recording template for LiveKit egress
// It accepts URL parameters: url (LiveKit server URL), token (access token), and layout
export default function LiveKitRecordingView() {
  const searchParams = useSearchParams();
  const [room] = useState(() => new Room());
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const hasStartedRecording = useRef(false);

  // Extract parameters from URL
  const livekitUrl = searchParams.get('url');
  const token = searchParams.get('token');
  const layout = searchParams.get('layout') || 'speaker';
  const roomName = searchParams.get('room') || 'recording-room';

  useEffect(() => {
    if (!livekitUrl || !token) {
      setError('Missing required parameters: url and token');
      return;
    }

    const connectToRoom = async () => {
      try {
        console.log('🎬 LiveKit Recording View: Connecting to room...');
        
        // Connect to the room
        await room.connect(livekitUrl, token, { autoSubscribe: true });
        
        // Enable microphone and camera
        await Promise.all([
          room.localParticipant.setMicrophoneEnabled(true),
          room.localParticipant.setCameraEnabled(true),
        ]);

        setIsConnected(true);
        setStartTime(new Date());
        
        console.log('🎬 LiveKit Recording View: Connected successfully');
        
        // Signal to LiveKit egress that recording can start
        // This is required for the egress recorder to begin capturing
        setTimeout(() => {
          if (!hasStartedRecording.current) {
            console.log('START_RECORDING');
            hasStartedRecording.current = true;
          }
        }, 2000); // Wait 2 seconds for UI to stabilize

      } catch (err) {
        console.error('❌ LiveKit Recording View: Connection failed:', err);
        setError(`Failed to connect to room: ${err.message}`);
      }
    };

    connectToRoom();

    // Handle room events
    const handleDisconnected = () => {
      console.log('🎬 LiveKit Recording View: Room disconnected');
      console.log('END_RECORDING');
    };

    const handleError = (error: Error) => {
      console.error('❌ LiveKit Recording View: Room error:', error);
      setError(`Room error: ${error.message}`);
    };

    room.on(RoomEvent.Disconnected, handleDisconnected);
    room.on(RoomEvent.ConnectionStateChanged, (state) => {
      console.log('🎬 LiveKit Recording View: Connection state changed:', state);
    });

    // Cleanup
    return () => {
      room.off(RoomEvent.Disconnected, handleDisconnected);
      if (isConnected) {
        room.disconnect();
      }
    };
  }, [livekitUrl, token, room, isConnected]);

  // Handle page visibility changes to signal recording end
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && hasStartedRecording.current) {
        console.log('END_RECORDING');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center bg-red-50">
        <div className="rounded-lg bg-white p-8 shadow-lg">
          <h1 className="text-xl font-bold text-red-600 mb-4">Recording Error</h1>
          <p className="text-red-500">{error}</p>
          <div className="mt-4 text-sm text-gray-600">
            <p>Expected URL parameters:</p>
            <ul className="list-disc list-inside mt-2">
              <li>url: LiveKit server WebSocket URL</li>
              <li>token: Access token for the room</li>
              <li>layout: Recording layout (optional, defaults to 'speaker')</li>
              <li>room: Room name (optional)</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  if (!isConnected) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-700">Connecting to interview room...</p>
          <p className="text-sm text-gray-500 mt-2">Room: {roomName}</p>
        </div>
      </div>
    );
  }

  return (
    <main 
      data-lk-theme="default" 
      className="h-screen w-screen bg-[var(--lk-bg)] overflow-hidden"
      style={{ 
        height: '100vh', 
        width: '100vw',
        position: 'fixed',
        top: 0,
        left: 0,
        zIndex: 1000
      }}
    >
      <RoomContext.Provider value={room}>
        <div className="h-full w-full">
          <SimpleVoiceAssistant
            onConnectButtonClicked={() => {}} // Already connected
            onCompleteHref="/interview-complete"
            interviewId={roomName}
            interviewData={{
              role: 'Software Developer',
              level: 'Mid-Level',
              event: 'AI Interview Recording',
              jobDescription: 'Recording session for AI interview'
            }}
          />
        </div>
      </RoomContext.Provider>
    </main>
  );
}
