import { redirect } from 'next/navigation';

import { InterviewCompletedScreen } from '@/components/wrapper-screen/interview-completed-screen';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function InterviewCompletion(props) {
  const session = await getServerSession(authOptions);

  return <InterviewCompletedScreen session={session} />;
}
