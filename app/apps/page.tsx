import Link from 'next/link';

import { allAppsData } from '@/constants/apps-metadata';

import { Card, CardContent, CardTitle } from '@camped-ui/card';

export default async function MyProfile() {
  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
      {allAppsData.map((item, key) => {
        return (
          <Link key={key} href={item.redirectPath}>
            <Card
              className="gradient1 flex cursor-pointer flex-col items-start justify-between p-4"
              style={{ minHeight: 180 }}
              key={key}
            >
              <div className="flex-1"></div>
              <CardContent className="p-0 text-left text-[#fff]">{item?.description}</CardContent>
              <CardTitle className="text-left text-xl text-[#fff]">{item?.name}</CardTitle>
            </Card>
          </Link>
        );
      })}
    </div>
  );
}
