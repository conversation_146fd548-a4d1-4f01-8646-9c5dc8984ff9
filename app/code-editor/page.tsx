'use client';

import { useEffect, useState } from 'react';

import Editor, { Monaco } from '@monaco-editor/react';
import { editor } from 'monaco-editor';

// Default code templates for different languages
const codeTemplates = {
  javascript: `// JavaScript code
function helloWorld() {
  console.log("Hello, world!");
}

helloWorld();
`,
  typescript: `// TypeScript code
function greet(name: string): string {
  return \`Hello, \${name}!\`;
}

const message: string = greet("World");
console.log(message);
`,
  python: `# Python code
def hello_world():
    print("Hello, world!")

hello_world()
`,
  java: `// Java code
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, world!");
    }
}
`,
  csharp: `// C# code
using System;

class Program {
    static void Main() {
        Console.WriteLine("Hello, world!");
    }
}
`,
  cpp: `// C++ code
#include <iostream>

int main() {
    std::cout << "Hello, world!" << std::endl;
    return 0;
}
`,
  php: `<?php
// PHP code
function helloWorld() {
    echo "Hello, world!";
}

helloWorld();
?>
`,
  ruby: `# Ruby code
def hello_world
  puts "Hello, world!"
end

hello_world
`,
  go: `// Go code
package main

import "fmt"

func main() {
    fmt.Println("Hello, world!")
}
`,
  rust: `// Rust code
fn main() {
    println!("Hello, world!");
}
`,
  html: `<!DOCTYPE html>
<html>
<head>
    <title>Hello World</title>
</head>
<body>
    <h1>Hello, world!</h1>
</body>
</html>
`,
  css: `/* CSS code */
body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
}

h1 {
    color: #333;
    text-align: center;
}
`,
  sql: `-- SQL code
CREATE TABLE users (
    id INT PRIMARY KEY,
    name VARCHAR(100),
    email VARCHAR(100)
);

INSERT INTO users (id, name, email) VALUES (1, 'John Doe', '<EMAIL>');

SELECT * FROM users;
`,
};

const defaultCode = codeTemplates.javascript;

export default function CodeEditorPage() {
  const [editorValue, setEditorValue] = useState(defaultCode);
  const [language, setLanguage] = useState('javascript');
  const [editorInstance, setEditorInstance] = useState<editor.IStandaloneCodeEditor | null>(null);
  const [consoleOutput, setConsoleOutput] = useState<string[]>([]);
  const [showConsole, setShowConsole] = useState(false);

  // Language-specific information
  const languageInfo = {
    javascript: 'JavaScript runs natively in the browser. Your code will be executed directly.',
    typescript:
      'TypeScript code is simulated. In a real environment, it would be transpiled to JavaScript first.',
    python:
      'Python code is simulated. In a real environment, it would require a Python interpreter.',
    java: 'Java code is simulated. In a real environment, it would be compiled to bytecode and run on the JVM.',
    csharp:
      'C# code is simulated. In a real environment, it would be compiled to IL and run on the .NET runtime.',
    cpp: 'C++ code is simulated. In a real environment, it would be compiled to machine code.',
    php: 'PHP code is simulated. In a real environment, it would require a PHP interpreter.',
    ruby: 'Ruby code is simulated. In a real environment, it would require a Ruby interpreter.',
    go: 'Go code is simulated. In a real environment, it would be compiled to machine code.',
    rust: 'Rust code is simulated. In a real environment, it would be compiled to machine code.',
    html: "HTML is rendered in the browser. In this simulation, we're just showing the code.",
    css: "CSS styles HTML elements. In this simulation, we're just showing the code.",
    sql: 'SQL queries are simulated. In a real environment, they would be executed against a database.',
  };

  // Handle editor value changes
  const handleEditorChange = (value: string | undefined) => {
    setEditorValue(value || '');
  };

  // Handle editor mount
  const handleEditorDidMount = (editor: editor.IStandaloneCodeEditor, monaco: Monaco) => {
    setEditorInstance(editor);

    // Focus the editor when it mounts
    editor.focus();

    // Add any additional editor configurations here
    editor.updateOptions({
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      fontSize: 14,
      wordWrap: 'on',
    });
  };

  // Reset editor content to template for current language
  const resetEditor = () => {
    const template = codeTemplates[language] || defaultCode;
    setEditorValue(template);
    if (editorInstance) {
      editorInstance.setValue(template);
    }
  };

  // Download the current code
  const downloadCode = () => {
    // Get file extension based on language
    const fileExtensions = {
      javascript: 'js',
      typescript: 'ts',
      python: 'py',
      java: 'java',
      csharp: 'cs',
      cpp: 'cpp',
      php: 'php',
      ruby: 'rb',
      go: 'go',
      rust: 'rs',
      html: 'html',
      css: 'css',
      sql: 'sql',
    };

    const extension = fileExtensions[language] || 'txt';
    const fileName = `code.${extension}`;

    // Create a blob with the code content
    const blob = new Blob([editorValue], { type: 'text/plain' });

    // Create a download link
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;

    // Trigger the download
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Show confirmation in console
    setShowConsole(true);
    setConsoleOutput((prev) => [...prev, `Downloaded ${fileName} successfully.`]);
  };

  // Available language options
  const languageOptions = [
    { value: 'javascript', label: 'JavaScript' },
    { value: 'typescript', label: 'TypeScript' },
    { value: 'python', label: 'Python' },
    { value: 'java', label: 'Java' },
    { value: 'csharp', label: 'C#' },
    { value: 'cpp', label: 'C++' },
    { value: 'php', label: 'PHP' },
    { value: 'ruby', label: 'Ruby' },
    { value: 'go', label: 'Go' },
    { value: 'rust', label: 'Rust' },
    { value: 'html', label: 'HTML' },
    { value: 'css', label: 'CSS' },
    { value: 'sql', label: 'SQL' },
  ];

  // Handle language change
  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLanguage = e.target.value;
    setLanguage(newLanguage);

    // Update the editor content with the template for the selected language
    if (codeTemplates[newLanguage]) {
      setEditorValue(codeTemplates[newLanguage]);
      if (editorInstance) {
        editorInstance.setValue(codeTemplates[newLanguage]);
      }
    }

    // Show the console with language information
    setShowConsole(true);
    setConsoleOutput([
      `Language changed to ${newLanguage}`,
      languageInfo[newLanguage] || `${newLanguage} code editing is supported.`,
      '',
      'Edit your code and click "Run" to see the output.',
    ]);
  };

  // Simulate output for different languages
  const simulateOutput = () => {
    switch (language) {
      case 'python':
        if (editorValue.includes('print(')) {
          // Extract content from print statements
          const printMatches = editorValue.match(/print\(["'](.+?)["']\)/g) || [];
          return printMatches.map((match) => {
            const content = match.match(/print\(["'](.+?)["']\)/);
            return content ? content[1] : '';
          });
        }
        return ['Program executed successfully.'];

      case 'java':
        if (editorValue.includes('System.out.println')) {
          // Extract content from println statements
          const printMatches = editorValue.match(/System\.out\.println\(["'](.+?)["']\)/g) || [];
          return printMatches.map((match) => {
            const content = match.match(/System\.out\.println\(["'](.+?)["']\)/);
            return content ? content[1] : '';
          });
        }
        return ['Java program compiled and executed successfully.'];

      case 'csharp':
        if (editorValue.includes('Console.WriteLine')) {
          // Extract content from WriteLine statements
          const printMatches = editorValue.match(/Console\.WriteLine\(["'](.+?)["']\)/g) || [];
          return printMatches.map((match) => {
            const content = match.match(/Console\.WriteLine\(["'](.+?)["']\)/);
            return content ? content[1] : '';
          });
        }
        return ['C# program compiled and executed successfully.'];

      case 'cpp':
        if (editorValue.includes('std::cout')) {
          // Extract content from cout statements
          const printMatches = editorValue.match(/std::cout\s*<<\s*["'](.+?)["']/g) || [];
          return printMatches.map((match) => {
            const content = match.match(/std::cout\s*<<\s*["'](.+?)["']/);
            return content ? content[1] : '';
          });
        }
        return ['C++ program compiled and executed successfully.'];

      case 'typescript':
        if (editorValue.includes('console.log')) {
          // Extract content from console.log statements
          const printMatches = editorValue.match(/console\.log\(["'`](.+?)["'`]\)/g) || [];
          return printMatches.map((match) => {
            const content = match.match(/console\.log\(["'`](.+?)["'`]\)/);
            return content ? content[1] : '';
          });
        }
        return ['TypeScript program transpiled and executed successfully.'];

      default:
        return [`${language.toUpperCase()} code simulation completed successfully.`];
    }
  };

  // Run code function
  const runCode = () => {
    // Show console when running code
    setShowConsole(true);

    // Clear previous output
    setConsoleOutput([]);

    try {
      // Only attempt to run JavaScript code directly
      if (language === 'javascript') {
        // Override console.log to capture output
        const originalConsoleLog = console.log;
        console.log = (...args) => {
          originalConsoleLog(...args);
          setConsoleOutput((prev) => [
            ...prev,
            args
              .map((arg) => (typeof arg === 'object' ? JSON.stringify(arg) : String(arg)))
              .join(' '),
          ]);
        };

        // Create a new function from the code and execute it
        const executeCode = new Function(editorValue);
        executeCode();

        // Restore original console.log
        setTimeout(() => {
          console.log = originalConsoleLog;
        }, 100);
      } else {
        // For non-JavaScript languages, simulate the output
        const simulatedOutput = simulateOutput();
        setConsoleOutput([
          `Running ${language} code simulation...`,
          languageInfo[language] || `${language} code is being simulated.`,
          '',
          ...simulatedOutput,
          '',
          `${language.toUpperCase()} simulation completed.`,
        ]);
      }
    } catch (error) {
      console.error('Error executing code:', error);
      setConsoleOutput((prev) => [...prev, `Error: ${error.message}`]);
    }
  };

  return (
    <div
      className="code-editor-page"
      style={{ height: '100vh', width: '100%', display: 'flex', flexDirection: 'column' }}
    >
      <div
        className="editor-toolbar"
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          padding: '8px',
          backgroundColor: '#1e1e1e',
          color: 'white',
        }}
      >
        <div className="language-selector">
          <select
            value={language}
            onChange={handleLanguageChange}
            style={{
              backgroundColor: '#333',
              color: 'white',
              border: 'none',
              padding: '4px 8px',
              borderRadius: '4px',
              marginRight: '10px',
            }}
          >
            {languageOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        <div className="editor-actions">
          <button
            onClick={runCode}
            style={{
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              padding: '4px 8px',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '8px',
            }}
          >
            Run
          </button>
          <button
            onClick={() => setShowConsole(!showConsole)}
            style={{
              backgroundColor: '#2196F3',
              color: 'white',
              border: 'none',
              padding: '4px 8px',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '8px',
            }}
          >
            {showConsole ? 'Hide Console' : 'Show Console'}
          </button>
          <button
            onClick={resetEditor}
            style={{
              backgroundColor: '#4a4a4a',
              color: 'white',
              border: 'none',
              padding: '4px 8px',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '8px',
            }}
          >
            Reset
          </button>
          <button
            onClick={downloadCode}
            style={{
              backgroundColor: '#9c27b0',
              color: 'white',
              border: 'none',
              padding: '4px 8px',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            Download
          </button>
        </div>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', height: 'calc(100% - 40px)' }}>
        <Editor
          height={showConsole ? '70%' : '100%'}
          width="100%"
          language={language}
          value={editorValue}
          theme="vs-dark"
          onChange={handleEditorChange}
          onMount={handleEditorDidMount}
          options={{
            automaticLayout: true,
          }}
        />

        {showConsole && (
          <div
            className="console-output"
            style={{
              height: '30%',
              backgroundColor: '#1e1e1e',
              color: '#fff',
              padding: '8px',
              fontFamily: 'monospace',
              fontSize: '14px',
              overflowY: 'auto',
              borderTop: '1px solid #333',
            }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span>Console Output</span>
              <button
                onClick={() => setConsoleOutput([])}
                style={{
                  backgroundColor: 'transparent',
                  color: '#ccc',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '12px',
                }}
              >
                Clear
              </button>
            </div>
            <div>
              {consoleOutput.length === 0 ? (
                <div style={{ color: '#666', fontStyle: 'italic' }}>
                  No output yet. Run your code to see results here.
                </div>
              ) : (
                consoleOutput.map((line, index) => (
                  <div key={index} style={{ marginBottom: '4px' }}>
                    {line}
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
