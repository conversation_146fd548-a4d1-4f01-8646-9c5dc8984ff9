import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { ProfileScreen } from '@/components/wrapper-screen/profile-screen';
import { getUserDetails } from '@/services/apicall';
import { getUserProfile } from '@/services/apicall';

export default async function MyProfile(props) {
  const id = props?.searchParams?.id;
  const userId = cookies().get('aceprepUserId')?.value;
  if (!userId) {
    return redirect('/404');
  }
  const user = await getUserDetails(userId);
  const userProfile = await getUserProfile(userId);
  return (
    <ProfileScreen
      user={user}
      userId={userId}
      userProfile={userProfile}
      isRequired={true}
      interviewId={id}
    />
  );
}
