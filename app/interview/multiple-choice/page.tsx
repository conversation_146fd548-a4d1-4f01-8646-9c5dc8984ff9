import { redirect } from 'next/navigation';

import { MultipleChoiceScreen } from '@/components/interview/multiple-choice-screen';
import { authOptions } from '@/lib/auth';
import {
  generateInterviewFeedback,
  generateInterviewathonFeedback,
  getInterviewQuestion,
} from '@/services/apicall';
import { getUserProfile } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const id = props?.searchParams?.id;
  const session = await getServerSession(authOptions);
  let action = '';

  const careerPractice = await getInterviewQuestion({
    id: id,
    userId: session?.userId,
  });

  if (careerPractice?.error) {
    return redirect('/404');
  }

  if (careerPractice?.round === 'coding-interview') {
    return redirect(`/interview/coding?id=${id}`);
  }

  if (careerPractice?.round === 'frontend-interview') {
    return redirect(`/interview/frontend?id=${id}`);
  }

  if (careerPractice?.round === 'written-interview') {
    return redirect(`/interview/written-interview?id=${id}`);
  }
  if (careerPractice?.hasCompleted || careerPractice?.timing?.completedTime) {
    if (!careerPractice?.timing?.feedBackGenerateTime) {
      if (!careerPractice?.isPlacement) {
        generateInterviewFeedback(id);
      } else {
        generateInterviewathonFeedback(id);
      }
    }
    return redirect(`/completed?id=${id}`);
  }

  const userProfile = await getUserProfile(session?.userId);

  if (!userProfile) {
    action = 'update-profile';
  }

  const codingQuestions = careerPractice?.questions?.filter(
    (item) => item?.round === 'multiple-choice',
  );

  const practiceId = id;

  return (
    <MultipleChoiceScreen
      questionList={codingQuestions}
      careerPractice={careerPractice}
      practiceId={practiceId}
      action={action}
    />
  );
}
