import { redirect } from 'next/navigation';

import { OnboardingInterviewScreen } from '@/components/onboarding-interview/interview-onboarding-screen';
import { authOptions } from '@/lib/auth';
import { getInterviewDetails, getUserDetails, getUserProfile } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function Onboarding(props) {
  const session = await getServerSession(authOptions);

  const id = props?.searchParams?.id;

  if (!session?.userId) {
    return redirect('/404');
  }

  const userProfile = await getUserProfile(session?.userId);

  const interviewDetails = await getInterviewDetails(id);

  if (!interviewDetails) {
    return redirect('/404');
  }

  const timing = interviewDetails?.result?.timing;

  // Check if interview is completed (multiple ways to determine completion)
  const isCompleted =
    interviewDetails?.result?.hasCompleted ||
    timing?.completedTime ||
    interviewDetails?.result?.interviewStatus === 'COMPLETED';

  if (isCompleted) {
    return redirect(`/completed?id=${id}`);
  }

  // Pass user profile status to the onboarding screen
  // The screen will handle the flow based on whether profile exists
  return (
    <OnboardingInterviewScreen
      session={session}
      interviewDetails={interviewDetails?.result}
      userProfile={userProfile}
    />
  );
}
