import React from 'react';

import OnboardingTemplate from '@/components/onboarding-stepper/onboarding-template';

const CodingInstructions = () => {
  return (
    <>
      <OnboardingTemplate
        title="Coding Interview Instructions"
        description="Follow these instructions before we start the test"
      />
      <div className="mt-6 rounded-lg border p-6">
        <p className="text-xl font-semibold">Instructions to be followed:</p>
        <ul className="ml-6 list-disc">
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Read the question: Carefully read and understand the question before starting to code.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Code Editor: Type your code in the code editor provided. You can write multiple lines of
            code and use the auto-indent feature for better readability.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Run Code: After writing your code, you can run it to see the output. Click the
            &quot;Run&quot; button to execute your code.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Submission: Click the &quot;Submit&quot; button when you have completed all questions to
            submit the Coding round.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Your code will be evaluated based on correctness, efficiency, and adherence to best
            practices. Make sure to write clean and readable code.
          </li>
        </ul>
        <span className="my-2 text-[14px] font-normal leading-[20px]">
          Your answers are <strong>Saved</strong> automatically when you switch to the next question
          or test submits automatically. You can also manually save the code by clicking
          &quot;Save&quot; button.
        </span>
      </div>
    </>
  );
};

export default CodingInstructions;
