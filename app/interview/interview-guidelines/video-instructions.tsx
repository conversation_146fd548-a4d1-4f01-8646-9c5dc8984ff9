import React from 'react';

import OnboardingTemplate from '@/components/onboarding-stepper/onboarding-template';

const VideoInstructions = () => {
  return (
    <>
      <OnboardingTemplate
        title="Video Instructions"
        description="Follow these instructions before we start the test"
      />
      <div className="mt-10 rounded-lg border p-6">
        <p className="mt-2 text-xl font-semibold">Instructions to be followed:</p>
        <ul className="ml-6 list-disc">
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Language: Please speak in English during the entire assessment.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Relax: Take a deep breath and try to relax. We want you to be at ease while recording.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Speak clearly: Ensure you speak clearly and audibly, so your responses are easy to
            understand.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Time: Each question has a time limit of 1:30 minutes. Please keep your responses concise
            and within the allocated time.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Your recordings will be shared with interviewers for authenticity along with feedback
            generated by AI.
          </li>
        </ul>
        <span className="my-2 text-[14px] font-normal leading-[20px]">
          Click the <strong>Recording</strong> button below when you are prepared to begin.
        </span>
      </div>
    </>
  );
};

export default VideoInstructions;
