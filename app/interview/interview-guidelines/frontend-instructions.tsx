import React from 'react';

import OnboardingTemplate from '@/components/onboarding-stepper/onboarding-template';

const FrontendInstructions = () => {
  return (
    <>
      <OnboardingTemplate
        title="Frontend Coding Interview Instructions"
        description="Follow these instructions before we start the test"
      />
      <div className="mt-6 rounded-lg border p-6">
        <p className="mt-2 text-xl font-semibold">Frontend Coding Round Instructions</p>
        <ul className="ml-6 list-disc">
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Read the question: Carefully read the question and understand the requirements before
            starting to code.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Code Editor: Type your code in the code editor provided. You can write multiple lines of
            code and use the auto-indent feature for better readability.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Browser Preview: The Browser preview on the right-hand side will display the output of
            your code in real-time. Use it to visualize your frontend design and functionality.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Browser Console: The browser console on the bottom-right to debug your code. Use
            console.log() statements to log information and troubleshoot any issues.
          </li>
          <li className="my-2 text-[14px] font-normal leading-[20px]">
            Submission: Click the &quot;Submit&quot; button when you have completed all questions to
            submit the Frontend round.
          </li>
        </ul>
        <span className="my-2 text-[14px] font-normal leading-[20px]">
          Your answers are <strong>Saved</strong> automatically when you switch to next question or
          when test submits automatically. You can also manually save the code by clicking
          &quot;Save&quot; button which can find below the file viewer.
        </span>
      </div>
    </>
  );
};

export default FrontendInstructions;
