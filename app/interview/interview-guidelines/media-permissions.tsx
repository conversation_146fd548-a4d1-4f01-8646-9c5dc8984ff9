import React, { useCallback, useEffect, useRef, useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import AudioAnalyzer from '@/components/audio/analyser';
import OnboardingTemplate from '@/components/onboarding-stepper/onboarding-template';
import { PermissionStatus, VideoRecorder } from '@/components/video/recorder';
import { useProctoring } from '@/hooks/client/useProctoring';
import { Icon } from '@/icons';
import { useScreenShareStore } from '@/packages/shared-store/src/screen-share';
import { updateStartTest } from '@/services/apicall';
import Webcam from 'react-webcam';

import { Button } from '@camped-ui/button';


const CheckPermissions = ({ session, interviewDetails, onNext }) => {
  const [cameraLoaded, setCameraLoaded] = useState(false);
  const webcamRef = useRef<Webcam | null>(null);
  const screenVideoRef: any = useRef(null);
  const [videoPermissionStatus, setVideoPermissionStatus] = useState({});
  const [screenPermissionStatus, setScreenPermissionStatus] = useState({
    status: 'error',
    message: 'Please select Entire Screen to continue',
  });
  const [soundDetected, setSoundDetected] = useState(false);
  const [audioPermissionStatus, setAudioPermissionStatus] = useState({
    status: 'normal',
    message: 'Idle',
  });
  const screenShare = useScreenShareStore((state: any) => state?.screenShare);
  const updateScreenShare = useScreenShareStore((state: any) => state?.update);

  const requestScreenShare = useCallback(async () => {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 15, max: 30 },
        },
      });

      updateScreenShare(screenStream);

      const videoTrack = screenStream.getVideoTracks()[0];
      const settings = videoTrack.getSettings();

      if (settings?.displaySurface === 'monitor') {
        if (screenVideoRef.current) screenVideoRef.current.srcObject = screenStream;
        setScreenPermissionStatus({ status: 'success', message: 'Screen share has permission' });
      } else {
        setScreenPermissionStatus({
          status: 'error',
          message: 'Please select Entire Screen to continue',
        });
      }

      videoTrack.addEventListener('ended', () => {
        console.log('Screen sharing stopped');
        setScreenPermissionStatus({
          status: 'error',
          message: 'Please select Entire Screen to continue',
        });
      });
    } catch (error) {
      console.error('Error requesting screen share:', error);
      setScreenPermissionStatus({
        status: 'error',
        message: 'Screen sharing permission denied',
      });
    }
  }, [updateScreenShare]);

  useEffect(() => {
    if (screenShare?.active) {
      const videoTrack = screenShare.getVideoTracks()[0];
      const settings = videoTrack.getSettings();
      if (settings?.displaySurface === 'monitor') {
        if (screenVideoRef.current) screenVideoRef.current.srcObject = screenShare;
        setScreenPermissionStatus({ status: 'success', message: 'Screen share has permission' });
      } else {
        setScreenPermissionStatus({
          status: 'error',
          message: 'Please select Entire Screen to continue',
        });
      }
    }
  }, [screenShare]);

  // Auto-request screen share on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!screenShare?.active) {
        requestScreenShare();
      }
    }, 1000); // Small delay to let other permissions load first

    return () => clearTimeout(timer);
  }, [screenShare?.active, requestScreenShare]);
  const [isStartLoading, setIsStartLoading] = useState(false);

  const { fullScreen } = useProctoring({
    forceFullScreen: true,
    preventTabSwitch: true,
    preventContextMenu: true,
    preventUserSelection: true,
    preventCopy: true,
  });

  const router = useRouter();
  const params = useSearchParams();
  const id = params?.get('id');

  const handleStartTest = async () => {
    setIsStartLoading(true);
    try {
      await updateStartTest({ id: id, userId: session.userId });
    } catch (error) {
      console.error('Error starting the test:', error);
    } finally {
      router.refresh();
    }
  };

  const getPermissionStatus = () => {
    const videoStatus = (videoPermissionStatus as any)?.status;
    const audioStatus = audioPermissionStatus?.status;
    const screenStatus = screenPermissionStatus?.status;

    switch (true) {
      case videoStatus === 'error' && audioStatus === 'error' && screenStatus === 'error':
        return 'Need permissions to proceed further';
      case videoStatus === 'success' && audioStatus === 'error':
        return 'Need Microphone Access';
      case videoStatus !== 'success' && audioStatus !== 'error':
        return 'Need Video Access';
      case videoStatus === 'success' && audioStatus !== 'error' && screenStatus === 'error':
        return 'Need Entire Screen Share Access';
      case !soundDetected:
        return 'Unable to hear the audio, Please check your microphone or try speaking louder.';
      case videoStatus === 'success' &&
        audioStatus !== 'error' &&
        screenStatus === 'success' &&
        soundDetected:
        return 'Take Test';
      default:
        return 'Loading ...';
    }
  };

  const permissionStatus = getPermissionStatus();
  const isPermissionGranted = permissionStatus === 'Take Test';

  return (
    <div className="flex flex-col justify-center gap-8">
      {/* Progress Indicator */}
      <div className="mx-auto flex items-center gap-2 text-xs">
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">Welcome</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">Personal Info</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">ID Verification</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">Face Verification</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">Guidelines</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-xs font-medium text-white">
            6
          </div>
          <span className="text-xs font-medium text-blue-600">Setup</span>
        </div>
      </div>

      <OnboardingTemplate
        title="Setup Your Camera & Microphone"
        description={
          "Let's test your camera, microphone, and screen sharing to ensure everything works perfectly for your interview."
        }
      />
      <div className="relative flex flex-col items-center gap-6 md:flex-row md:gap-8 py-4">
        <div className="h-full w-full space-y-4">
          <div className="relative">
            <div className="absolute -top-3 left-4 z-10 flex items-center gap-2 rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              Camera
            </div>
            <div className="h-full w-full rounded-lg border bg-card shadow-sm">
              <div className="relative mb-8 h-56 w-full rounded-lg md:w-full">
                <VideoRecorder
                  cameraLoaded={cameraLoaded}
                  setCameraLoaded={setCameraLoaded}
                  webcamRef={webcamRef}
                  className={'rounded-lg'}
                  setPermissionStatus={setVideoPermissionStatus}
                  needAudioAccess={false}
                />
              </div>
            </div>
          </div>
          <div className="py-2">
            <PermissionStatus permission={videoPermissionStatus} defaultText={'Check Video'} />
          </div>
        </div>
        <div className="h-full w-full space-y-4">
          <div className="relative">
            <div className="absolute -top-3 left-4 z-10 flex items-center gap-2 rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-700 dark:bg-green-900/20 dark:text-green-400">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              Microphone
            </div>
            <div className="h-full w-full rounded-lg border bg-card shadow-sm">
              <div className="relative mb-8 h-56 w-full rounded-lg">
                <AudioAnalyzer
                  permissionStatus={audioPermissionStatus}
                  setPermissionStatus={setAudioPermissionStatus}
                  soundDetected={soundDetected}
                  setSoundDetected={setSoundDetected}
                />
              </div>
            </div>
          </div>
          <div className="py-2">
            <PermissionStatus permission={audioPermissionStatus} defaultText={'Check Audio'} />
          </div>
        </div>
        <div className="h-full w-full space-y-4">
          <div className="relative">
            <div className="absolute -top-3 left-4 z-10 flex items-center gap-2 rounded-full bg-purple-100 px-3 py-1 text-sm font-medium text-purple-700 dark:bg-purple-900/20 dark:text-purple-400">
              <div className="h-2 w-2 rounded-full bg-purple-500"></div>
              Screen Share
            </div>
            <div className="h-full w-full rounded-lg border bg-card shadow-sm">
              <div className="relative mb-8 h-56 w-full rounded-lg">
                <video
                  ref={screenVideoRef}
                  id="screen-video"
                  className="h-56 w-full rounded-lg"
                  controls
                  playsInline
                  autoPlay={true}
                  muted={false}
                />
                {screenPermissionStatus?.status !== 'success' && (
                  <div className="flex h-full w-full flex-col items-center justify-center">
                    <Button onClick={requestScreenShare}>Enable Screen Share</Button>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="py-2">
            <PermissionStatus permission={screenPermissionStatus} />
          </div>
        </div>
      </div>
      <div className="mb-8 flex justify-center md:mb-0">
        <Button
          onClick={() => {
            if (isPermissionGranted) {
              if (onNext) {
                // Continue to next step in onboarding flow
                onNext();
              } else {
                // Original behavior for guidelines flow
                if (typeof window !== 'undefined') {
                  localStorage.removeItem('interview-guidelines-step');
                }
                if (!interviewDetails?.timing?.startTime) {
                  handleStartTest();
                }
                fullScreen.trigger();
                router.push(`/interview?id=${id}`);
              }
            }
          }}
          variant={isPermissionGranted ? 'default' : 'outline'}
          disabled={isStartLoading}
          size="lg"
          className="min-w-[200px]"
        >
          {isStartLoading && <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />}
          {onNext ? 'Continue' : permissionStatus}
        </Button>
      </div>
    </div>
  );
};

export default CheckPermissions;
