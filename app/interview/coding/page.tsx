import { redirect } from 'next/navigation';

import { CodingScreen } from '@/components/interview/coding-screen';
import { authOptions } from '@/lib/auth';
import { generateInterviewathonFeedback, getInterviewQuestion } from '@/services/apicall';
import { getUserProfile } from '@/services/apicall';
import { generateInterviewFeedback } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const id = props?.searchParams?.id;
  const session = await getServerSession(authOptions);

  const careerPractice = await getInterviewQuestion({
    id: id,
    userId: session?.userId,
  });

  if (careerPractice?.error) {
    return redirect('/404');
  }

  if (careerPractice?.hasCompleted || careerPractice?.timing?.completedTime) {
    if (!careerPractice?.timing?.feedBackGenerateTime) {
      if (!careerPractice?.isPlacement) {
        generateInterviewFeedback(id);
      } else {
        generateInterviewathonFeedback(id);
      }
    }
    return redirect(`/completed?id=${id}`);
  }
  if (careerPractice?.round === 'frontend-interview') {
    return redirect(`/interview/frontend?id=${id}`);
  }

  if (careerPractice?.round === 'multiple-choice') {
    return redirect(`/interview/multiple-choice?id=${id}`);
  }
  if (careerPractice?.round === 'written-interview') {
    return redirect(`/interview/written-interview?id=${id}`);
  }

  const codingQuestions = careerPractice?.questions?.filter(
    (item) => item?.round === 'coding-interview',
  );

  const practiceId = id;

  return (
    <CodingScreen
      questionType={codingQuestions}
      careerPractice={careerPractice}
      practiceId={practiceId}
    />
  );
}
