import { cookies } from 'next/headers';

import VideoCallInterview from '@/components/interview/video-call-interview';
import { addParticipant, getMeetingById } from '@/services/apicall';

export default async function DemoPage(props) {
  const meetingId = props?.searchParams?.id;
  const meeting = await getMeetingById({ id: meetingId, status: 'PENDING' });
  const userId = cookies().get('aceprepUserId')?.value;
  let authToken = meeting?.participants?.find((participant) => participant?.userId === userId)
    ?.meetingUserToken;
  if (!authToken) {
    const participant = await addParticipant(meeting?.meetingId, userId, 'group_call_host');
    authToken = participant?.id?.data?.token;
  }
  return <VideoCallInterview authToken={authToken} />;
}
