import { redirect } from 'next/navigation';

import { authOptions } from '@/lib/auth';
import { getInterviewQuestion } from '@/services/apicall';
import { getServerSession } from 'next-auth';

import { createParticipantToken } from './actions';
import PageClient from './page-client';

export default async function AIInterview(props) {
  const session = await getServerSession(authOptions);
  const id = props?.searchParams?.id;
  if (!session?.userId) {
    return redirect('/sign-in');
  }
  const careerPractice = await getInterviewQuestion({
    id: id,
    userId: session?.userId,
  });

  if (careerPractice?.error) {
    return redirect('/404');
  }

  if (careerPractice?.hasCompleted || careerPractice?.timing?.completedTime) {
    return redirect(`/completed?id=${id}`);
  }

  if (!careerPractice?.timing?.startTime) {
    return redirect(`/interview/onboarding?id=${id}`);
  }

  if (careerPractice?.round === 'coding-interview') {
    return redirect(`/interview/coding?id=${id}`);
  }

  if (careerPractice?.round === 'frontend-interview') {
    return redirect(`/interview/frontend?id=${id}`);
  }
  if (careerPractice?.round === 'multiple-choice') {
    return redirect(`/interview/multiple-choice?id=${id}`);
  }

  const roomName = `room-${Math.random().toString(36).substring(2, 15)}`;
  
  const token = await createParticipantToken(
    session?.name || 'Guest',
    JSON.stringify({
      role: careerPractice?.role,
      level: careerPractice?.level,
      name: session?.name || '',
      topics_to_cover: careerPractice?.customTopics,
      company_info_available: careerPractice?.jobDescription,
      interviewer_questions: careerPractice?.interviewerQuestions,
      interview_id: id,
      llm_provider: careerPractice?.llm_provider || 'google',
      video_avatar: careerPractice?.video_avatar || false,
      interview_round: careerPractice?.interview_round
    }),
    roomName,
  );

  return <PageClient token={token} id={id} />;
}
