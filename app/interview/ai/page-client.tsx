'use client';

import { useEffect, useState } from 'react';

import SimpleVoiceAssistant from '@/components/SimpleVoiceAssistant';
import useBeforeUnload from '@/hooks/client/useBeforeUnload';
import { AppLogo } from '@/layout/logo';
import { RoomContext } from '@livekit/components-react';
import '@livekit/components-styles';
import { LocalAudioTrack, Room, RoomEvent, TrackPublication,Track } from 'livekit-client';

const URL = process.env.NEXT_PUBLIC_LIVEKIT_URL;

if (!URL) {
  throw new Error('Missing LiveKit URL');
}

interface InterviewData {
  role?: string;
  level?: string;
  event?: string;
  jobDescription?: string;
}

export default function PageClient({ token, id }: { token: string; id: string }) {
  const [room] = useState(() => new Room());
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [interviewData, setInterviewData] = useState<InterviewData>({});

  // Fetch interview data
  useEffect(() => {
    const fetchInterviewData = async () => {
      try {
        const response = await fetch('/api/interview/get-question', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id }),
        });
        const data = await response.json();
        setInterviewData({
          role: data.role,
          level: data.level,
          event: data.event,
          jobDescription: data.jobDescription,
        });
      } catch (error) {
        console.error('Error fetching interview data:', error);
      }
    };

    if (id) {
      fetchInterviewData();
    }
  }, [id]);

  const handleCompleteInterview = async () => {
    try {
      await fetch('/api/livekit-connect/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          careerPracticeId: id,
        }),
      });
    } catch (error) {
      console.error('Error completing interview on page unload:', error);
    }
  };

  useBeforeUnload(handleCompleteInterview);

  useEffect(() => {
    const handleDeviceError = (error: Error) => onDeviceFailure(error);
    room.on(RoomEvent.MediaDevicesError, handleDeviceError);
    room.on(RoomEvent.LocalTrackPublished,async (trackPublication)=>{
      if(trackPublication.source===Track.Source.Microphone && trackPublication.track instanceof LocalAudioTrack){

        const {KrispNoiseFilter, isKrispNoiseFilterSupported} = await import('@livekit/krisp-noise-filter');
        if(!isKrispNoiseFilterSupported()){
          console.log('Krisp noise filter is not supported');
          return;
        }
        try
        {
          await trackPublication.track?.setProcessor(KrispNoiseFilter());
          console.log('Krisp noise filter applied');
        }
        catch(e)
        {
          void e;
          console.warn("Background Noise reduction cannot be enabled")
        }
        console.log('Microphone track published');
      } 
    })

    onConnectButtonClicked();

    return () => {
      room.off(RoomEvent.MediaDevicesError, handleDeviceError);
      if (isConnected) {
        room.disconnect();
      }
    };
  }, [room, isConnected]);

  const onConnectButtonClicked = async () => {
    try {
      await room.connect(URL, token, { autoSubscribe: true });
      await Promise.all([
        room.localParticipant.setMicrophoneEnabled(true),
        room.localParticipant.setCameraEnabled(true),
      ]);
      setIsConnected(true);
    } catch (err) {
      console.error('Connection failed:', err);
      setError('Failed to join the interview room');
    }
  };

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="rounded-lg bg-red-50 p-4 text-lg text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <main data-lk-theme="default" className="flex h-screen flex-col bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Interview Header with Position Info */}
      <div className="border-b border-white/20 backdrop-blur-sm bg-white/80 dark:bg-slate-900/80">
        <div className="flex w-full items-center justify-between px-4 py-2 sm:py-4">
          {/* Flinkk Hire Logo */}
          <AppLogo />

          {/* Position Information */}
          {(interviewData.role || interviewData.level) && (
            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  Position
                </div>
                <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                  {interviewData.role}
                </div>
                {interviewData.level && (
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {interviewData.level}
                  </div>
                )}
              </div>
              <div className="h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center">
                <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2M8 6v10a2 2 0 002 2h4a2 2 0 002-2V6" />
                </svg>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Interview Content */}
      <div className="flex-1 flex items-center justify-center p-6">
        <RoomContext.Provider value={room}>
          <div className="w-full max-w-6xl">
            <SimpleVoiceAssistant
              onConnectButtonClicked={onConnectButtonClicked}
              onCompleteHref={`/completed?id=${id}`}
              interviewId={id}
              interviewData={interviewData}
            />
          </div>
        </RoomContext.Provider>
      </div>
    </main>
  );
}

function onDeviceFailure(error: Error) {
  console.error('Device error:', error);
  alert(
    'Error acquiring camera or microphone permissions. Please:\n\n' +
      '1. Check your browser permissions\n' +
      '2. Ensure no other app is using your camera/mic\n' +
      '3. Reload the page',
  );
}
