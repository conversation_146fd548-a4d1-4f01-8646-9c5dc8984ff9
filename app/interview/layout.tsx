'use client';

import { useEffect, useRef, useState } from 'react';

import { usePathname, useSearchParams } from 'next/navigation';

import Modal from '@/components/Modal/modal';
import useBeforeUnload from '@/hooks/client/useBeforeUnload';
import { useScreenShareStore } from '@/packages/shared-store/src/screen-share';
import { v4 as uuid } from 'uuid';
import { ReliableCompositeRecorder } from '@/utils/reliable-composite-recording';

import { Button } from '@camped-ui/button';

const bucket = process.env.NEXT_PUBLIC_S3FOLDER || '';
export default function Layout({ children }) {
  const searchParams: any = useSearchParams();
  const id = searchParams?.get('id');
  const pathName = usePathname();
  const path = pathName?.split('/')?.pop();
  const [screen, setScreen]: any = useState('');

  const updateScreenShare = useScreenShareStore((state) => (state as any).update);
  const screenStream: any = useRef(null);
  const stream: any = useRef(null);
  useBeforeUnload(() => {
    if (screenStream.current) screenStream?.current?.getTracks()?.forEach((track) => track.stop());
    if (stream.current) stream?.current?.getTracks().forEach((track) => track.stop());
  });
  useEffect(() => {
    if (path === 'video-call' || path === 'ai') return;
    const startStreaming = async () => {
      try {
        // Capture video and audio
        stream.current = await navigator.mediaDevices.getUserMedia({
          audio: true,
          video: {
            width: { ideal: 640 }, // Ideal width (e.g., 640px)
            height: { ideal: 480 }, // Ideal height (e.g., 480px)
            frameRate: { ideal: 15, max: 30 }, // Reduce frame rate
          },
        });
        screenStream.current = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: 1280 }, // Ideal screen share width
            height: { ideal: 720 }, // Ideal screen share height
            frameRate: { ideal: 15, max: 30 }, // Reduce frame rate
          },
        });
        updateScreenShare(screenStream.current);
        const videoTrack = screenStream.current.getVideoTracks()[0];
        const settings = videoTrack.getSettings();

        setScreen(settings.displaySurface);
        if (settings.displaySurface === 'monitor') {
          const unique_id = `${uuid()}`;
          // Use new composite recording instead of dual recordings
          startCompositeRecording(stream.current, screenStream.current, unique_id, id);
        }
        videoTrack.addEventListener('ended', () => {
          console.log('Screen sharing stopped');
          setScreen('');
        });
      } catch (error) {
        console.error('Error starting stream:', error);
      }
    };

    startStreaming();
    return () => {
      if (path === 'video-call' || path === 'ai') return;

      // Cleanup reliable recorder if it exists
      if ((window as any).reliableRecorder) {
        (window as any).reliableRecorder.cleanup();
        (window as any).reliableRecorder = null;
      }

      if (screenStream.current)
        screenStream?.current?.getTracks()?.forEach((track) => track.stop());
      if (stream.current) stream?.current?.getTracks().forEach((track) => track.stop());
    };
  }, []);

  if (screen !== 'monitor' && path !== 'guidelines' && path !== 'onboarding' && path !== 'video-call' && path !== 'ai') {
    return (
      <main className={`relative flex h-full w-full flex-1 flex-col`}>
        <div className={`relative h-full min-h-screen w-full overflow-x-hidden`}>
          <Modal showModal={true} setShowModal={() => {}}>
            <div className="w-full overflow-hidden bg-background md:max-w-md md:rounded-2xl md:border md:shadow-xl">
              <div className="flex flex-col items-center justify-center space-y-3 bg-background px-4 py-6 pt-8 text-center md:px-12">
                <div className="flex w-full max-w-[640px] flex-col items-center justify-center text-center">
                  <h3 className="w-full max-w-[600px] text-lg font-semibold">Warning</h3>
                  <p className="mt-4 text-sm text-muted-foreground">
                    Please select entire screen mode to ensure a smooth Interview experience
                  </p>
                  <br />

                  <br />
                  <Button onClick={() => window.location.reload()}>Refresh</Button>
                </div>
              </div>
            </div>
          </Modal>
        </div>
      </main>
    );
  }
  return (
    <main className={`relative flex h-full w-full flex-1 flex-col ${path === 'onboarding' ? 'overflow-y-auto' : 'overflow-hidden'}`}>
      <div className={`relative h-full min-h-screen w-full overflow-x-hidden`}>{children}</div>
    </main>
  );
}

async function startRecording(stream, id, cpId, otherStream, isWebcam = true) {
  let buffer: any = [];
  let bufferSize = 0;
  const mediaRecorder = new MediaRecorder(stream);
  const s3Key = `${bucket}/interview-recordings/${cpId}/${id}-${
    isWebcam ? 'webcam' : 'screen'
  }.mp4`;

  let { uploadId } = await initializeMultipartUpload(s3Key);
  let partNumber = 1;
  const uploadedParts: { ETag: string; PartNumber: number }[] = [];

  mediaRecorder.ondataavailable = async (event) => {
    if (event.data.size > 0) {
      buffer.push(event.data);
      bufferSize += event.data.size;

      if (bufferSize >= 5 * 1024 * 1024) {
        const chunk = new Blob(buffer);
        if (!uploadId) {
          uploadId = (await initializeMultipartUpload(s3Key))?.uploadId;
        }
        try {
          const part = await uploadChunkWithPresignedUrl(chunk, partNumber, uploadId, s3Key);
          buffer = [];
          bufferSize = 0;
          if (part) uploadedParts.push(part);
          partNumber += 1; // Increment part number
        } catch (error) {
          console.error('Error uploading chunk with presigned URL:', error);
        }
      }
    }
  };

  mediaRecorder.onstop = async () => {
    console.log('Recording stopped. Finalizing upload...');
    if (!isWebcam) {
      if (otherStream.current) otherStream?.current?.getTracks().forEach((track) => track.stop());
    }
    if (bufferSize > 0) {
      const chunk = new Blob(buffer);
      try {
        const part = await uploadChunkWithPresignedUrl(chunk, partNumber, uploadId, s3Key);
        if (part) uploadedParts.push(part);
      } catch (error) {
        console.error('Error uploading remaining chunk:', error);
      }
    }

    // Finalize the upload by calling the server API
    try {
      const result = await finalizeUploadOnServer(uploadId, uploadedParts, s3Key);
      console.log('Upload finalized:', result);
    } catch (error) {
      console.error('Error finalizing upload on server:', error);
    }
  };

  mediaRecorder.start(60000); // Emit data every 60 seconds
}

async function getPresignedUrl(partNumber, uploadId, s3Key) {
  const response = await fetch('/api/interview/candidate/get-multipart-presigned-url', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      bucketName: process.env.NEXT_PUBLIC_S3BUCKET ?? 'aceprep-bucket',
      objectKey: s3Key,
      partNumber,
      uploadId,
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to fetch presigned URL');
  }
  const data = await response.json();
  return data.presignedUrl;
}

async function uploadChunkWithPresignedUrl(chunk, partNumber, uploadId, s3Key) {
  const presignedUrl = await getPresignedUrl(partNumber, uploadId, s3Key);
  const resp = await fetch(presignedUrl, {
    method: 'PUT',
    headers: {
      'Content-Type': 'video/mp4',
    },
    body: chunk,
  });

  if (!resp.ok) {
    throw new Error(`Failed to upload chunk: ${resp.statusText}`);
  }

  // Get the ETag from response headers
  const eTag = resp.headers.get('ETag');
  if (!eTag) {
    throw new Error('ETag not returned in response headers');
  }

  console.log(`Uploaded part ${partNumber}, ETag: ${eTag}`);

  return { ETag: eTag.replace(/"/g, ''), PartNumber: partNumber }; // Remove quotes from ETag if present
}

async function finalizeUploadOnServer(uploadId, uploadedParts, s3Key) {
  const response = await fetch('/api/interview/candidate/finalize-multipart-upload', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      bucketName: process.env.NEXT_PUBLIC_S3BUCKET ?? 'aceprep-bucket', // Your bucket name
      objectKey: s3Key, // S3 object key for the file
      uploadId,
      parts: uploadedParts,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to finalize upload on server');
  }

  return await response.json();
}
const initializeMultipartUpload = async (s3Key) => {
  const response = await fetch('/api/interview/candidate/initialize-multipart-upload', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      bucketName: process.env.NEXT_PUBLIC_S3BUCKET ?? 'aceprep-bucket', // Your bucket name
      objectKey: s3Key,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to finalize upload on server');
  }

  return await response.json();
};

// New composite recording function that combines webcam + screen into one video
async function startCompositeRecording(
  webcamStream: MediaStream,
  screenStream: MediaStream,
  sessionId: string,
  cpId: string
) {
  console.log('🎬 Starting composite interview recording...');

  const recorder = new ReliableCompositeRecorder({
    webcamPosition: 'bottom-left',
    webcamSize: 'medium',
    onProgress: (progress, stage) => {
      console.log(`📊 Reliable recording ${stage}: ${progress.toFixed(1)}%`);
    },
    onError: (error) => {
      console.error('❌ Reliable recording error:', error);
    },
    onChunkUploaded: (chunkId) => {
      console.log('✅ Chunk uploaded:', chunkId);
    },
  });

  const success = await recorder.startRecording(webcamStream, screenStream, sessionId, cpId);

  if (!success) {
    console.error('Failed to start composite recording, falling back to dual recording');
    // Fallback to original dual recording if composite fails
    const unique_id = `${sessionId}`;
    startRecording(screenStream, unique_id, cpId, { current: webcamStream }, false);
    startRecording(webcamStream, unique_id, cpId, { current: screenStream }, true);
  }

  // Store recorder reference for cleanup and debugging
  (window as any).reliableRecorder = recorder;

  // Add debug helpers
  (window as any).debugRecording = {
    getStatus: () => recorder.getStatus(),
    forceUpload: () => recorder.forceUpload(),
    checkRecorder: () => {
      console.log('🔍 Recorder status:', recorder.getStatus());
      return recorder.getStatus();
    }
  };

  console.log('🎬 Reliable recorder initialized. Debug with: window.debugRecording.getStatus()');
}
