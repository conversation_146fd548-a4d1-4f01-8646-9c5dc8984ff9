import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { FrontendScreen } from '@/components/interview/frontend-screen';
import { generateInterviewFeedback, generateInterviewathonFeedback } from '@/services/apicall';
import { getInterviewQuestion, getUserProfile } from '@/services/apicall';

export default async function Home(props) {
  const id = props?.searchParams?.id;
  const userId = cookies().get('aceprepUserId')?.value;
  let action = '';

  const careerPractice = await getInterviewQuestion({
    id: id,
    userId: userId,
  });

  if (careerPractice?.error) {
    return redirect('/404');
  }

  if (careerPractice?.hasCompleted || careerPractice?.timing?.completedTime) {
    if (!careerPractice?.timing?.feedBackGenerateTime) {
      if (!careerPractice?.isPlacement) {
        generateInterviewFeedback(id);
      } else {
        generateInterviewathonFeedback(id);
      }
    }
    return redirect(`/completed?id=${id}`);
  }
  if (careerPractice?.round === 'written-interview') {
    return redirect(`/interview/written-interview?id=${id}`);
  }

  if (careerPractice?.round === 'multiple-choice') {
    return redirect(`/interview/multiple-choice?id=${id}`);
  }
  const userProfile = await getUserProfile(userId);

  if (!userProfile) {
    action = 'update-profile';
  }

  const frontendQuestions = careerPractice?.questions?.filter(
    (item) => item?.round === 'frontend-interview',
  );

  const practiceId = id;

  return (
    <FrontendScreen
      frontendQuestions={frontendQuestions}
      careerPractice={careerPractice}
      practiceId={practiceId}
      action={action}
    />
  );
}
