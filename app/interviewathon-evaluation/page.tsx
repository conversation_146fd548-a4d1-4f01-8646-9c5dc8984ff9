import { redirect } from 'next/navigation';

import { InterviewDurationCard } from '@/components/cards/admin/interview-duration';
import RefreshCard from '@/components/cards/refresh-card';
import PageHeader from '@/components/page-header';
import { InterviewEvaluationScreen } from '@/components/wrapper-screen/interview-evaluation-screen';
import { VideoInterviewDetailScreen } from '@/components/wrapper-screen/interview/video-interview-detail-screen';
import { authOptions } from '@/lib/auth';
import { getUserInterviewDetails, getUserProfile } from '@/services/apicall';
import { convertToIST } from '@/utils/utc-to-ist';
import { getServerSession } from 'next-auth';

import { Card, CardDescription } from '@camped-ui/card';

export default async function DemoPage(props) {
  const session = await getServerSession(authOptions);

  const id = props?.searchParams?.id;

  const response = await getUserInterviewDetails(id, session?.userId);
  const userProfile = await getUserProfile(session?.userId);

  if (response?.error) {
    redirect('/404');
  }
  if (!response.items?.timing?.feedBackGenerateTime) {
    return (
      <RefreshCard
        showRefresh={true}
        buttonTitle={null}
        href={null}
        description={
          'We are in the process of generating your feedback, which typically takes around two to three minutes. Kindly wait a moment.'
        }
      />
    );
  }

  // Determine if this is an AI interview based on eventDetails.isAiQuestion
  const isAiInterview = response?.items?.eventDetails?.isAiQuestion === true;

  // For AI interviews, check if there's video content (conversation with s3Id)
  const hasVideoContent = response?.items?.conversation?.some((item: any) => item?.s3Id);

  // For regular interviews, check if there's a room_name (video meeting)
  const hasVideoMeeting = response?.items?.conversation?.[0]?.room_name;

  return (
    <>
      <PageHeader hasBack title={response?.items?.event} />
      <div className="mt-4 flex w-full flex-col gap-4 lg:flex lg:flex-row">
        <div className="flex w-full max-w-[800px] flex-col gap-4">
          {/* Show VideoInterviewDetailScreen for AI interviews with video content OR regular interviews with video meetings */}
          {(isAiInterview && hasVideoContent) || (!isAiInterview && hasVideoMeeting) ? (
            <VideoInterviewDetailScreen
              meeting={{
                ...response.items,
                s3RecordingId: response.items.conversation?.[0]?.s3Id,
                role: response.items?.role,
                level: response.items?.level,
                organizationId: response.items?.eventDetails?.organizationId
              }}
              userId={''}
              isAiInterview={isAiInterview}
            />
          ) : (
            <InterviewEvaluationScreen
              careerPractice={response.items}
              session={{ ...session, updatedAt: userProfile?.updatedAt }}
            />
          )}
        </div>
        <div className="flex max-w-[320px] flex-col gap-4">
          <Card className="p-4">
            {response?.items?.event ? (
              <CardDescription className="mb-2 text-left">
                <span className="font-bold">Event:</span> {response?.items?.event}
              </CardDescription>
            ) : null}

            <CardDescription className="mb-2">
              <span className="font-bold">Role:</span> {response?.items?.role}
            </CardDescription>
            <CardDescription>
              <span className="font-bold">Level:</span> {response?.items?.level}
            </CardDescription>
          </Card>
          <InterviewDurationCard
            title={'Interviewathon Duration'}
            startTime={convertToIST(response.items?.timing?.startTime, false)}
            completedTime={convertToIST(response.items?.timing?.completedTime, false)}
          />
        </div>
      </div>
    </>
  );
}
