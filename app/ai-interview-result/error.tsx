'use client';

import { useEffect } from 'react';

import { useRouter } from 'next/navigation';

import WarningSvg from '@/components/illustrations/warning';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="flex h-screen flex-col items-center justify-center px-4">
      <WarningSvg />
      <h2 className="mt-6 text-2xl font-semibold text-red-500">Interview Results Unavailable</h2>
      <p className="mt-4 max-w-md text-center text-muted-foreground">
        {error.message || "We couldn't find the interview results you're looking for."}
      </p>
    </div>
  );
}
