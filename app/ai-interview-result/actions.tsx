'use server';

import { getSignedUrl } from '@/pages/api/gcp-bucket';
import { listFolderContents } from '@/pages/api/upload-url';
import { db } from '@/prisma/db';

const baseUrl = process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL;

export async function getResult(id) {
  if (!id) {
    throw new Error('Please provide an interview ID to view results');
  }

  try {
    const response = await db.careerPractice.findFirst({
      where: {
        references_id: id,
      },
      include: {
        eventDetails: true,
      },
    });

    console.log({ response });

    if (!response) {
      throw new Error(
        'Interview results not found. Please ensure you have the correct interview ID.',
      );
    }

    // Assuming careerPractice is your data structure containing conversation
    const conversation = response?.conversation;
    const organizationId = response?.eventDetails?.organizationId;

    // Use Promise.all to concurrently fetch the pre-signed URLs
    const mappedResponses = await Promise.all(
      (Array.isArray(conversation) ? conversation : [])?.map(async (response) => {
        try {
          if (
            typeof response === 'object' &&
            response !== null &&
            's3Id' in response &&
            response.s3Id
          ) {
            let videoUrl;
            if (response?.videoOrigin === 'GCP_BUCKET') {
              videoUrl = await getSignedUrl(response?.s3Id, organizationId);
            } else {
              videoUrl = `${process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_S3FOLDER}/${response.s3Id}`;
            }
            return {
              ...response,
              videoUrl,
            };
          } else {
            // Handle cases where response or response.s3Id is missing or falsy.
            // You can choose to return some default value or handle the error accordingly.
            if (typeof response === 'object' && response !== null) {
              return {
                ...response,
                videoUrl: null, // or any default value
              };
            } else {
              return {
                videoUrl: null,
              };
            }
          }
        } catch (error) {
          // Handle errors that occur while fetching the pre-signed URL.
          // You can choose to throw the error, log it, or handle it as needed.
          console.error(`Error fetching pre-signed URL for response:`, error);
          throw error; // Optionally rethrow the error if needed.
        }
      }),
    );
    const Video_recordings: any = await listFolderContents(id);
    const separatedVideos: any = {
      screen: [],
      webcam: [],
    };

    Video_recordings?.Contents?.forEach((recording) => {
      if (recording.Key.includes('screen')) {
        separatedVideos.screen.push(`${baseUrl}/${recording.Key}`);
      } else if (recording.Key.includes('webcam')) {
        separatedVideos.webcam.push(`${baseUrl}/${recording.Key}`);
      } else if (recording.Key.includes('composite')) {
        // Composite videos contain both screen and webcam, show in screen section
        separatedVideos.screen.push(`${baseUrl}/${recording.Key}`);
      }
    });
    return {
      result: {
        ...response,
        conversation: mappedResponses,
        videoRecordings: separatedVideos,
      },
    };
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to fetch interview results. Please try again later.');
  }
}
