import InProgressSvg from '@/components/illustrations/in-progress';
import { InterviewEvaluationScreen } from '@/components/wrapper-screen/interview-evaluation-screen';

import { getResult } from './actions';

export default async function DemoPage(props) {
  const id = props?.searchParams?.id;

  if (!id) {
    throw new Error('Interview ID is missing');
  }

  const result = await getResult(id);
  const careerPractice = result?.result;

  if (careerPractice?.interviewStatus === 'COMPLETED') {
    return (
      <div className="container mx-auto flex flex-col gap-4 p-4">
        <InterviewEvaluationScreen careerPractice={result.result} session={null} />
      </div>
    );
  }

  return (
    <div className="flex h-screen flex-col items-center justify-center px-4">
      <InProgressSvg />
      <h2 className="mt-6 text-2xl font-semibold text-primary">Interview In Progress</h2>
      <p className="mt-4 max-w-md text-center text-muted-foreground">
        The interview is currently in progress or hasn&apos;t been completed yet. Please check back
        later to view the results.
      </p>
    </div>
  );
}
