'use client';

import { EnhancedDashboard } from '@/components/dashboard/enhanced-dashboard';

export default function DashboardDemo() {
  // Demo with a sample organization ID
  const demoOrganizationId = 'demo-org-id';

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Enhanced Dashboard Demo</h1>
          <p className="text-lg text-gray-600">
            Preview of the new comprehensive dashboard with real-time metrics
          </p>
        </div>
        
        <EnhancedDashboard
          organizationId={demoOrganizationId}
          membershipId="demo-membership-id"
          role="admin"
        />
      </div>
    </div>
  );
}
