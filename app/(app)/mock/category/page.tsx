import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { MockInterviewScreen } from '@/components/interview/mock/mock-interview-screen';
import { authOptions } from '@/lib/auth';
import { fetchQuestionType } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage() {
  const session = await getServerSession(authOptions);
  const questionType = (await fetchQuestionType()) ?? [];
  return <MockInterviewScreen session={session} questionType={questionType} />;
}
