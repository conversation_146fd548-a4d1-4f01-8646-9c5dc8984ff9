import { TopicScreen } from '@/components/wrapper-screen/quiz-topic-screen';
import { getQuizCategory, getQuizPracticeQuestion, getQuizTopics } from '@/services/apicall';

export default async function DemoPage(props) {
  const category = props?.params?.category;

  const topic = props?.params?.topic;

  const quiz = await getQuizPracticeQuestion(category, topic);
  const allCategory = await getQuizCategory();
  const selectedTopics = await getQuizTopics(category);

  return (
    <TopicScreen
      quiz={quiz?.result}
      categoryName={allCategory?.find((item) => item?.slug === category)?.name}
      topicName={selectedTopics?.find((item) => item?.slug === topic)?.name}
    />
  );
}
