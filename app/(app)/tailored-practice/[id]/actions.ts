'use server';

import { AccessToken, AccessTokenOptions, VideoGrant } from 'livekit-server-sdk';

const API_KEY = process.env.LIVEKIT_API_KEY;
const API_SECRET = process.env.LIVEKIT_API_SECRET;
const LIVEKIT_URL = process.env.LIVEKIT_URL;

if (!LIVEKIT_URL || !API_KEY || !API_SECRET) {
  throw new Error('Missing LiveKit environment variables');
}

export async function createParticipantToken(
  identity: string,
  metadata: any,
  roomName: string,
): Promise<string> {
  const token = new AccessToken(API_KEY!, API_SECRET!, {
    identity: identity,
    metadata: metadata,
    ttl: '15m',
  });

  const grant: VideoGrant = {
    room: roomName,
    roomJoin: true,
    canPublish: true,
    canPublishData: true,
    canSubscribe: true,
  };

  token.addGrant(grant);

  return await token.toJwt();
}
