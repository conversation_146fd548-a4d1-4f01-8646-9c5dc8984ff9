'use client';

import { useEffect, useState } from 'react';

import SimpleVoiceAssistant from '@/components/SimpleVoiceAssistant';
import useBeforeUnload from '@/hooks/client/useBeforeUnload';
import { RoomContext } from '@livekit/components-react';
import '@livekit/components-styles';
import { Room, RoomEvent } from 'livekit-client';

const URL = process.env.NEXT_PUBLIC_LIVEKIT_URL;

if (!URL) {
  throw new Error('Missing LiveKit URL');
}

export default function PageClient({ token, id }: { token: string; id: string }) {
  const [room] = useState(() => new Room());
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleCompleteInterview = async () => {
    try {
      await fetch('/api/livekit-connect/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          careerPracticeId: id,
        }),
      });
    } catch (error) {
      console.error('Error completing interview on page unload:', error);
    }
  };

  useBeforeUnload(handleCompleteInterview);

  useEffect(() => {
    const handleDeviceError = (error: Error) => onDeviceFailure(error);
    room.on(RoomEvent.MediaDevicesError, handleDeviceError);

    onConnectButtonClicked();

    return () => {
      room.off(RoomEvent.MediaDevicesError, handleDeviceError);
      if (isConnected) {
        room.disconnect();
      }
    };
  }, [room, isConnected]);

  const onConnectButtonClicked = async () => {
    try {
      await room.connect(URL!, token, { autoSubscribe: true });
      await Promise.all([
        room.localParticipant.setMicrophoneEnabled(true),
        room.localParticipant.setCameraEnabled(true),
      ]);
      setIsConnected(true);
    } catch (err) {
      console.error('Connection failed:', err);
      setError('Failed to join the interview room');
    }
  };

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="rounded-lg bg-red-50 p-4 text-lg text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <main data-lk-theme="default" className="grid h-full content-center bg-background">
      <RoomContext.Provider value={room}>
        <div className="lk-room-container mx-auto max-h-[90vh] w-[90vw] max-w-[1024px]">
          <SimpleVoiceAssistant
            onConnectButtonClicked={onConnectButtonClicked}
            onCompleteHref={`/tailored-practice/${id}/result`}
            interviewId={id}
          />
        </div>
      </RoomContext.Provider>
    </main>
  );
}

function onDeviceFailure(error: Error) {
  console.error('Device error:', error);
  alert(
    'Error acquiring camera or microphone permissions. Please:\n\n' +
    '1. Check your browser permissions\n' +
    '2. Ensure no other app is using your camera/mic\n' +
    '3. Reload the page',
  );
}
