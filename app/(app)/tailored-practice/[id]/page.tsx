import { cookies } from 'next/headers';

import { authOptions } from '@/lib/auth';
import { getTailoredTest } from '@/services/apicall';
import { getServerSession } from 'next-auth';

import { createParticipantToken } from './actions';
import PageClient from './page-client';

export default async function DemoPage(props) {
  const id = props.params.id;

  const session = await getServerSession(authOptions);
  console.log({ session });
  const userId = cookies()?.get('aceprepUserId')?.value;

  const careerPractice = await getTailoredTest({
    id: id,
    sub: userId || 'Visitor',
  });

  console.log({ careerPractice });

  const practiceId = props.params.id;

  const roomName = `room-${Math.random().toString(36).substring(2, 15)}`;

  const token = await createParticipantToken(
    session?.name || 'Guest',
    JSON.stringify({
      role: careerPractice?.role,
      level: careerPractice?.level,
      name: session?.name || '',
      interview_id: practiceId,
    }),
    roomName,
  );

  console.log({ token });

  return <PageClient token={token} id={id} />;
}
