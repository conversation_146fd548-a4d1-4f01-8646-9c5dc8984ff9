import { cookies } from 'next/headers';

import NotFound from '@/app/not-found';
import RefreshCard from '@/components/cards/refresh-card';
import { TailoredResultScreen } from '@/components/interview/tailored-practice/tailored-result-screen';
import PageHeader from '@/components/page-header';
import { authOptions } from '@/lib/auth';
import { getTailoredPracticesResult } from '@/services/apicall';
import { getServerSession } from 'next-auth';

// Disable caching for this page
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export default async function DemoPage(props) {
  const id = props.params.id;
  const userId = cookies()?.get('aceprepUserId')?.value;
  const tenantId = cookies().get('aceprepTenantId')?.value;

  const session: any = await getServerSession(authOptions);

  const currentOrg = (session?.memberships as any)?.find(
    (item) => item?.organizationId === tenantId,
  );
  let careerPractice;

  if (session?.memberships?.length > 0 && currentOrg?.role !== 'STUDENT') {
    careerPractice = await getTailoredPracticesResult({
      id: id,
      sub: userId || 'Visitor',
      isAdmin: true,
    });
  } else {
    careerPractice = await getTailoredPracticesResult({
      id: id,
      sub: userId || 'Visitor',
      isAdmin: false,
    });
  }

  if (careerPractice?.error) {
    return <NotFound />;
  }

  // Debug logging for server-side
  console.log('🔍 Server-side Debug:', {
    hasCompleted: careerPractice?.hasCompleted,
    hasFeedback: !!careerPractice?.result?.feedback,
    feedbackKeys: careerPractice?.result?.feedback ? Object.keys(careerPractice?.result?.feedback) : [],
    overallScore: careerPractice?.result?.feedback?.overall_score,
  });

  if (!careerPractice?.hasCompleted && careerPractice?.id) {
    return (
      <RefreshCard
        showRefresh={false}
        buttonTitle={'Resume Interview'}
        href={`/tailored-practice/${careerPractice?.id}`}
        description={
          'Your interview is not yet completed. To continue, please click the Resume Interview button below.'
        }
      />
    );
  }
  return (
    <>
      <PageHeader hasBack title={careerPractice?.role} />
      <div
        className={`mx-auto mt-2 flex w-full ${
          !careerPractice?.result?.feedback ? '' : 'max-w-[800px]'
        } flex-col gap-4`}
      >
        <TailoredResultScreen careerPractice={careerPractice} session={session} />
      </div>
    </>
  );
}
