import { cookies } from 'next/headers';

import { StudentEnhancedDashboard } from '@/components/dashboard/student-enhanced-dashboard';
import { IssuesStats } from '@/components/workspace/issues-stats';
import { authOptions } from '@/lib/auth';
import { getActivityGraph, getPracticesByUserId, leaderboardDetails } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function App(props) {
  const session: any = await getServerSession(authOptions);

  const userId = cookies().get('aceprepUserId')?.value ?? session?.userId;
  const organizationId =
    cookies().get('aceprepTenantId')?.value ?? session?.memberships?.[0]?.organizationId;

  let activityStatus, currentUser;

  if (userId && !organizationId) {
    activityStatus = await getActivityGraph(userId);
  }

  if (userId && organizationId) {
    [activityStatus, currentUser] = await Promise.all([
      getPracticesByUserId(userId),
      organizationId
        ? leaderboardDetails({
            organizationId: organizationId,
            fetchFirstThree: false,
            searchParams: { page: 1, per_page: 1 },
            userId: userId,
          })
        : null,
    ]);
  }

  // For individual students (not part of organization), show enhanced dashboard
  if (userId && !organizationId) {
    return <StudentEnhancedDashboard userId={userId} />;
  }

  // For organization students, keep the existing dashboard
  return (
    <IssuesStats
      data={{ ...activityStatus, rank: currentUser?.user?.rank }}
      student={organizationId}
    />
  );
}
