import { redirect } from 'next/navigation';

import { GlobalPanel } from '@/layout/global-panel';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function Layout({
  activity,
  insight,
  myinterview,
  bannerNotification,
}) {
  const session: any = await getServerSession(authOptions);
  const studentMembership = (session?.memberships as any)?.filter(
    (item) => item?.role === 'STUDENT',
  );
  if ((session?.memberships as any)?.length > 0 && studentMembership?.length === 0) {
    redirect('/home');
  }

  return (
    <GlobalPanel title="Dashboard">
      {bannerNotification}
      {activity}
      <h3 className="mt-4 scroll-m-20 text-2xl font-medium tracking-wide">Insights</h3>
      <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">{insight}</div>
      {myinterview}
    </GlobalPanel>
  );
}
