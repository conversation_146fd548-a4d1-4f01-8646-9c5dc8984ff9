import QuestionCard from '@/components/skeleton/question-card';

export default function Loading() {
  const insight = [1, 2, 3];
  return (
    <>
      <h3 className="mt-4 scroll-m-20 text-2xl font-medium tracking-wide">My Interviews</h3>
      <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {insight.map((item, index) => (
          <QuestionCard key={index} />
        ))}
      </div>
    </>
  );
}
