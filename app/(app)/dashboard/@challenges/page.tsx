import Link from 'next/link';

import { getChallenges } from '@/services/apicall';

import { Button } from '@camped-ui/button';
import { Card, CardFooter } from '@camped-ui/card';

export default async function App(props) {
  const insights = await getChallenges();

  return (
    <>
      {insights?.map((item, index) => (
        <Link href={item?.navigationPath} key={index}>
          <Card key={index} className="p-4">
            {item?.title}
            <CardFooter className="p-0">
              <Button variant="link" className="p-0">
                {item?.buttonLabel}
              </Button>
            </CardFooter>
          </Card>
        </Link>
      ))}
    </>
  );
}
