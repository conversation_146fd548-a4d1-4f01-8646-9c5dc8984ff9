import { cookies } from 'next/headers';

import { BannerCard } from '@/components/cards/banner-card';
import { authOptions } from '@/lib/auth';
import { getOrganizationById } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function App(props) {
  const session: any = await getServerSession(authOptions);

  const student: any = (session as any)?.membership?.find(
    (membership: any) => membership?.role === 'STUDENT',
  );
  const userId = cookies()?.get('aceprepUserId')?.value ?? session?.userId;

  let organization;
  if (student && userId) {
    organization = await getOrganizationById(student?.organizationId, userId);
  }

  return (
    <>
      {organization?.bannerNotification?.map((item, index) => {
        return <BannerCard key={index} bannerNotification={item} />;
      })}
    </>
  );
}
