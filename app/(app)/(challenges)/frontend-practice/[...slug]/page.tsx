import { cookies } from 'next/headers';

import { FrontendProblemScreen } from '@/components/wrapper-screen/frontend-problem-screen';
import { authOptions } from '@/lib/auth';
import {
  getFrontEndPractice,
  getSavedFrontendProblem,
  getUserSavedFrontendProblem,
} from '@/services/apicall';

export default async function Home(props) {
  const userId = cookies()?.get('aceprepUserId')?.value ?? 'Visitor';
  const organizationId = cookies()?.get('aceprepTenantId')?.value;
  const slug = props.params.slug ?? '';
  const problem = await getFrontEndPractice(slug);
  const savedCode = userId === 'Visitor' ? [] : await getUserSavedFrontendProblem(slug, userId);

  return (
    <FrontendProblemScreen
      problem={problem?.result}
      userId={userId}
      savedCode={savedCode?.problem}
      organizationId={organizationId}
    />
  );
}
