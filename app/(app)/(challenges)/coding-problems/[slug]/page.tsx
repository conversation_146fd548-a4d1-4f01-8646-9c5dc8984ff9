import { cookies } from 'next/headers';

import NotFound from '@/app/not-found';
import { CodingSlugScreen } from '@/components/wrapper-screen/coding-slug-screen';
import { authOptions } from '@/lib/auth';
import { getCodingProblems } from '@/services/apicall';
import { getServerSession } from 'next-auth';

const App = async (prop) => {
  const slug = prop.params.slug ?? '';
  const userId = cookies()?.get('aceprepUserId')?.value ?? 'Visitor';
  const organizationId = cookies()?.get('aceprepTenantId')?.value;
  const problem = await getCodingProblems(slug);

  if (!problem?.result) {
    return <NotFound />;
  }

  return (
    <CodingSlugScreen userId={userId} problem={problem?.result} organizationId={organizationId} />
  );
};

export default App;
