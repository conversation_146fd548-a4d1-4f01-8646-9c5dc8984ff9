import React from 'react';

import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { LeaderboardTable } from '@/components/data-table/leaderboard/data-table';
import { authOptions } from '@/lib/auth';
import { CampedTableProvider } from '@/packages/shared-data-table/camped-table-provider';
import { getAllDepartment, getAllGroup, leaderboardDetails } from '@/services/apicall';
import { getServerSession } from 'next-auth';

import { Card } from '@camped-ui/card';

export type DocumentsPageProps = {
  searchParams?: {
    status?: string;
  };
};

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  let organizationId, userId;
  const searchParams = props.searchParams;
  const student: any = session?.memberships?.find(
    (membership: any) => membership?.role === 'STUDENT',
  );

  organizationId = cookies().get('aceprepTenantId')?.value ?? student?.organizationId;
  const currentOrg: any = session?.memberships?.find(
    (membership: any) => membership?.organizationId === organizationId,
  );

  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());

  if (!student && currentOrg?.organization?.type !== 'institution') {
    redirect('/404');
  }
  userId = cookies().get('aceprepUserId')?.value;

  const [leaderboardDetail, currentUser, department, group] = await Promise.all([
    leaderboardDetails({
      organizationId,
      fetchFirstThree: false,
      searchParams: {
        memberShipId: isSuperUser ? undefined : currentOrg?.id,
        ...searchParams,
      },
    }),
    student
      ? leaderboardDetails({
          organizationId,
          fetchFirstThree: false,
          searchParams: {
            memberShipId: isSuperUser ? undefined : currentOrg?.id,
            ...searchParams,
          },
          userId: userId,
        })
      : null,
    getAllDepartment({ organizationId, membershipId: isSuperUser ? undefined : currentOrg?.id }),
    getAllGroup({
      organizationId,
      membershipId: isSuperUser ? undefined : currentOrg?.id,
    }),
  ]);

  return (
    <CampedTableProvider>
      <LeaderboardTable
        isSuperUser={isSuperUser}
        department={department}
        tasksPromise={{
          data: leaderboardDetail,
        }}
        isStudent={student?.role === 'STUDENT'}
        currentUser={currentUser?.user}
        group={group}
        searchDepartment={searchParams?.department}
      />
    </CampedTableProvider>
  );
}
