import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { MyInterviewthonScreen } from '@/components/my-interviews/my-interviewthon-screen';
import { authOptions } from '@/lib/auth';
import { getActiveInterviewathon, getMyInterviewthon } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function App(props) {
  const tab = props.searchParams.tab ?? 'active';
  const session: any = await getServerSession(authOptions);
  const userId = cookies()?.get('aceprepUserId')?.value;
  const student: any = session?.memberships?.find(
    (membership: any) => membership?.role === 'STUDENT',
  );

  if (student?.length === 0 || !student || !userId) {
    return redirect('/');
  }

  const tenantId = student?.organizationId;
  let myInterviews, completedInterview;
  if (tab === 'active') {
    myInterviews = await getActiveInterviewathon(tenantId, student?.id);
  } else if (tab === 'completed') {
    completedInterview = await getMyInterviewthon(userId, tenantId);
  }

  return (
    <MyInterviewthonScreen
      myInterviews={myInterviews}
      tab={tab}
      completedInterview={completedInterview ?? []}
      user={session?.user}
      session={session}
    />
  );
}
