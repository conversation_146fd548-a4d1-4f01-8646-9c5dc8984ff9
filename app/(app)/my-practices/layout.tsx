'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useRouter } from 'next/navigation';

import { DropDown } from '@/components/create-interview/dropdown';
import PageHeader from '@/components/page-header';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import useWindowSize from '@/hooks/client/use-window-size';

import { Button } from '@camped-ui/button';
import { Card } from '@camped-ui/card';

const menuContents = [
  {
    title: 'Mock Interviews',
    href: 'mock-interviews',
  },
  {
    title: 'Frontend Practices',
    href: 'frontend-practices',
  },
  {
    title: 'Coding Practices',
    href: 'coding-practices',
  },
  {
    title: 'Tailored Practices',
    href: 'tailored-practices',
  },
  {
    title: 'Competitive Interviews',
    href: 'competitive-interviews',
  },
];

export default function Layout({ children }: any) {
  const pathname = usePathname();
  const router = useRouter();
  const { isDesktop } = useWindowSize();
  const activeTab = pathname?.split('/')?.pop();
  if (isDesktop) {
    return (
      <Card className="relative flex h-full w-full flex-1 flex-col">
        <div className="relative h-full min-h-screen w-full overflow-x-hidden pb-16">
          <div className="flex h-full w-full flex-col overflow-hidden md:flex-row">
            <div className="flex h-full w-[320px] flex-col border-r p-4">
              <div className="flex flex-1 flex-col gap-1 pb-0">
                {menuContents.map((link, index) => {
                  const isActive = pathname?.split('/').includes(link.href);
                  return (
                    <Link key={index} href={`/my-practices/${link.href}`}>
                      <Button
                        variant="ghost"
                        className={`group mb-0.5 flex w-full items-center justify-start gap-2.5 py-2 text-sm font-medium outline-none ${
                          isActive ? 'bg-secondary' : ''
                        }`}
                      >
                        {link.title}
                      </Button>
                    </Link>
                  );
                })}
              </div>
            </div>
            <div className="h-full w-full flex-1 overflow-scroll p-4">{children}</div>
          </div>
        </div>
      </Card>
    );
  }
  return (
    <Card className={`relative flex h-full w-full flex-1 flex-col`}>
      <div
        className={`relative h-full min-h-screen w-full overflow-x-hidden px-4`}
        style={{ paddingBottom: 128 }}
      >
        <div className="flex justify-between py-2">
          <PageHeader title="My Practices" />
          <DropDown
            disabled={false}
            data={menuContents?.map((item) => ({
              name: item?.title,
              value: item?.href,
            }))}
            onSelect={(item) => router.push(`/my-practices/${item.value}`)}
            value={menuContents?.find((item) => item?.href === activeTab)?.title}
            placeHolder="Practices"
            className="h-full max-h-10 w-full max-w-[180px]"
          />
        </div>
        <div className={`h-full w-full overflow-scroll py-2`}>{children} </div>
      </div>
    </Card>
  );
}
