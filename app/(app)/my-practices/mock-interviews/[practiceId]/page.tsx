import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { BreadCrumbWrapper } from '@/components/breadcrumb/wrapper';
import { MockResultScreen } from '@/components/interview/mock/mock-result-screen';
import { authOptions } from '@/lib/auth';
import { getUserInterviews } from '@/services/apicall';
import { getServerSession } from 'next-auth';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@camped-ui/breadcrumb';

export default async function InterViewResults(props) {
  const session: any = await getServerSession(authOptions);

  const userId = cookies().get('aceprepUserId')?.value;
  const id = props.params.practiceId;
  const studentId = props.params.id;

  if (!id) {
    redirect(`/my-practices/mock-interviews`);
  }

  const interviewResult = await getUserInterviews(null, id);

  return (
    <>
      <BreadCrumbWrapper
        data={[
          {
            title: 'Mock Interview',
          },
          {
            title: interviewResult?.items?.question,
          },
        ]}
      />
      <div className="flex w-full max-w-[800px] flex-col gap-4">
        <MockResultScreen session={session} interviewResult={interviewResult} />
      </div>
    </>
  );
}
