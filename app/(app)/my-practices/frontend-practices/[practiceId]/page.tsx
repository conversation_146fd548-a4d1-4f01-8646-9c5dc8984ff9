import { cookies } from 'next/headers';
import Link from 'next/link';
import { redirect } from 'next/navigation';

import { BreadCrumbWrapper } from '@/components/breadcrumb/wrapper';
import { FrontendResultScreen } from '@/components/wrapper-screen/frontend-result-screen';
import { AppLogo } from '@/layout/logo';
import { authOptions } from '@/lib/auth';
import { getFrontEndPractice, getSavedFrontendProblem } from '@/services/apicall';
import { getServerSession } from 'next-auth';

import { Button } from '@camped-ui/button';

export default async function InterViewResults(props) {
  const id = props.params.practiceId;
  const studentId = props.params.id;

  if (!id) {
    redirect(`/my-practices/frontend-practices`);
  }
  const userId = cookies()?.get('aceprepUserId')?.value;

  const session: any = await getServerSession(authOptions);
  let problem;

  const codingResult = await getSavedFrontendProblem(undefined, undefined, id);

  if (codingResult) {
    problem = await getFrontEndPractice(codingResult?.result?.[0]?.questionId);
  }

  if (codingResult?.error || codingResult === null || codingResult?.result?.length === 0) {
    return (
      <>
        <div className="mx-auto mt-[60px] flex max-w-lg flex-col items-center text-center">
          <AppLogo />
          <p className="mt-4 text-gray-500 dark:text-gray-400">
            We searched high and low, but couldn’t find what you’re looking for. Let’s find a better
            place for you to go.
          </p>

          <div className="mt-6 flex w-full shrink-0 items-center gap-x-3 sm:w-auto">
            <Link href={'/my-practices/frontend-practices'}>
              <Button>Take me back</Button>
            </Link>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <BreadCrumbWrapper
        data={[
          {
            title: 'Frontend Practice',
          },
          {
            title: codingResult?.result?.[0]?.questionId?.replaceAll(/-/g, ' '),
          },
        ]}
      />
      <div className="flex w-full max-w-[800px] flex-col gap-4">
        <FrontendResultScreen
          codingResult={codingResult?.result?.[0]}
          problem={problem?.result}
          userId={userId}
          session={session}
        />
      </div>
    </>
  );
}
