import { cookies } from 'next/headers';

import { WorkspaceLayout } from '@/layout/wrapper';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function Layout({ children }) {
  const session = await getServerSession(authOptions);
  const organization = (session as any)?.memberships;
  const organizationId = cookies()?.get('aceprepTenantId')?.value;

  return (
    <WorkspaceLayout session={session} organization={organization} organizationId={organizationId}>
      {children}
    </WorkspaceLayout>
  );
}
