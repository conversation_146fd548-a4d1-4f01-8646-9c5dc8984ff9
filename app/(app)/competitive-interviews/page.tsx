import { cookies } from 'next/headers';

import RoleSelectSection from '@/components/section/ias-select-section';
import { getComplexity, getSME } from '@/services/apicall';

export default async function Home() {
  const userId = cookies()?.get('aceprepUserId')?.value;

  const role = await getSME();
  const level = await getComplexity();
  return <RoleSelectSection role={role} level={level} userId={userId} />;
}
