import { cookies } from 'next/headers';

import { DiscordWebhookTable } from '@/components/data-table/discord/data-table';
import { getDiscordWebhooks } from '@/services/apicall';

export default async function Integration(props) {
  const searchParams = props.searchParams;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const webhooks = await getDiscordWebhooks({ organizationId: tenantId, searchParams });

  return (
    <DiscordWebhookTable
      tasksPromise={{ data: webhooks?.integration, pageCount: webhooks?.pageCount }}
    />
  );
}
