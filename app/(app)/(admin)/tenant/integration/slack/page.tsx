import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { ChannelSelectModal } from '@/components/Modal/connect-channel';
import { metaData } from '@/constants/integration-platform';
import { getIntegration } from '@/services/apicall';
import axios from 'axios';

export default async function Integration(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const integration = await getIntegration({ organizationId: tenantId, platform: 'slack' });
  if (!integration) {
    redirect('/tenant/integration');
  }
  const accessToken = integration?.accessToken;
  const response = await axios.get(`https://slack.com/api/conversations.list`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  const channels = response.data?.channels;

  return (
    <ChannelSelectModal
      channels={channels}
      item={metaData?.find((item) => item?.platform === 'Slack')}
      integration={integration}
    />
  );
}
