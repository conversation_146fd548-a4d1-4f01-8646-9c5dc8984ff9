import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import PageHeader from '@/components/page-header';
import { IntegrationScreen } from '@/components/wrapper-screen/integration/connect-screen';
import { authOptions } from '@/lib/auth';
import { getAllIntegration } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function Integration(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;

  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};
  if (currentOrg?.organization?.type?.toLowerCase() !== 'organization') {
    return redirect('/404');
  }
  const integration = await getAllIntegration({ organizationId: tenantId });

  return (
    <div className="p-4">
      <IntegrationScreen integration={integration} />
    </div>
  );
}
