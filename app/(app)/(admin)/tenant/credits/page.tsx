import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { CreditsUsageScreen } from '@/components/wrapper-screen/credits-usage-screen';
import { authOptions } from '@/lib/auth';
import { getCandidatesCount } from '@/services/apicall';
import { getOrganizationById } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function CreditsPage() {
  const userId = cookies().get('aceprepUserId')?.value;
  const organizationId = cookies().get('aceprepTenantId')?.value;

  if (!organizationId || !userId) {
    return redirect('/404');
  }

  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};

  const [organization, candidatePercentage] = await Promise.all([
    getOrganizationById(organizationId, userId),
    getCandidatesCount({
      tenantId: organizationId,
      membershipId: currentOrg?.id,
      role: currentOrg?.role
    }),
  ]);

  return (
    <CreditsUsageScreen
      organization={organization}
      candidatePercentage={candidatePercentage?.count}
    />
  );
}
