import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import ImageUpload from '@/components/image-upload/image-upload';
import { OrganizationSettingsScreen } from '@/components/wrapper-screen/tenant-settings-screen';
import { getOrganizationById } from '@/services/apicall';

export default async function app() {
  const userId = cookies().get('aceprepUserId')?.value;

  const organizationId = cookies().get('aceprepTenantId')?.value;

  if (!organizationId || !userId) {
    return redirect('/404');
  }

  const organization = await getOrganizationById(organizationId, userId);

  return (
    <>
      <ImageUpload
        folderName={'organization-profile'}
        id={organizationId}
        updatedAt={organization?.updatedAt}
        heading={'Organization Logo'}
        role={organization?.role}
        screen="organization"
      />
      <OrganizationSettingsScreen organization={organization} />
    </>
  );
}
