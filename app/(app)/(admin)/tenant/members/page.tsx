import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { MemberListScreen } from '@/components/organization/member-list-screen';
import { authOptions } from '@/lib/auth';
import { getStaffList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const userId = cookies().get('aceprepUserId')?.value;
  if (!userId) {
    return redirect('/404');
  }

  const organizationId = cookies().get('aceprepTenantId')?.value;
  const organizationType = 'organization';

  if (!organizationId) {
    return redirect('/404');
  }
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};

  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());
  const [membersList] = await Promise.all([
    getStaffList({
      organizationId: organizationId,
      role: ['ADMIN', 'MEMBER'],
      userId: userId,
      organizationType: organizationType,
      includePending: true, // Include pending invitations
    }),
  ]);

  return (
    <MemberListScreen
      userId={userId}
      organizationId={organizationId}
      membersListResponse={membersList?.items}
    />
  );
}
