import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { DepartmentMemberListScreen } from '@/components/wrapper-screen/department-member-listing';
import { authOptions } from '@/lib/auth';
import { getDepartmentMembershipMappingByDepartmentId, getStaffList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const userId = cookies().get('aceprepUserId')?.value;
  const searchParams = props?.searchParams;
  const departmentId = searchParams?.id;
  const departmentName = searchParams?.key;

  if (!userId) {
    return redirect('/404');
  }

  const organizationId = cookies().get('aceprepTenantId')?.value;
  const organizationType = 'organization';

  if (!organizationId) {
    return redirect('/404');
  }
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};

  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());
  const [membersList, department] = await Promise.all([
    getStaffList({
      organizationId: organizationId,
      role: ['MEMBER'],
      userId: userId,
      organizationType: organizationType,
    }),
    getDepartmentMembershipMappingByDepartmentId({
      departmentId,
      role: ['MEMBER', 'ADMIN'],
    }),
  ]);
  const currentUser = department?.departmentMembershipMapping?.find(
    (item) => item?.membershipId === currentOrg?.id,
  );
  return (
    <DepartmentMemberListScreen
      userId={userId}
      isSuperUser={isSuperUser || currentUser?.role === 'ADMIN'}
      departmentId={departmentId}
      members={membersList?.items}
      departmentName={departmentName}
      departmentMembers={department?.departmentMembershipMapping}
    />
  );
}
