import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { GroupTable } from '@/components/group/group-listing';
import { authOptions } from '@/lib/auth';
import { getAllDepartment, getAllGroup } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const userId = cookies().get('aceprepUserId')?.value;
  if (!userId) {
    return redirect('/404');
  }

  const organizationId = cookies().get('aceprepTenantId')?.value;

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};
  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());

  if (!organizationId) {
    return redirect('/404');
  }

  const [groupList, department] = await Promise.all([
    getAllGroup({
      membershipId: isSuperUser ? undefined : currentOrg?.id,
      organizationId: organizationId,
    }),
    getAllDepartment({
      organizationId: organizationId,
      membershipId: isSuperUser ? undefined : currentOrg?.id,
    }),
  ]);
  return (
    <GroupTable
      tasksPromise={{ data: groupList?.group }}
      isSuperUser={isSuperUser}
      department={department}
    />
  );
}
