import React from 'react';

import { cookies } from 'next/headers';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import { InterviewathonTable } from '@/components/data-table/interviewathon-list/data-table';
import { authOptions } from '@/lib/auth';
import { CampedTableProvider } from '@/packages/shared-data-table/camped-table-provider';
import { getInterviewathonByMember } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export type DocumentsPageProps = {
  searchParams?: {
    status?: string;
  };
};

const edgeCaseMeta = [
  {
    id: 'active',
    title: 'No Active Events Scheduled',
    description:
      'There are currently no active interview events scheduled. As an admin, you can create new events to engage users and enhance their interview preparation experience.',
  },
  {
    id: 'completed',
    title: 'No Completed Events Yet',
    description:
      'There are no past interview events that have been completed. Start organizing events to track and manage completed interviews.',
  },
  {
    id: 'archived',
    title: 'No Archived Events Found',
    description:
      'There are currently no archived interview events. Archive completed events to maintain a record of past interviews.',
  },
];

export default async function ListEvent(props) {
  const status = props?.params?.id?.toLowerCase() || 'active';
  const searchParams = props.searchParams;
  const tenantId = cookies().get('aceprepTenantId')?.value;

  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const eventList = await getInterviewathonByMember({
    tenantId,
    searchParams,
    membershipId: currentOrg?.id,
    role: currentOrg?.role,
  });

  if (eventList?.length === 0) {
    return (
      <EdgeCaseCard
        title={edgeCaseMeta?.find((item) => item?.id === status)?.title || ''}
        description={edgeCaseMeta?.find((item) => item?.id === status)?.description || ''}
        ctaLabel="Create New Event"
        ctaHref="/interviewathon/create"
      />
    );
  }

  return (
    <CampedTableProvider>
      <InterviewathonTable
        tasksPromise={{
          data: eventList,
        }}
      />
    </CampedTableProvider>
  );
}
