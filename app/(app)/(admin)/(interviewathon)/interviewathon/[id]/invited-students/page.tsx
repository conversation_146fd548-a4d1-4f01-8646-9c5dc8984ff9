import { cookies } from 'next/headers';

import { StudentTable } from '@/components/data-table/student-list/data-table';
import { getInvitedStudentsDetails } from '@/services/apicall';

export default async function InterViewEvent(props) {
  const eventId = props.params.id;
  const searchParams = props.searchParams;
  const organizationId = cookies().get('aceprepTenantId')?.value;
  const [data] = await Promise.all([
    getInvitedStudentsDetails({
      organizationId,
      searchParams,
      eventId,
    }),
  ]);

  const totalPages = (data as any)?.totalPages;

  return (
    <StudentTable
      isSuperUser={false}
      group={[]}
      searchDepartment={searchParams?.department}
      department={{}}
      eventId={eventId}
      tasksPromise={data?.student}
      totalPages={totalPages}
    />
  );
}
