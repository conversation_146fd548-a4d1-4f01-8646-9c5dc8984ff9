import { cookies } from 'next/headers';

import { AssessmentCriteriaModal } from '@/components/Modal/assessment-criteria-modal';
import { getInterviewQuestions } from '@/services/apicall';

export default async function AssessmentCriteriaModalPage(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies().get('aceprepUserId')?.value;
  const eventId = props.params.id;

  const eventDetails = await getInterviewQuestions({
    eventId,
    organizationId: tenantId,
    userId: userId,
  });

  return (
    <AssessmentCriteriaModal
      eventDetails={eventDetails}
      show={true}
      setShow={undefined}
      screen="interviewathon"
    />
  );
}
