import { cookies } from 'next/headers';

import { AssessmentCriteriaView } from '@/components/wrapper-screen/interview/assessment-criteria-view';
import { getInterviewQuestions } from '@/services/apicall';

export default async function AssessmentCriteriaPage(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies().get('aceprepUserId')?.value;
  const eventId = props.params.id;

  const eventDetails = await getInterviewQuestions({
    eventId,
    organizationId: tenantId,
    userId: userId,
  });

  return <AssessmentCriteriaView eventDetails={eventDetails} />;
}
