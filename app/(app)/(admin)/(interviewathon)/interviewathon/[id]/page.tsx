import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { InterviewCandidateTable } from '@/components/data-table/interview-candidate/data-table';
import { authOptions } from '@/lib/auth';
import { CampedTableProvider } from '@/packages/shared-data-table/camped-table-provider';
import { getInterviewList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function InterViewEvent(props) {
  const eventId = props.params.id;
  const searchParams = props.searchParams;
  const organizationId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies().get('aceprepUserId')?.value;
  const session: any = await getServerSession(authOptions);

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};

  const interviewResult = await getInterviewList({
    id: eventId,
    organizationId,
    userId: userId,
    searchParams,
    membershipId: currentOrg?.id,
    role: currentOrg?.role,
  });
  if (!interviewResult?.items) return redirect('/404');

  return (
    <CampedTableProvider>
      <InterviewCandidateTable
        tasksPromise={{
          data: interviewResult,
        }}
        eventId={eventId}
        userId={userId}
        members={[]}
        platform={''}
      />
    </CampedTableProvider>
  );
}
