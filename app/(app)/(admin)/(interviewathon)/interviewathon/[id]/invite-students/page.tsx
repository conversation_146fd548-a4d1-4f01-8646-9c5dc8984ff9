import { cookies } from 'next/headers';

import { StudentTable } from '@/components/data-table/student-list/data-table';
import { authOptions } from '@/lib/auth';
import { getAllDepartment, getAllGroup, getUnInvitedStudentsDetails } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function InterViewEvent(props) {
  const eventId = props.params.id;
  const searchParams = props.searchParams;

  const session: any = await getServerSession(authOptions);
  const organizationId = cookies().get('aceprepTenantId')?.value;
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};
  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());
  const [data, department, group] = await Promise.all([
    getUnInvitedStudentsDetails({
      organizationId,
      searchParams: {
        ...searchParams,
        memberShipId: isSuperUser ? undefined : currentOrg?.id,
      },
      eventId,
    }),
    getAllDepartment({
      organizationId,
      membershipId: isSuperUser ? undefined : currentOrg?.id,
    }),
    getAllGroup({
      organizationId,
      membershipId: isSuperUser ? undefined : currentOrg?.id,
    }),
  ]);

  const totalPages = (data as any)?.totalPages;

  return (
    <StudentTable
      group={group}
      searchDepartment={searchParams?.department}
      isSuperUser={isSuperUser}
      eventId={eventId}
      tasksPromise={data?.student}
      totalPages={totalPages}
      department={department}
    />
  );
}
