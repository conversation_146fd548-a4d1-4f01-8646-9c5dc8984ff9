import { cookies, headers } from 'next/headers';
import { redirect } from 'next/navigation';

import InterviewathonTabs from '@/components/interviewathon/tabs';
import { authOptions } from '@/lib/auth';
import { getInterviewList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function Layout({ children, modal }) {
  const pathname = (await headers().get('x-next-pathname')) as string;
  const eventId = pathname?.split('/')?.[2];
  const screen = pathname?.split('/')?.[3] ?? 'student';
  const organizationId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies().get('aceprepUserId')?.value;
  const session: any = await getServerSession(authOptions);

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};

  const interviewResult = await getInterviewList({
    id: eventId,
    organizationId,
    userId: userId,
    searchParams: {},
    membershipId: currentOrg?.id,
    role: currentOrg?.role,
  });
  if (!interviewResult?.items) return redirect('/404');

  return (
    <main className={`relative flex h-full w-full flex-1 flex-col overflow-hidden`}>
      <div className={`relative h-full w-full overflow-x-hidden`}>
        <InterviewathonTabs eventId={eventId} title={interviewResult?.items?.name} />
        {children}
      </div>
    </main>
  );
}
