import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import { InterviewFormProvider } from '@/components/create-interview/provider';
import { CreateEventScreen } from '@/components/wrapper-screen/create-interview/create-event-screen';
import { authOptions } from '@/lib/auth';
import {
  fetchOrganizationVideoQuestion,
  fetchQuestion,
  fetchQuestionType,
  getGroupWithMembership,
  getLevel,
  getQuizCategory,
  getQuizQuestion,
  getQuizTopics,
  getStaffList,
  getTotalInterviewathonCount,
} from '@/services/apicall';
import { getCodingProblems, getFrontEndPractice } from '@/services/apicall';
import { getServerSession } from 'next-auth';

const CreateEvent = async (props) => {
  const userId = cookies().get('aceprepUserId')?.value;
  const tenantId = cookies().get('aceprepTenantId')?.value;

  const [session, eventCount]: any = await Promise.all([
    getServerSession(authOptions),
    getTotalInterviewathonCount(tenantId),
  ]);

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  if (
    currentOrg?.organization?.eventLimit?.event <= eventCount &&
    currentOrg?.organization?.eventLimit?.event !== null
  ) {
    return (
      <EdgeCaseCard
        title="Interviewathon Creation Limit Reached"
        description="The maximum number of Interviewathons has been created. Please contact us for further assistance."
        ctaLabel="Contact Us"
        ctaHref="/contact"
      />
    );
  }
  const eventId = props?.searchParams?.id;
  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());

  const [
    level,
    codingProblems,
    frontendProblems,
    multipleChoiceQuestions,
    multipleChoiceCategory,
    multipleChoiceTopics,
    group,
    members,
    videoCategory,
    videoOrgQuestion,
    videoQuestion,
  ] = await Promise.all([
    getLevel(),
    getCodingProblems(),
    getFrontEndPractice(),
    getQuizQuestion({ organizationId: tenantId }),
    getQuizCategory(),
    getQuizTopics('all'),
    getGroupWithMembership({
      membershipId: isSuperUser ? undefined : currentOrg?.id,
      organizationId: tenantId,
    }),
    getStaffList({
      organizationId: tenantId,
      role: ['ADMIN', 'MEMBER'],
      userId: userId,
      organizationType: 'organization',
    }),
    fetchQuestionType(),
    fetchOrganizationVideoQuestion({ organizationId: tenantId }),
    fetchQuestion('all', {}),
  ]);
  const videoQuestions = videoQuestion?.map((item) => {
    const question = item?.questions;
    return question?.map((item2) => ({ ...item2, slug: item?.type }));
  });

  return (
    <InterviewFormProvider>
      <CreateEventScreen
        level={level}
        organizationId={tenantId}
        codingProblems={codingProblems?.result}
        frontendProblems={frontendProblems?.result}
        multipleChoiceQuestions={multipleChoiceQuestions?.result}
        eventId={eventId}
        userId={userId}
        multipleChoiceCategory={multipleChoiceCategory}
        multipleChoiceTopics={multipleChoiceTopics}
        group={group}
        members={members}
        eventDetails={{}}
        videoCategory={videoCategory}
        videoQuestion={[
          ...videoQuestions.flatMap((group) => group.map((question) => ({ ...question }))),
          ...videoOrgQuestion?.map((item) => ({ ...item, slug: 'Custom Question' })),
        ]}
      />
    </InterviewFormProvider>
  );
};

export default CreateEvent;
