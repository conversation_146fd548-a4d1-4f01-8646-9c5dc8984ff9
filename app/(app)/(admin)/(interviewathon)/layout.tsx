import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function Layout({ children }) {
  const session: any = await getServerSession(authOptions);

  if (!session) {
    return redirect('/sign-in');
  }

  const tenantId =
    (await cookies()?.get('aceprepTenantId')?.value) ?? session?.memberships?.[0]?.organizationId;

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  if (currentOrg?.organization?.type?.toLowerCase() !== 'institution') {
    return redirect('/404');
  }
  return <>{children}</>;
}
