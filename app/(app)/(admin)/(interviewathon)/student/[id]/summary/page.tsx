import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { camelCaseToProper } from '@/components/profile/RightSide';
import { getCandidateSummary } from '@/services/apicall';

import { Badge } from '@camped-ui/badge';
import { Card, CardContent, CardDescription, CardTitle } from '@camped-ui/card';

export default async function DemoPage(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const id = props?.params?.id;

  const candidateDetails = await getCandidateSummary(id, tenantId);

  const user = candidateDetails?.userDetails;

  if (!user) {
    return (
      <Card className="flex min-h-[60vh] items-center justify-center p-4">
        <p className="text-center">User not updated the profile yet</p>
      </Card>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="w-full space-y-2">
        <CardTitle className="text-xl">About Me</CardTitle>
        <Card className="max-w-[600px] border-dashed p-4">{user?.aboutMe || '-'}</Card>
      </div>

      <div className="w-full space-y-2">
        <CardTitle className="mt-4 text-xl">Experience</CardTitle>
        <Card className="max-w-[600px] border-dashed p-4">{user?.experience || '-'}</Card>
      </div>

      <CardTitle className="mt-4 text-xl">Skills</CardTitle>
      {user?.skills?.length > 0 ? (
        <Card className="max-w-[600px] border-dashed px-3 py-4">
          <div className="flex flex-wrap gap-3">
            {user?.skills?.map((item, index) => (
              <Badge variant="secondary" key={index}>
                {item}
              </Badge>
            ))}
          </div>
        </Card>
      ) : (
        <p>-</p>
      )}

      {user?.otherDetails && (
        <>
          <CardTitle className="mt-4 text-xl">Other Details</CardTitle>
          {Object.keys(user.otherDetails).map((property) => {
            const properName = camelCaseToProper(property);
            const value = user.otherDetails[property];

            return (
              <CardContent className="p-0 opacity-70" key={property}>
                <CardDescription>
                  {properName}: {value}
                </CardDescription>
              </CardContent>
            );
          })}
        </>
      )}
    </div>
  );
}
