import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { StudentDetailScreen } from '@/components/wrapper-screen/student-details-screen/student-interviewathon-screen';
import { authOptions } from '@/lib/auth';
import { getCandidateEventDetails } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const studentId = props?.params?.id;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const candidateDetails = await getCandidateEventDetails(
    studentId,
    tenantId,
    true,
    currentOrg?.id,
    currentOrg?.role,
  );

  return (
    <StudentDetailScreen studentId={studentId} eventDetails={candidateDetails?.eventDetails} />
  );
}
