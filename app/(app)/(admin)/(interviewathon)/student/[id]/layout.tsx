'use client';

import { useState } from 'react';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import { DropDown } from '@/components/create-interview/dropdown';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import useWindowSize from '@/hooks/client/use-window-size';

export default function Layout({ user, children }) {
  const router = useRouter();
  const pathName = usePathname();
  const { isDesktop, isLoading } = useWindowSize();
  const filter = pathName?.split('/')?.[4] ?? 'mock';
  const tab = pathName?.split('/')?.[3] ?? 'summary';
  const userId = pathName?.split('/')?.[2];
  const data = [
    {
      name: 'Mock Interview',
      value: 'mock',
    },
    {
      name: 'Coding Practice',
      value: 'coding',
    },
    {
      name: 'Frontend Practice',
      value: 'frontend',
    },
    {
      name: 'Tailored Practice',
      value: 'tailored',
    },
  ];

  if (isDesktop || isLoading) {
    return (
      <div className="flex h-full w-full flex-col divide-y md:flex-row md:divide-x md:divide-y-0">
        <div className="w-full p-4 md:w-[340px]">{user}</div>

        <div className="h-full w-full overflow-y-scroll px-4 pt-4 md:col-span-2 md:mt-0">
          <Tabs defaultValue={tab} className="h-full w-full">
            <div className="mb-2 flex justify-between">
              <TabsList className="">
                <TabsTrigger
                  value="summary"
                  onClick={() => router.replace(`/student/${userId}/summary`)}
                >
                  Summary
                </TabsTrigger>
                <TabsTrigger
                  value="profile"
                  onClick={() => router.replace(`/student/${userId}/resume`)}
                >
                  Resume
                </TabsTrigger>
                <TabsTrigger
                  value="interviewathon"
                  onClick={() => router.replace(`/student/${userId}/interviewathon`)}
                >
                  Interviewathons
                </TabsTrigger>
                <TabsTrigger
                  value="practices"
                  onClick={() => router.replace(`/student/${userId}/practices/mock`)}
                >
                  Practices
                </TabsTrigger>
              </TabsList>
              {tab === 'practices' && (
                <DropDown
                  disabled={false}
                  data={data}
                  onSelect={(item) => router.replace(`/student/${userId}/practices/${item?.value}`)}
                  value={data?.find((item) => item?.value === filter)?.name}
                  placeHolder="Practices"
                  className="h-[full] max-h-10 w-full max-w-[180px]"
                />
              )}
            </div>
            {children}
          </Tabs>
        </div>
      </div>
    );
  }
  return (
    <div className="flex w-full flex-col overflow-y-scroll">
      <div className="w-full p-4">{user}</div>

      <div className="h-full w-full overflow-y-scroll px-4 pt-4 md:col-span-2 md:mt-0">
        <Tabs defaultValue={tab} className="h-full w-full">
          <div className="mb-2 flex flex-col justify-between md:flex-row">
            <TabsList className="overflow-scroll pl-10">
              <TabsTrigger
                value="summary"
                onClick={() => router.replace(`/student/${userId}/summary`)}
              >
                Summary
              </TabsTrigger>
              <TabsTrigger
                value="profile"
                onClick={() => router.replace(`/student/${userId}/resume`)}
              >
                Resume
              </TabsTrigger>
              <TabsTrigger
                value="interviewathon"
                onClick={() => router.replace(`/student/${userId}/interviewathon`)}
              >
                Interviewathons
              </TabsTrigger>
              <TabsTrigger
                value="practices"
                onClick={() => router.replace(`/student/${userId}/practices/mock`)}
              >
                Practices
              </TabsTrigger>
            </TabsList>
            {tab === 'practices' && (
              <DropDown
                disabled={false}
                data={data}
                onSelect={(item) => router.replace(`/student/${userId}/practices/${item?.value}`)}
                value={data?.find((item) => item?.value === filter)?.name}
                placeHolder="Practices"
                className="mt-2 h-[full] max-h-10 w-full md:max-w-[180px]"
              />
            )}
          </div>

          {children}
        </Tabs>
      </div>
    </div>
  );
}
