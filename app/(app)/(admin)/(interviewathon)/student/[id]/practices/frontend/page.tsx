import { EdgeCaseCard } from '@/components/cards/edge-case';
import MyFrontendInterviewCard from '@/components/cards/frontend-card';
import { authOptions } from '@/lib/auth';
import { getSavedFrontendProblem } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function MyInterview(props) {
  const id = props.params.id;

  const savedCode = await getSavedFrontendProblem(undefined, id);
  const frontendPractices = savedCode?.result?.filter((item) => item?.feedback !== null);

  if (frontendPractices?.length === 0) {
    return (
      <EdgeCaseCard
        title="No Frontend Practices"
        description="The student has not begun practicing yet."
      />
    );
  }
  const session = await getServerSession(authOptions);
  return (
    <div className="grid w-full grid-cols-1 gap-4">
      {frontendPractices?.map((coding, index) => (
        <MyFrontendInterviewCard interview={coding} key={index} session={session} />
      ))}
    </div>
  );
}
