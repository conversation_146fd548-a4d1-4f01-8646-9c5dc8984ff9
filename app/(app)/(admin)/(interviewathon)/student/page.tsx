import { cookies } from 'next/headers';

import { StudentTable } from '@/components/data-table/student-list/data-table';
import { authOptions } from '@/lib/auth';
import { getAllDepartment, getAllGroup, getStudentsDetails } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const searchParams = props.searchParams;

  const session: any = await getServerSession(authOptions);
  const organizationId = cookies().get('aceprepTenantId')?.value;
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};
  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());

  const [data, department, group] = await Promise.all([
    getStudentsDetails({
      organizationId,
      searchParams: {
        memberShipId: isSuperUser ? undefined : currentOrg?.id,
        ...searchParams,
      },
    }),
    getAllDepartment({
      organizationId,
      membershipId: isSuperUser ? undefined : currentOrg?.id,
    }),
    getAllGroup({
      organizationId,
      membershipId: isSuperUser ? undefined : currentOrg?.id,
    }),
  ]);

  const totalPages = (data as any)?.totalPages;

  return (
    <StudentTable
      isSuperUser={isSuperUser}
      department={department}
      eventId={''}
      group={group}
      tasksPromise={data?.student}
      totalPages={totalPages}
      searchDepartment={searchParams?.department}
    />
  );
}
