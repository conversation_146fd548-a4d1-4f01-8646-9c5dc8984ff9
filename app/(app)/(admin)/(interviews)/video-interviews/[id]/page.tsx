import { cookies } from 'next/headers';

import NotFound from '@/app/not-found';
import { VideoInterviewDetailScreen } from '@/components/wrapper-screen/interview/video-interview-detail-screen';
import { getMeetingById } from '@/services/apicall';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const id = props.params.id;
  const userId = cookies().get('aceprepUserId')?.value;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const [meeting] = await Promise.all([getMeetingById({ id })]);
  if (!meeting) {
    return <NotFound />;
  }
  return (
    <VideoInterviewDetailScreen
      meeting={meeting}
      userId={userId}
      candidateId={meeting?.userId}
      organizationId={meeting?.organizationId || tenantId}
      currentOrgId={currentOrg?.id}
      currentOrgRole={currentOrg?.role}
    />
  );
}
