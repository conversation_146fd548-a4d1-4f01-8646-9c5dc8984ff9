import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { CandidateTable } from '@/components/data-table/candidate-list/data-table';
import { CampedTableProvider } from '@/packages/shared-data-table/camped-table-provider';
import { authOptions } from '@/lib/auth';
import { getCandidatesList, getEventList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export type DocumentsPageProps = {
  searchParams?: {
    page?: string;
    per_page?: string;
    eventName?: string;
  };
};
export default async function ListEvent({ searchParams = {} }: DocumentsPageProps) {
  const userId = cookies().get('aceprepUserId')?.value;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const event = searchParams?.eventName;

  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const [membersList, eventList] = await Promise.all([
    getCandidatesList({
      organizationId: tenantId,
      role: 'CANDIDATE',
      userId: userId,
      membershipId: currentOrg?.id,
      searchParams,
    }),
    getEventList({
      tenantId,
      screen: 'interview',
      searchParams,
    }),
  ]);

  const eventId =
    Array.isArray(event?.split('.')) || !event ? event?.split('.') : [event?.split('.')];

  return (
    <CampedTableProvider>
      <CandidateTable
        eventList={eventList?.items}
        event={eventId}
        tasksPromise={{
          data: membersList,
        }}
      />
    </CampedTableProvider>
  );
}
