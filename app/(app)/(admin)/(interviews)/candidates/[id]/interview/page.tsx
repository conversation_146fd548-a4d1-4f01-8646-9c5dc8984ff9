import { cookies } from 'next/headers';

import { CandidateDetailScreen } from '@/components/wrapper-screen/candidate-detail-screen/candidate-details-screen';
import { authOptions } from '@/lib/auth';
import { getCandidateEventDetails } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const id = props?.params?.id;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const candidateDetails = await getCandidateEventDetails(
    id,
    tenantId,
    false,
    currentOrg?.id,
    currentOrg?.role,
  );

  return (
    <CandidateDetailScreen
      eventDetails={candidateDetails?.eventDetails}
      candidateId={id}
      organizationId={tenantId}
      currentOrgId={currentOrg?.id}
      currentOrgRole={currentOrg?.role}
    />
  );
}
