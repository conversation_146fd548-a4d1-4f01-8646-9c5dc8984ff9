'use client';

import { usePathname, useRouter } from 'next/navigation';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import useWindowSize from '@/hooks/client/use-window-size';

export default function Layout({ user, children, summary, interviews, profile, emails }) {
  const pathName = usePathname();
  const router = useRouter();
  const tab = pathName?.split('/')?.[3] ?? 'summary';
  const userId = pathName?.split('/')?.[2];
  const { isDesktop, isLoading } = useWindowSize();
  if (isDesktop || isLoading) {
    return (
      <div className="flex h-full w-full flex-col divide-y md:flex-row md:divide-x md:divide-y-0">
        <div className="w-full p-4 md:w-[340px]">{user}</div>

        <div className="h-full w-full overflow-y-scroll px-4 pt-4 md:col-span-2 md:mt-0">
          <Tabs defaultValue={tab} className="h-full w-full">
            <div className="mb-2 flex justify-between">
              <TabsList className="">
                <TabsTrigger
                  value="summary"
                  onClick={() => router.replace(`/candidates/${userId}/summary`)}
                >
                  Summary
                </TabsTrigger>
                <TabsTrigger
                  value="profile"
                  onClick={() => router.replace(`/candidates/${userId}/profile`)}
                >
                  Profile
                </TabsTrigger>
                <TabsTrigger
                  value="interview"
                  onClick={() => router.replace(`/candidates/${userId}/interview`)}
                >
                  Interview
                </TabsTrigger>
              </TabsList>
            </div>
            {children}
          </Tabs>
        </div>
      </div>
    );
  }
  return (
    <div className="mb-20 flex h-full w-full flex-col divide-y md:grid md:grid-cols-3 md:gap-0 md:divide-x md:divide-y-0">
      <div className="w-full p-4 md:w-[240px]">{user}</div>

      <div className="h-full w-full overflow-y-scroll px-4 pt-4 md:col-span-2 md:mt-0">
        <Tabs defaultValue={tab} className="h-full w-full">
          <div className="mb-2 flex justify-between">
            <TabsList className="">
              <TabsTrigger
                value="summary"
                onClick={() => router.replace(`/candidates/${userId}/summary`)}
              >
                Summary
              </TabsTrigger>
              <TabsTrigger
                value="profile"
                onClick={() => router.replace(`/candidates/${userId}/profile`)}
              >
                Profile
              </TabsTrigger>
              <TabsTrigger
                value="interview"
                onClick={() => router.replace(`/candidates/${userId}/interview`)}
              >
                Interview
              </TabsTrigger>
            </TabsList>
          </div>
          {children}
        </Tabs>
      </div>
    </div>
  );
}
