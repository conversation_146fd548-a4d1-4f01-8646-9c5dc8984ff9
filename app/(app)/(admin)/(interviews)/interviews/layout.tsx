import { cookies } from 'next/headers';

import { InterviewLayoutScreen } from '@/components/wrapper-screen/interview/event-overview-update';
import { authOptions } from '@/lib/auth';
import { GlobalTabNav } from '@/packages/shared-global-tab-nav';
import { getEventDetailsOverview } from '@/services/apicall';
import { getServerSession } from 'next-auth';
import { GlobalPanel } from '@/layout/global-panel';

export default async function Layout({ children }) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const eventDetails = await getEventDetailsOverview(
    tenantId,
    false,
    currentOrg?.id,
    currentOrg?.role,
  );

  return (
    <GlobalPanel title="Interviews">
      <InterviewLayoutScreen eventDetails={eventDetails} />
      {children}
    </GlobalPanel>
  );
}
