import React from 'react';

import { cookies } from 'next/headers';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import { ModernInterviewDashboard } from '@/components/admin/modern-interview-dashboard';
import { authOptions } from '@/lib/auth';
import { getInterviewByMember, getEventDetailsOverview } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export type DocumentsPageProps = {
  searchParams?: {
    status?: string;
  };
};

const edgeCaseMeta = [
  {
    id: 'active',
    title: 'No Active Events Scheduled',
    description:
      'There are currently no active interview events scheduled. As an admin, you can create new events to engage users and enhance their interview preparation experience.',
  },
  {
    id: 'completed',
    title: 'No Completed Events Yet',
    description:
      'There are no past interview events that have been completed. Start organizing events to track and manage completed interviews.',
  },
  {
    id: 'archived',
    title: 'No Archived Events Found',
    description:
      'There are currently no archived interview events. Archive completed events to maintain a record of past interviews.',
  },
];

export default async function ListEvent(props) {
  const status = props?.params?.status || 'active';
  const searchParams = props.searchParams;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  // Fetch both interview list and event details for metrics
  const [eventList, eventDetails] = await Promise.all([
    getInterviewByMember({
      tenantId,
      searchParams,
      membershipId: currentOrg?.id,
      role: currentOrg?.role,
    }),
    getEventDetailsOverview(
      tenantId,
      false,
      currentOrg?.id,
      currentOrg?.role,
    )
  ]);

  // Extract the items array from the eventList response
  const interviews = eventList?.items || [];

  if (!interviews || interviews?.length === 0) {
    return (
      <EdgeCaseCard
        title={edgeCaseMeta?.find((item) => item?.id === status)?.title || ''}
        description={edgeCaseMeta?.find((item) => item?.id === status)?.description || ''}
        ctaLabel="Create New Event"
        ctaHref="/create-interview"
      />
    );
  }

  return (
    <ModernInterviewDashboard
      eventList={interviews}
      eventDetails={{
        ...eventDetails,
        organizationId: tenantId,
        userId: session?.user?.id
      }}
      searchParams={searchParams || {}}
    />
  );
}
