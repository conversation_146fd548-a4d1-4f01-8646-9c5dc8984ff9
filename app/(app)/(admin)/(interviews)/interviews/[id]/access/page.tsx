import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { InterviewAccess } from '@/components/wrapper-screen/interview/interview-events-access';
import { authOptions } from '@/lib/auth';
import { getInterviewAccess, getStaffList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function InterViewEvent(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies().get('aceprepUserId')?.value;
  const eventId = props.params.id;
  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const [eventDetails, members] = await Promise.all([
    getInterviewAccess({
      eventId,
      membershipId: currentOrg?.id,
      role: currentOrg?.role,
    }),
    getStaffList({
      organizationId: tenantId,
      role: ['ADMIN', 'MEMBER'],
      userId: userId,
      organizationType: 'organization',
    }),
  ]);

  if (!eventDetails?.items) {
    redirect('/404');
  }
  const selectedMembers = eventDetails?.items?.MembershipOnEventDetails?.map((item) => ({
    ...item?.membership,
    MembershipOnEventDetailsId: item?.id,
  }));

  return (
    <InterviewAccess
      selectedMembers={selectedMembers}
      members={members}
      organizationId={tenantId}
      eventId={eventId}
      userId={userId}
    />
  );
}
