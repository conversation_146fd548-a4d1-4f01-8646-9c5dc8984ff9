import { SVGSkeleton, Skeleton } from '@/components/skeleton/skeleton';

export default function Loading() {
  return (
    <>
      <div className="relative h-full min-h-screen w-full overflow-x-hidden px-4 py-2">
        <div className="mt-3 flex h-full overflow-y-scroll pb-10">
          <div className="h-full w-full">
            <div className="flex items-center gap-4">
              <div className="inline-flex h-10 w-10 items-center justify-center border border-input transition-colors">
                <div className="flex justify-center">
                  <SVGSkeleton className="lucide-chevron-left h-[24px] w-[24px]" />
                </div>
              </div>
              <h3 className="leading-none tracking-tight">
                <Skeleton className="w-[136px] max-w-full" />
              </h3>
            </div>
            <div className="mx-auto mt-4 max-w-[990px] border">
              <div className="flex h-full w-full flex-col justify-between">
                <div className="h-full w-full">
                  <div className="space-y-6 p-4">
                    <div className="space-y-2">
                      <p>
                        <Skeleton className="w-[80px] max-w-full" />
                      </p>
                      <div className="relative h-3 w-full">
                        <div className="h-full w-full flex-1"></div>
                      </div>
                    </div>
                    <div className="relative">
                      <h1>
                        <Skeleton className="w-[128px] max-w-full" />
                      </h1>
                    </div>
                  </div>
                </div>
                <div className="h-full min-h-[350px] w-full px-4 md:min-h-[420px]">
                  <div className="w-full">
                    <div className="grid w-full grid-cols-1 gap-8 px-1 md:grid-cols-2">
                      <div className="col-span-1 flex h-20 w-full flex-col items-start space-y-2">
                        <label className="flex-[1]">
                          <Skeleton className="w-[112px] max-w-full" />
                        </label>
                        <div className="flex h-10 w-full flex-[6] border border-input px-3 py-2 file:border-0">
                          <Skeleton className="w-[160px] max-w-full" />
                        </div>
                      </div>
                      <div className="flex h-20 w-full flex-col items-start space-y-2">
                        <label className="flex w-full flex-[1] flex-row items-center justify-between gap-2">
                          <Skeleton className="w-[40px] max-w-full" />
                          <div>
                            <div className="flex justify-center">
                              <SVGSkeleton className="mr-1 h-[24px] w-[24px]" />
                            </div>
                          </div>
                        </label>
                        <div className="flex h-10 w-full flex-[6] border border-input px-3 py-2 file:border-0">
                          <Skeleton className="w-[120px] max-w-full" />
                        </div>
                      </div>
                      <div className="col-span-1 flex h-20 w-full flex-col items-start space-y-2">
                        <label className="flex-[1]">
                          <Skeleton className="w-[40px] max-w-full" />
                        </label>
                        <div className="flex w-full flex-[6] items-center justify-between border px-4 py-2">
                          <p>
                            <Skeleton className="w-[96px] max-w-full" />
                          </p>
                          <div className="flex justify-center">
                            <SVGSkeleton className="h-[24px] w-[24px]" />
                          </div>
                        </div>
                      </div>
                      <div className="relative col-span-1 flex h-20 w-full flex-col items-start space-y-2">
                        <label className="flex-[1]">
                          <Skeleton className="w-[144px] max-w-full" />
                          <span>
                            <Skeleton className="w-[72px] max-w-full" />
                          </span>
                        </label>
                        <div className="flex h-10 w-full flex-[6] border border-input px-3 py-2 file:border-0">
                          <Skeleton className="w-[112px] max-w-full" />
                        </div>
                      </div>
                      <div className="col-span-1 flex h-20 w-full flex-col items-start space-y-2">
                        <label className="flex-[1]">
                          <Skeleton className="w-[208px] max-w-full" />
                          <span>
                            <Skeleton className="w-[80px] max-w-full" />
                          </span>
                        </label>
                        <div className="flex h-10 w-full flex-[6] border border-input px-3 py-2 file:border-0">
                          <Skeleton className="w-[152px] max-w-full" />
                        </div>
                      </div>
                    </div>
                    <div className="relative col-span-1 mt-5 flex w-full flex-col items-start space-y-2 overflow-y-auto p-2">
                      <div className="flex w-full items-center">
                        <label className="flex-[1]">
                          <Skeleton className="w-[120px] max-w-full" />
                        </label>
                        <div className="inline-flex h-10 items-center justify-center px-4 py-2 transition-colors">
                          <Skeleton className="w-[104px] max-w-full" />
                        </div>
                      </div>
                      <div className="h-[350px] w-full flex-[6] overflow-y-auto border p-2">
                        <Skeleton className="w-[168px] max-w-full" />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="w-full py-4">
                  <div className="flex w-full flex-row-reverse justify-between">
                    <div className="mr-4 flex flex-row-reverse items-center gap-4">
                      <div className="inline-flex h-11 items-center justify-center px-8 transition-colors">
                        <Skeleton className="w-[32px] max-w-full" />
                      </div>
                    </div>
                    <div className="ml-4 flex flex-row-reverse items-center gap-4"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
