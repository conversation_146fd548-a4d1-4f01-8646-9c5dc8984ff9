import { cookies } from 'next/headers';

import NotFound from '@/app/not-found';
import { InterviewFormProvider } from '@/components/create-interview/provider';
import { CreateEventScreen } from '@/components/wrapper-screen/create-interview/create-event-screen';
import { authOptions } from '@/lib/auth';
import {
  fetchOrganizationVideoQuestion,
  fetchQuestion,
  fetchQuestionType,
  getInterviewAccess,
  getLevel,
  getQuizCategory,
  getQuizQuestion,
  getQuizTopics,
  getStaffList,
} from '@/services/apicall';
import { getCodingProblems, getFrontEndPractice } from '@/services/apicall';
import { getServerSession } from 'next-auth';

const CreateEvent = async (props) => {
  const userId = cookies().get('aceprepUserId')?.value;
  const tenantId = cookies().get('aceprepTenantId')?.value;

  const session: any = await getServerSession(authOptions);
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const eventId = props?.params?.id;

  const [
    level,
    codingProblems,
    frontendProblems,
    multipleChoiceQuestions,
    multipleChoiceCategory,
    multipleChoiceTopics,
    members,
    eventDetails,
    videoCategory,
    videoOrgQuestion,
    videoQuestion,
  ] = await Promise.all([
    getLevel(),
    getCodingProblems(),
    getFrontEndPractice(),
    getQuizQuestion({ organizationId: tenantId }),
    getQuizCategory(),
    getQuizTopics('all'),
    getStaffList({
      organizationId: tenantId,
      role: ['ADMIN', 'MEMBER'],
      userId: userId,
      organizationType: 'organization',
    }),
    getInterviewAccess({ eventId, membershipId: currentOrg?.id, role: currentOrg?.role }),
    fetchQuestionType(),
    fetchOrganizationVideoQuestion({ organizationId: tenantId }),
    fetchQuestion('all', {}),
  ]);

  if (!Boolean(eventDetails?.items)) {
    return <NotFound />;
  }
  const videoQuestions = videoQuestion?.map((item) => {
    const question = item?.questions;
    return question?.map((item2) => ({ ...item2, slug: item?.type }));
  });
  return (
    <InterviewFormProvider>
      <CreateEventScreen
        level={level}
        organizationId={tenantId}
        codingProblems={codingProblems?.result}
        frontendProblems={frontendProblems?.result}
        multipleChoiceQuestions={multipleChoiceQuestions?.result}
        eventId={eventId}
        userId={userId}
        group={{}}
        multipleChoiceCategory={multipleChoiceCategory}
        multipleChoiceTopics={multipleChoiceTopics}
        members={members}
        eventDetails={eventDetails?.items}
        videoCategory={videoCategory}
        videoQuestion={[
          ...videoQuestions.flatMap((group) => group.map((question) => ({ ...question }))),
          ...videoOrgQuestion?.map((item) => ({ ...item, slug: 'Custom Question' })),
        ]}
      />
    </InterviewFormProvider>
  );
};

export default CreateEvent;
