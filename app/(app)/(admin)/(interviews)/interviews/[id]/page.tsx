import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { InterviewCandidateTable } from '@/components/data-table/interview-candidate/data-table';
import { ModernInterviewCandidateDashboard } from '@/components/data-table/interview-candidate/modern-interview-candidate-dashboard';
import { authOptions } from '@/lib/auth';
import { CampedTableProvider } from '@/packages/shared-data-table/camped-table-provider';
import { getAllIntegration, getInterviewList, getStaffList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function InterViewEvent(props: any) {
  const eventId = props.params.id;
  const searchParams = props.searchParams;
  const session: any = await getServerSession(authOptions);

  const organizationId = cookies().get('aceprepTenantId')?.value;
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === organizationId) ?? {};

  const userId = cookies().get('aceprepUserId')?.value;

  const [interviewResult, members, platform] = await Promise.all([
    getInterviewList({
      id: eventId,
      organizationId,
      userId: userId,
      searchParams,
      membershipId: currentOrg?.id,
      role: currentOrg?.role,
    }),
    getStaffList({
      organizationId,
      role: ['ADMIN', 'MEMBER'],
      userId: userId,
      organizationType: 'organization',
    }),
    getAllIntegration({ organizationId }),
  ]);
  if (!interviewResult?.items) return redirect('/404');

  // Check if we should use the modern dashboard (you can add feature flags here)
  const useModernDashboard = true; // Set to true to use the new modern dashboard

  if (useModernDashboard) {
    return (
      <ModernInterviewCandidateDashboard
        tasksPromise={{
          data: interviewResult,
        }}
        eventId={eventId}
        userId={userId || ''}
        members={members}
        platform={platform
          ?.filter((item: any) => ['GOOGLE', 'OUTLOOK']?.includes(item?.platform))?.[0]
          ?.platform?.toLowerCase() || ''}
        currentOrg={currentOrg}
      />
    );
  }

  // Fallback to original table
  return (
    <CampedTableProvider>
      <InterviewCandidateTable
        members={members}
        tasksPromise={{
          data: interviewResult,
        }}
        eventId={eventId}
        userId={userId}
        platform={platform
          ?.filter((item: any) => ['GOOGLE', 'OUTLOOK']?.includes(item?.platform))?.[0]
          ?.platform?.toLowerCase() || ''}
      />
    </CampedTableProvider>
  );
}
