import { cookies } from 'next/headers';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import { InviteCandidatesScreen } from '@/components/organization/invite-candidates-screen';
import { authOptions } from '@/lib/auth';
import { getCandidatesCount, getCandidatesList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function InterViewEvent(props) {
  const session = await getServerSession(authOptions);
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const eventId = props.params.id;
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};
  const candidates = await getCandidatesCount({
    tenantId,
    membershipId: currentOrg?.id,
    role: currentOrg?.role
  });
  if (
    currentOrg?.organization?.eventLimit?.student <= candidates?.count &&
    currentOrg?.organization?.eventLimit?.student !== null
  ) {
    return (
      <EdgeCaseCard
        title="Candidate Limit Reached"
        description="The maximum number of Candidate has been added. Please contact us for further assistance."
        ctaLabel="Contact Us"
        ctaHref="/contact"
      />
    );
  }

  return (
    <InviteCandidatesScreen
      eventId={eventId}
      existingCount={candidates?.count}
      maxCandidate={currentOrg?.organization?.eventLimit?.student ?? null}
    />
  );
}
