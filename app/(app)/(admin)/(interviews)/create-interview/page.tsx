import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import { InterviewFormProvider } from '@/components/create-interview/provider';
import { CreateEventScreen } from '@/components/wrapper-screen/create-interview/create-event-screen';
import { authOptions } from '@/lib/auth';
import {
  fetchOrganizationVideoQuestion,
  fetchQuestion,
  fetchQuestionType,
  getLevel,
  getQuizCategory,
  getQuizQuestion,
  getQuizTopics,
  getStaffList,
  getTotalInterviewCount,
} from '@/services/apicall';
import { getCodingProblems, getFrontEndPractice } from '@/services/apicall';
import { getServerSession } from 'next-auth';

const CreateEvent = async (props) => {
  const userId = cookies().get('aceprepUserId')?.value;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const [session, eventCount]: any = await Promise.all([
    getServerSession(authOptions),
    getTotalInterviewCount(tenantId),
  ]);

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  if (
    currentOrg?.organization?.eventLimit?.event <= eventCount &&
    currentOrg?.organization?.eventLimit?.event !== null
  ) {
    return (
      <EdgeCaseCard
        title="Interview Creation Limit Reached"
        description="The maximum number of Interviews has been created. Please contact us for further assistance."
        ctaLabel="Contact Us"
        ctaHref="/contact"
      />
    );
  }
  const eventId = props?.searchParams?.id;

  const [
    level,
    codingProblems,
    frontendProblems,
    multipleChoiceQuestions,
    multipleChoiceCategory,
    multipleChoiceTopics,
    members,
    videoCategory,
    videoOrgQuestion,
    videoQuestion,
  ] = await Promise.all([
    getLevel(),
    getCodingProblems(),
    getFrontEndPractice(),
    getQuizQuestion({ organizationId: tenantId }),
    getQuizCategory(),
    getQuizTopics('all'),
    getStaffList({
      organizationId: tenantId,
      role: ['ADMIN', 'MEMBER'],
      userId: userId,
      organizationType: 'organization',
    }),
    fetchQuestionType(),
    fetchOrganizationVideoQuestion({ organizationId: tenantId }),
    fetchQuestion('all', {}),
  ]);
  const videoQuestions = videoQuestion?.map((item) => {
    const question = item?.questions;
    return question?.map((item2) => ({ ...item2, slug: item?.type }));
  });

  return (
    <InterviewFormProvider>
      <CreateEventScreen
        level={level}
        organizationId={tenantId}
        codingProblems={codingProblems?.result}
        frontendProblems={frontendProblems?.result}
        multipleChoiceQuestions={multipleChoiceQuestions?.result}
        eventId={eventId}
        userId={userId}
        multipleChoiceCategory={multipleChoiceCategory}
        multipleChoiceTopics={multipleChoiceTopics}
        members={members}
        eventDetails={{}}
        group={{}}
        videoCategory={videoCategory}
        videoQuestion={[
          ...videoQuestions.flatMap((group) => group.map((question) => ({ ...question }))),
          ...videoOrgQuestion?.map((item) => ({ ...item, slug: 'Custom Question' })),
        ]}
      />
    </InterviewFormProvider>
  );
};

export default CreateEvent;
