'use client';

import { Icon } from '@/icons';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';

// Modern gradient backgrounds with subtle shadows and better contrast
const modernColorSchemes = {
  pink: {
    bg: 'bg-gradient-to-br from-pink-500 to-pink-600',
    shadow: 'shadow-pink-500/20',
    ring: 'ring-pink-500/30',
  },
  purple: {
    bg: 'bg-gradient-to-br from-purple-500 to-purple-600',
    shadow: 'shadow-purple-500/20',
    ring: 'ring-purple-500/30',
  },
  blue: {
    bg: 'bg-gradient-to-br from-blue-500 to-blue-600',
    shadow: 'shadow-blue-500/20',
    ring: 'ring-blue-500/30',
  },
  turquoise: {
    bg: 'bg-gradient-to-br from-teal-500 to-cyan-600',
    shadow: 'shadow-teal-500/20',
    ring: 'ring-teal-500/30',
  },
  lightpink: {
    bg: 'bg-gradient-to-br from-pink-400 to-rose-500',
    shadow: 'shadow-pink-400/20',
    ring: 'ring-pink-400/30',
  },
  orange: {
    bg: 'bg-gradient-to-br from-orange-500 to-amber-600',
    shadow: 'shadow-orange-500/20',
    ring: 'ring-orange-500/30',
  },
  cyan: {
    bg: 'bg-gradient-to-br from-cyan-500 to-blue-500',
    shadow: 'shadow-cyan-500/20',
    ring: 'ring-cyan-500/30',
  },
  green: {
    bg: 'bg-gradient-to-br from-green-500 to-emerald-600',
    shadow: 'shadow-green-500/20',
    ring: 'ring-green-500/30',
  },
  yellow: {
    bg: 'bg-gradient-to-br from-yellow-400 to-orange-500',
    shadow: 'shadow-yellow-400/20',
    ring: 'ring-yellow-400/30',
  },
};

export const TemplateCard = ({ item }) => {
  const colorScheme = modernColorSchemes[item?.iconColor] || modernColorSchemes.blue;

  return (
    <Card className="group relative overflow-hidden border border-base-200 bg-gradient-to-t from-primary/5 to-card shadow-xs transition-all duration-300 hover:border-base-300 hover:shadow-xl hover:shadow-base-900/15 dark:bg-card dark:hover:border-base-600 flex-1 h-full">
      <CardContent className="p-6">
        <CardHeader className="mb-4 flex flex-row items-center gap-4 p-0">
          <div
            className={cn(
              'flex h-12 w-12 items-center justify-center rounded-xl shadow-lg transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl',
              colorScheme.bg,
              colorScheme.shadow,
              'ring-1',
              colorScheme.ring
            )}
          >
            <Icon name={item.iconName} className="h-6 w-6 text-white drop-shadow-sm" />
          </div>
          <div className="flex-1">
            <CardTitle className="text-xl font-semibold text-foreground transition-colors duration-200 group-hover:text-primary">
              {item?.role}
            </CardTitle>
            <CardDescription className="text-sm font-medium text-muted-foreground">
              {item?.level}
            </CardDescription>
          </div>
        </CardHeader>
        <CardDescription className="line-clamp-3 text-sm leading-relaxed text-muted-foreground">
          {item?.jobDescription}
        </CardDescription>
      </CardContent>

      {/* Modern accent border on hover */}
      <div
        className={cn(
          'absolute left-0 top-0 h-full w-1 transition-all duration-300 opacity-0 group-hover:opacity-100',
          colorScheme.bg
        )}
      />

      {/* Subtle inner highlight effect */}
      <div className="absolute inset-0 rounded-lg bg-gradient-to-br from-white/40 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:from-white/10" />
    </Card>
  );
};
