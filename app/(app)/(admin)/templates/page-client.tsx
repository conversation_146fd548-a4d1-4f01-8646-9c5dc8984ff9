'use client';

import Link from 'next/link';

import { TemplateCard } from './_components/template-card';

export const PageClient = ({ groupedData, isInterviewathon }) => {
  return (
    <div className="h-full overflow-scroll">
      <div className="mb-2">
        <div className="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3">
          {(groupedData as any)?.map((item, key) => (
            <Link
              key={key}
              href={`/${
                isInterviewathon ? 'interviewathon/create' : 'create-interview'
              }?key=${item?.key}`}
            >
              <TemplateCard item={item} />
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};
