import { cookies } from 'next/headers';

import HomeBottomCard from '@/components/cards/home-bottom-card';
import { authOptions } from '@/lib/auth';
import { getInterviewDetail } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies()?.get('aceprepTenantId')?.value ?? session?.memberships?.[0]?.organizationId;

  const filter = props?.searchParams?.filter ?? 'last1hour';
  const interviewPage = props?.searchParams?.interviewPage ?? 1;
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const [completedInterview] = await Promise.all([
    getInterviewDetail(
      tenantId,
      'completedTime',
      filter,
      interviewPage,
      currentOrg?.id,
      currentOrg?.role,
    ),
  ]);

  return (
    <HomeBottomCard
      title={'Recent Interviews'}
      totalCount={completedInterview?.totalCount}
      screen="interviews"
      tabs={[
        {
          title: 'Completed',
          value: 'completed',
          header: ['Candidate/Interview', 'Score', 'Completed Time'],
          row: completedInterview?.eventDetails?.map((item) => {
            return {
              id: item?.id,
              eventId: item?.eventDetails?.id,
              interview: item?.eventDetails?.name,
              score: item?.feedback?.overall_score,
              recommendation: item?.feedback?.overall_recommendation?.decision,
              candidate:
                item?.user?.userProfile?.fullName !== '' || item?.user?.userProfile?.fullName
                  ? item?.user?.userProfile?.fullName
                  : item?.user?.email,
              time: item?.completedTime,
            };
          }),
        },
      ]}
      page={interviewPage}
    />
  );
}
