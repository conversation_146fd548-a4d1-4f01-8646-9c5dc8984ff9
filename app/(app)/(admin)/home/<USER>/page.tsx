import { cookies } from 'next/headers';

import HomeTopCard from '@/components/cards/home-top-card';
import { authOptions } from '@/lib/auth';
import { getInterviewOverview } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies()?.get('aceprepTenantId')?.value ?? session?.memberships?.[0]?.organizationId;
  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const filter = props?.searchParams?.filter ?? 'last1hour';
  const [interviewOverview] = await Promise.all([
    getInterviewOverview(tenantId, filter, currentOrg?.id, currentOrg?.role),
  ]);

  return (
    <HomeTopCard
      data={[
        {
          label: 'Candidates invited',
          value: interviewOverview?.count?.invitedCount ?? 0,
          icon: 'Send',
          bottomLabel: 'Candidates',
        },
        {
          label: 'Completed',
          value: interviewOverview?.count?.completedCount ?? 0,
          icon: 'CircleCheck',
          bottomLabel: 'Candidates',
        },
        {
          label: 'Partially completed',
          value: interviewOverview?.count?.partialCount ?? 0,
          icon: 'CircleHelp',
          bottomLabel: 'Candidates',
        },
        {
          label: 'Feedback mail sent',
          value: interviewOverview?.count?.feedbackSentCount ?? 0,
          icon: 'MailCheck',
          bottomLabel: 'Candidates',
        },
        {
          label: 'Feedback mail yet to be sent',
          value: interviewOverview?.count?.feedbackNotSentCount ?? 0,
          icon: 'MailQuestion',
          bottomLabel: 'Candidates',
        },
      ]}
    />
  );
}
