import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import HomeBottomCard from '@/components/cards/home-bottom-card';
import { authOptions } from '@/lib/auth';
import { getActiveStudentDetail } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies()?.get('aceprepTenantId')?.value ?? session?.memberships?.[0]?.organizationId;

  const filter = props?.searchParams?.filter ?? 'last1hour';
  const feedbackPage = props?.searchParams?.studentPage ?? 1;

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};
  const isSuperUser = ['admin', 'owner'].includes(currentOrg?.role?.toLowerCase());
  const student = await getActiveStudentDetail(
    tenantId,
    filter,
    feedbackPage,
    isSuperUser ? undefined : currentOrg?.id,
  );

  return (
    <HomeBottomCard
      title={'Active Student'}
      totalCount={student?.totalCount}
      screen="activeStudent"
      tabs={[
        {
          header: ['Student', 'Interviewathons', 'Practices'],
          row: student?.userDetails?.map((item) => {
            return {
              id: item?.userId,
              practices: (item?.totalEvents ?? 0) - (item?.interviewathonEvents ?? 0),
              score: item?.interviewathonEvents ?? 0,
              interview: item?.fullName && item?.fullName !== '' ? item?.fullName : item?.email,
            };
          }),
        },
      ]}
      page={feedbackPage}
    />
  );
}
