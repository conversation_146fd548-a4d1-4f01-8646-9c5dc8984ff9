import { cookies } from 'next/headers';
import Link from 'next/link';

import { authOptions } from '@/lib/auth';
import { leaderboardDetails } from '@/services/apicall';
import { capitalizeFirstLetter } from '@/utils/string.helper';
import { getServerSession } from 'next-auth';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Card, CardTitle } from '@camped-ui/card';

export default async function ListEvent(props) {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies()?.get('aceprepTenantId')?.value ?? session?.memberships?.[0]?.organizationId;

  const leaderboard = await leaderboardDetails({
    organizationId: tenantId,
    searchParams: { page: 1, per_page: 3 },
  });

  const topStudents = leaderboard?.userProfile.sort((a, b) => a.rank - b.rank);
  return (
    <Card className="h-[60vh] border-none shadow-none">
      <div className="my-4 flex items-center justify-between">
        <CardTitle className="pl-4 text-xl font-semibold">Leaderboard</CardTitle>
        <Link href="/leaderboard">
          <Button variant="link" className="flex w-full justify-center">
            {'See More'}
          </Button>
        </Link>
      </div>
      <br />
      <br />
      <div className="flex items-end justify-center gap-2 rounded-md">
        {topStudents?.map((item, key) => <LeaderboardChart key={key} student={item} />)}
      </div>
    </Card>
  );
}
const LeaderboardChart = ({ student }) => {
  const getProps = (rank) => {
    if (rank == 1) return `bg-yellow-100 h-44`;
    if (rank == 2) return `h-36 bg-gray-100`;
    if (rank == 3) return `h-32 bg-gray-200`;
  };

  return (
    <div className={`${student?.rank === 2 ? 'order-first' : ''} flex flex-col gap-8`}>
      <div className="flex flex-col items-center">
        <Avatar>
          <AvatarImage
            src={`${process.env.NEXT_PUBLIC_CAMPED_ACCOUNTS_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_ACCOUNTS_ENVIRONMENT}/user-profile/${student?.userId}`}
          />
          <AvatarFallback>{capitalizeFirstLetter(student?.fullName?.[0])} </AvatarFallback>
        </Avatar>
        <span className="mt-2 text-sm font-medium">{student?.fullName} </span>
      </div>
      <div className="flex flex-col items-center">
        <div
          className={`flex w-32 flex-col items-center justify-center rounded-t-lg ${getProps(
            student?.rank,
          )}`}
        >
          <span className="text-sm font-medium">{student?.rank}</span>
          <Badge variant="secondary" className="mt-2">
            {`${student?.totalFinalScore} Score`}
          </Badge>
        </div>
      </div>
    </div>
  );
};
