import { cookies } from 'next/headers';

import { AddQuestionScreen } from '@/components/wrapper-screen/question-bank/add-question-screen';
import { getQuizCategory, getQuizQuestion, getQuizTopics } from '@/services/apicall';

export default async function Page(props) {
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies().get('aceprepUserId')?.value;

  const [topic, category] = await Promise.all([getQuizTopics('all'), getQuizCategory()]);

  return (
    <AddQuestionScreen
      topic={topic}
      category={category}
      organizationId={tenantId}
      userId={userId}
    />
  );
}
