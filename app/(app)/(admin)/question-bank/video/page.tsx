import { cookies } from 'next/headers';

import MCQQuestionBankScreen from '@/components/wrapper-screen/question-bank/mcq-question-bank-screen';
import {
  fetchOrganizationVideoQuestion,
  fetchQuestion,
  fetchQuestionType,
} from '@/services/apicall';

export default async function Page(props) {
  //   const session: any = await getServerSession(authOptions);
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const [category] = await Promise.all([fetchQuestionType()]);
  const selectedCategory = props?.searchParams?.category ?? category?.[0]?.name;

  let question;
  if (selectedCategory === 'Custom Question') {
    question = await fetchOrganizationVideoQuestion({ organizationId: tenantId });
  } else {
    question = await fetchQuestion(selectedCategory, {});
  }

  return (
    <MCQQuestionBankScreen
      questions={question}
      category={category}
      type="video"
      selectedCategory={selectedCategory}
    />
  );
}
