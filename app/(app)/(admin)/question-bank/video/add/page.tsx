import { cookies } from 'next/headers';

import { AddVideoQuestionScreen } from '@/components/wrapper-screen/question-bank/add-video-question-screen';
import { getReferenceVideo } from '@/services/apicall';

export default async function Page(props) {
  const organizationId = cookies().get('aceprepTenantId')?.value;
  const userId = cookies().get('aceprepUserId')?.value;
  const referenceVideo = await getReferenceVideo({ organizationId });
  return (
    <AddVideoQuestionScreen
      organizationId={organizationId}
      userId={userId}
      referenceVideo={referenceVideo}
    />
  );
}
