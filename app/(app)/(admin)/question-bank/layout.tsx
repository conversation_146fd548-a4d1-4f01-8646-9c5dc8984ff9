import { headers } from 'next/headers';
import Link from 'next/link';

import PageHeader from '@/components/page-header';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GlobalPanel } from '@/layout/global-panel';

import { Button } from '@camped-ui/button';

export default async function Layout({ children }) {
  const pathname = (await headers().get('x-next-pathname')) as string;
  const tab = pathname?.split('/')?.[2];
  return (
    <GlobalPanel title="Question Bank">
      <div className="flex w-full items-center justify-between gap-2 overflow-x-hidden">
        <Tabs defaultValue={tab ?? 'video'}>
          <TabsList className="bg-gray-200">
            <Link href={'/question-bank/video'}>
              <TabsTrigger value="video">Video</TabsTrigger>
            </Link>
            <Link href={'/question-bank/mcq'}>
              <TabsTrigger value="mcq">MCQ</TabsTrigger>
            </Link>
          </TabsList>
        </Tabs>
        <Link href={`/question-bank/${tab}/add`}>
          <Button size="sm">Add Question</Button>
        </Link>
      </div>
      {children}
    </GlobalPanel>
  );
}
