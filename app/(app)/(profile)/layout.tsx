import { headers } from 'next/headers';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { SubLayoutWrapper } from '@camped-layout/sub-base';

import { Card } from '@camped-ui/card';

const menuContents = [
  {
    title: 'Profile',
    href: '/profile',
    active: ['/profile'],
  },
  {
    title: 'Preferences',
    href: '/preferences',
    active: ['/preferences'],
  },
  {
    title: 'My Plans',
    href: '/my-plans',
    active: ['/my-plans'],
  },
];
const menu = [
  {
    title: 'Profile',
    href: '/profile',
    active: ['/profile'],
  },
  {
    title: 'Preferences',
    href: '/preferences',
    active: ['/preferences'],
  },
];

const data = [
  {
    subLayoutTitle: 'Profile',
    subLayoutDescription: 'This is how others will see you on the site.',
    pathName: '/profile',
  },
  {
    subLayoutTitle: 'Preferences',
    subLayoutDescription:
      'Customize the appearance of the app. Automatically switch between day and night themes.',
    pathName: '/preferences',
  },
  {
    subLayoutTitle: 'My Plans',
    subLayoutDescription: `View and manage your subscription plans, billing details, and payment preferences.`,
    pathName: '/my-plans',
  },
];

export default async function PageLayout({ children }: { children: React.ReactNode }) {
  const pathname = (await headers().get('x-next-pathname')) as string;
  const userId = cookies()?.get('aceprepUserId')?.value;

  if (!userId) {
    return redirect('/404');
  }
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const selectedItem = data?.find((item) => item?.pathName === pathname);
  return (
    <Card>
      <SubLayoutWrapper
        pathname={pathname}
        menuContents={
          !tenantId && process.env.NEXT_PUBLIC_PLATFORM === 'aceprep' ? menuContents : menu
        }
        mainTitle="Profile Settings"
        mainDescription="Manage your account settings"
        subLayoutTitle={(selectedItem as any)?.subLayoutTitle}
        subLayoutDescription={(selectedItem as any)?.subLayoutDescription}
      >
        {children}
      </SubLayoutWrapper>
    </Card>
  );
}
