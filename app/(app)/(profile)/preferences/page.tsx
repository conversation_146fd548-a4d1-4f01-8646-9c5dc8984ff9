'use client';

import React from 'react';

import { setCookie } from '@/utils/cookies';
import { useTheme } from 'next-themes';
import { useForm } from 'react-hook-form';

import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@camped-ui/form';
import { cn } from '@camped-ui/lib';
import { RadioGroup } from '@camped-ui/radio-group';

export default function App() {
  const { theme, setTheme } = useTheme();
  const form = useForm();
  return (
    <div className="flex items-center gap-10">
      <Form {...form}>
        <form className="space-y-8">
          <FormField
            control={form.control}
            name="theme"
            render={({ field }) => (
              <FormItem className="space-y-1">
                <FormLabel>Theme</FormLabel>
                <FormDescription>The dashboard uses light theme. Dark theme is not available.</FormDescription>
                <FormMessage />
                <div className="grid max-w-md grid-cols-1 gap-8 pt-2">
                  <FormItem>
                    <FormLabel>
                      <div
                        className="border-primary items-center rounded-md border-2 p-1"
                      >
                        <div className="space-y-2 rounded-sm bg-[#ecedef] p-2">
                          <div className="space-y-2 rounded-md bg-white p-2 shadow-sm">
                            <div className="h-2 w-[80px] rounded-lg bg-[#ecedef]" />
                            <div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
                          </div>
                          <div className="flex items-center space-x-2 rounded-md bg-white p-2 shadow-sm">
                            <div className="h-4 w-4 rounded-full bg-[#ecedef]" />
                            <div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
                          </div>
                          <div className="flex items-center space-x-2 rounded-md bg-white p-2 shadow-sm">
                            <div className="h-4 w-4 rounded-full bg-[#ecedef]" />
                            <div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
                          </div>
                        </div>
                      </div>
                      <span className="block w-full p-2 text-center font-normal">Light Theme</span>
                    </FormLabel>
                  </FormItem>
                </div>
              </FormItem>
            )}
          />
        </form>
      </Form>
    </div>
  );
}
