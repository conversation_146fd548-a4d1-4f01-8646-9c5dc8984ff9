import { NextResponse } from 'next/server';

import { OpenAIStreamPayload } from '@/lib/OpenAIStream';
import retryOpenAI from '@/lib/retryOpenApi';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';

export const maxDuration = 299;

const generateFeedback = async (messages) => {
  const payload: OpenAIStreamPayload = {
    model: process.env.AZURE_OPEN_AI_MODEL || '',
    messages,
    temperature: 0.7,
    top_p: 0.8,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: false,
    n: 1,
    response_format: { type: 'json_object' },
  };
  const result = await retryOpenAI(payload, 3, true);
  return result;
};
export async function POST(request: any) {
  const { eventId, jd } = await request.json();
  if (jd?.length > 0) {
    try {
      const feedback = await generateFeedback([
        {
          role: 'system',
          content: await generatePrompt('SKILL_EXTRACTION_SYSTEM_PROMPT_STRATEGY', {}),
        },
        {
          role: 'user',
          content: await generatePrompt('SKILL_EXTRACTION_USER_PROMPT_STRATEGY', {
            jobDescription: jd ?? '',
          }),
        },
      ]);

      if (feedback?.skills) {
        return NextResponse.json({
          skills: feedback?.skills,
          message: 'Skill extraction is done',
          status: 200,
        });
      }
      return NextResponse.json({
        error: 'Error extracting skill from jd',
        status: 500,
      });
    } catch (error) {
      console.log({ error });
      return NextResponse.json({
        error: 'Error extracting skill from jd',
        status: 500,
      });
    }
  } else {
    try {
      const event = await db.eventDetails.findUnique({ where: { id: eventId } });

      if (!event?.jobDescription) {
        return NextResponse.json({
          message: 'No job description found',
          status: 200,
        });
      }

      const feedback = await generateFeedback([
        {
          role: 'system',
          content: await generatePrompt('SKILL_EXTRACTION_SYSTEM_PROMPT_STRATEGY', {}),
        },
        {
          role: 'user',
          content: await generatePrompt('SKILL_EXTRACTION_USER_PROMPT_STRATEGY', {
            jobDescription: event?.jobDescription ?? '',
          }),
        },
      ]);

      if (feedback?.skills) {
        let update = await db.eventDetails.update({
          where: { id: eventId },
          data: {
            skillsAndKeywords: feedback?.skills,
          },
        });
        return NextResponse.json({
          feedback,
          message: 'Skill extraction is done',
          status: 200,
        });
      }
      return NextResponse.json({
        error: 'Error extracting skill from jd',
        status: 500,
      });
    } catch (error) {
      console.log({ error });
      return NextResponse.json({
        error: 'Error extracting skill from jd',
        status: 500,
      });
    }
  }
}
