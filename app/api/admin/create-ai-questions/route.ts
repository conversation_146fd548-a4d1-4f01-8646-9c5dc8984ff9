import { NextResponse } from 'next/server';

import { OpenAIStreamPayload } from '@/lib/OpenAIStream';
import retryOpenAI from '@/lib/retryOpenApi';
import { generatePrompt } from '@/prompt-service';
import { getToken } from 'next-auth/jwt';

export const maxDuration = 299;

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing env var from OpenAI');
}

export async function POST(request: any) {
  const token = await getToken({ req: request });
  if (!token)
    return NextResponse.json({
      error: 'Unauthorized',
      status: 401,
    });
  const input = await request.json();
  const { level, jobDescription, role, topics, category } = input;

  const getContent = async () => {
    if (role && level) {
      return await generatePrompt('VIDEO_AI_QUESTION_PROMPT_STRATEGY', {
        role,
        level,
        jobDescription,
      });
    } else if (category && topics) {
      return await generatePrompt('MCQ_QUESTION_CATEGORY_PROMPT_STRATEGY', {
        category,
        topics,
      });
    } else if (category) {
      return await generatePrompt('MCQ_QUESTION_CATEGORY_PROMPT_STRATEGY', {
        category,
        topics: getTopic(category),
      });
    }
    return await generatePrompt('MCQ_QUESTION_WITHOUT_CATEGORY_PROMPT_STRATEGY', {});
  };
  const content = await getContent();

  const payload: OpenAIStreamPayload = {
    model: process.env.AZURE_OPEN_AI_MODEL || '',
    messages: [
      {
        role: 'system',
        content,
      },
    ],
    temperature: 0.7,
    top_p: 0.8,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: false,
    n: 1,
    response_format: { type: 'json_object' },
  };
  const { questions } = await retryOpenAI(payload, 3, true);

  return NextResponse.json(
    {
      questions,
    },
    { status: 200 },
  );
}

const getTopic = (topic) => {
  switch (topic) {
    case 'general-aptitude':
      return [
        'problems-on-trains',
        'time-and-distance',
        'height-and-distance',
        'time-and-work',
        'simple-interest',
      ];
    case 'verbal-ability':
      return [
        'vocabulary',
        'grammar-and-sentence-correction',
        'synonyms-and-antonyms',
        'analogies',
      ];
    case 'logical-reasoning':
      return [
        'analytical-reasoning',
        'data-interpretation',
        'syllogisms',
        'blood-relations',
        'critical-reasoning',
      ];
    case 'current-affairs-and-gk':
      return ['current-affairs', 'general-knowledge'];
  }
};
