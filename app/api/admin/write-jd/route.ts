import { NextResponse } from 'next/server';

import { AzureOpenAI } from '@/lib/ai-models';
import { Gemini } from '@/lib/ai-models/src/models/gemini';
import { GeminiConfiguration } from '@/lib/ai-models/src/types/gemini_model';
import { AzureOpenAIConfiguration } from '@/lib/ai-models/types';
import { generatePrompt } from '@/prompt-service';
import { getToken } from 'next-auth/jwt';

export const maxDuration = 299;

const API_KEY = process.env.NEXT_PUBLIC_GEMINI_API_KEY || '';

export async function POST(request: any) {
  const token = await getToken({ req: request });
  if (!token)
    return NextResponse.json({
      error: 'Unauthorized',
      status: 401,
    });
  const { role, level, jobDescription } = await request.json();

  const prompt = await generatePrompt('JOB_DESCRIPTION_PROMPT_STRATEGY', {
    role,
    level,
    jobDescription,
  });

  try {
    // return await azureOpenAI(prompt)
    const stream = await googleGemini(prompt);
    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
      },
    });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({
      error: 'Error occurred while creating job description',
      status: 500,
    });
  }
}

const azureOpenAI = async (prompt) => {
  const config: AzureOpenAIConfiguration = {
    basePath: process.env.AZURE_OPEN_AI_GPT_4O_BASE || '',
    apiKey: process.env.AZURE_OPENAI_GPT_4O_API_KEY || '',
    chatVersion: process.env.AZURE_OPEN_AI_CHAT_VERSION,
  };
  const azure = new AzureOpenAI(config);

  const stream = await azure.createChatCompletion({
    messages: [
      {
        role: 'system',
        content: prompt,
      },
    ],
    stream: true, // stream the response
    model: 'gpt-4o', // process.env.AZURE_OPEN_AI_CHAT_VERSION || '',
    temperature: 0.5,
  });

  return new Response(stream);
};

const googleGemini = async (prompt) => {
  const config: GeminiConfiguration = {
    apiKey: API_KEY,
    model: process.env.NEXT_PUBLIC_GCP_MODEL || 'gemini-1.5-flash',
  };

  const gemini = new Gemini(config);

  const stream = await gemini.streamTextWithGemini(prompt);
  return stream;
};
