import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export const maxDuration = 299;

export async function POST(request: any) {
  const input = await request.json();

  const token = await getToken({ req: request });

  const {
    name,
    role,
    level,
    jobDescription,
    isProctor,
    questions,
    isAiQuestion,
    organizationId,
    round,
    isPlacement,
    timing,
    inviteCandidates,
    customTopics,
    userId,
    members,
    skillsAndKeywords,
    evaluation,
    employment_type,
    assessmentCriteria,
  } = input;

  if (!token?.email) {
    throw new Error('Not Authorized to make this call');
  }

  const user = await db.user.findUnique({
    where: {
      email: token?.email?.toLowerCase(),
    },
  });

  if (!user) {
    return NextResponse.json(
      {
        message: 'User not found',
      },
      { status: 400 },
    );
  }

  const memberResponse = await db.membership.findFirst({
    where: {
      organizationId: organizationId,
      userId: userId,
      role: { in: ['OWNER', 'MEMBER', 'ADMIN'] },
    },
  });

  if (!memberResponse) {
    return NextResponse.json(
      {
        message: 'Your are not part of Organization',
      },
      { status: 400 },
    );
  }

  const response = await db.eventDetails.create({
    data: {
      name: name,
      organizationId: organizationId,
      role: role,
      level: level,
      questions: questions,
      jobDescription: jobDescription,
      isProctor: isProctor,
      isAiQuestion: isAiQuestion,
      isPlacement,
      evaluation,
      employment_type,
      round: round,
      timing: timing,
      customTopics: customTopics || '',
      assessmentCriteria: assessmentCriteria || undefined,
      assessmentTemplateId: undefined, // No longer using single template approach
      createdById: userId,
      skillsAndKeywords: skillsAndKeywords ?? [],
      MembershipOnEventDetails: {
        create: members?.map((item: any) => ({
          membership: {
            connect: {
              id: item?.id,
            },
          },
        })),
      },
    },
  });

  if (!response?.id) {
    return NextResponse.json(
      {
        message: 'Failed to create an Intevriew',
      },
      { status: 400 },
    );
  }

  let createAndInviteMembers;
  let invitedCandidates;

  if (inviteCandidates?.length) {
    const createCandidatesUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/server/admin/create-bulk-candidate`;
    const userAndMembershipRes = await fetch(createCandidatesUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        emails: inviteCandidates,
        organizationId: organizationId,
      }),
    });
    createAndInviteMembers = await userAndMembershipRes.json();
    const sendInvite = createAndInviteMembers?.items || createAndInviteMembers?.createdEmails;
    const emails = sendInvite?.map((item) => item?.value?.email);
    const inviteUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/admin/create-event-invitation`;
    const inviteResponse = await fetch(inviteUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ users: emails, eventId: response?.id }),
    });
    invitedCandidates = await inviteResponse.json();
  }

  if (response && response?.id) {
    let message = '';
    if (inviteCandidates?.length && invitedCandidates?.result === 'Invited Successfully') {
      message = createAndInviteMembers?.failedEmails
        ? 'Some of the candidates are failed to invite'
        : 'Candidates are invited Successfully';
    } else {
      message = 'Failed to invite Candidates';
    }

    return NextResponse.json(
      {
        result: 'Event Created Successfully',
        message: message,
      },
      { status: 200 },
    );
  }
}
