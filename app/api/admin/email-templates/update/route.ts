import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export async function POST(request: any) {
  const token = await getToken({ req: request });
  if (!token)
    return NextResponse.json({
      error: 'Unauthorized',
      status: 401,
    });
  const { email, id } = await request.json();

  try {
    let emailTemplates = await db.emailTemplates.update({
      where: { id: id },
      data: { ...email },
    });
    return NextResponse.json({
      emailTemplates,
      status: 200,
    });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Interview Not Found', status: 500 });
  }
}
