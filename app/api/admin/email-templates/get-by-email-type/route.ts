import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export async function GET(request: any) {
  const token = await getToken({ req: request });

  // if (!token)
  //   return NextResponse.json({
  //     error: 'Unauthorized',
  //     status: 401,
  //   })
  const { searchParams } = new URL(request.url);
  const emailType = searchParams.get('type');
  const organizationId = searchParams.get('organizationId');

  try {
    let emailTemplate = await db.emailTemplates.findFirst({
      where: {
        emailType: emailType ?? '',
        organizationId: organizationId ?? '',
      },
    });
    return NextResponse.json({
      emailTemplate,
      status: 200,
    });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({
      error: 'Email Templates Not Found',
      status: 500,
    });
  }
}
