import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export async function POST(request: any) {
  const { candidateIds, aiEventId, organizationId } = await request.json();

  // Get user session to determine membership
  const token = await getToken({ req: request });
  if (!token?.email) {
    return NextResponse.json({ error: 'Not authorized' }, { status: 401 });
  }





  const user = await db.user.findUnique({
      where: {
        email: token?.email?.toLowerCase(),
      },
      include: {
        memberships: {
          include: {
            organization: true,
          },
        },
      },
    });
    const currentOrg: any = user?.memberships?.find(
      (item) => item?.organizationId === organizationId,
    );

    if (!currentOrg?.organizationId) {
      return NextResponse.json({ error: 'User not found or not a member of this organization' }, { status: 403 });
    }

    console.log({user})


  const membership = user.memberships[0];
  const membershipId = membership.id;
  const role = membership.role;

  if (!candidateIds || !Array.isArray(candidateIds) || candidateIds.length === 0) {
    return NextResponse.json({ error: 'Candidate IDs required' }, { status: 400 });
  }

  if (!aiEventId) {
    return NextResponse.json({ error: 'AI Event ID required' }, { status: 400 });
  }

  if (!organizationId) {
    return NextResponse.json({ error: 'Organization ID required' }, { status: 400 });
  }

  try {
    // Verify the AI event exists and is accessible
    let eventWhere: any = {
      id: aiEventId,
      organizationId: organizationId,
      isAiQuestion: true,
      status: 'ACTIVE',
    };

    // Apply membership-based filtering for non-admin/owner roles
    if (!['admin', 'owner']?.includes(role?.toLowerCase())) {
      eventWhere.MembershipOnEventDetails = {
        some: {
          membershipId,
        },
      };
    }

    const aiEvent = await db.eventDetails.findFirst({
      where: eventWhere,
      select: {
        id: true,
        name: true,
        role: true,
        level: true,
        timing: true,
        organizationId: true,
      },
    });

    if (!aiEvent) {
      return NextResponse.json({ error: 'AI Event not found or not accessible' }, { status: 404 });
    }

    // Get candidate details from CareerPractice records
    const candidates = await db.careerPractice.findMany({
      where: {
        id: { in: candidateIds },
        eventDetails: {
          organizationId: organizationId,
        },
      },
      select: {
        id: true,
        userId: true,
        user: {
          select: {
            id: true,
            email: true,
            name: true,
          },
        },
      },
    });

    if (candidates.length === 0) {
      return NextResponse.json({ error: 'No valid candidates found' }, { status: 404 });
    }

    const results: Array<{
      candidateId: string;
      email: string;
      status: 'success' | 'already_assigned' | 'error';
      careerPracticeId?: string;
      message: string;
    }> = [];

    // Process each candidate
    for (const candidate of candidates) {
      try {
        // Check if candidate is already assigned to this AI event
        const existingAssignment = await db.careerPractice.findFirst({
          where: {
            userId: candidate.userId,
            eventId: aiEvent.id,
          },
        });

        if (existingAssignment) {
          results.push({
            candidateId: candidate.id,
            email: candidate.user.email,
            status: 'already_assigned',
            message: 'Candidate already assigned to this AI event',
          });
          continue;
        }

        // Update the current CareerPractice status to 'Accepted'
        await db.careerPractice.update({
          where: { id: candidate.id },
          data: {
            resultStatus: 'Accepted',
            comments: [{
              status: "Accepted",
              message: "Moved to AI Round",
              withLink: false,
              sendEmail: false,
              commentedBy: user.id
            }],
            updatedAt: new Date(),
          },
        });

        const conversation =[{"id": "74e0e899-e80f-4312-8ec3-1a8e4049749e", "s3Id": "", "round": "video-interview", "answer": "", "question": "Welcome! Walk me through your resume", "isAnswered": false}];

        // Create CareerPractice record for AI event without sending invite
        const careerPractice = await db.careerPractice.create({
          data: {
            userId: candidate.userId,
            role: aiEvent.role,
            level: aiEvent.level,
            conversation,
            event: aiEvent.name,
            eventId: aiEvent.id,
            timing: {
              ...aiEvent.timing,
              inviteTime: null, // No invite sent
              assignedTime: new Date(), // Track when assigned
            },
            source: 'MOVED_TO_AI_ROUND',
          },
        });

        results.push({
          candidateId: candidate.id,
          email: candidate.user.email,
          status: 'success',
          careerPracticeId: careerPractice.id,
          message: 'Successfully moved to AI round',
        });
      } catch (error) {
        console.error(`Error processing candidate ${candidate.id}:`, error);
        results.push({
          candidateId: candidate.id,
          email: candidate.user.email,
          status: 'error',
          message: 'Failed to move to AI round',
        });
      }
    }

    const successCount = results.filter((r) => r.status === 'success').length;
    const alreadyAssignedCount = results.filter((r) => r.status === 'already_assigned').length;
    const errorCount = results.filter((r) => r.status === 'error').length;

    return NextResponse.json(
      {
        message: `Processed ${candidates.length} candidates: ${successCount} moved, ${alreadyAssignedCount} already assigned, ${errorCount} errors`,
        results,
        summary: {
          total: candidates.length,
          success: successCount,
          alreadyAssigned: alreadyAssignedCount,
          errors: errorCount,
        },
        aiEvent: {
          id: aiEvent.id,
          name: aiEvent.name,
          role: aiEvent.role,
          level: aiEvent.level,
        },
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Error moving candidates to AI round:', error);
    return NextResponse.json(
      {
        error: 'Error moving candidates to AI round',
      },
      { status: 500 },
    );
  }
}
