import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export async function POST(request: any) {
  const { organizationId } = await request.json();

  // Get user session to determine membership
  const token = await getToken({ req: request });
  if (!token?.email) {
    return NextResponse.json({ error: 'Not authorized' }, { status: 401 });
  }

  // Get user's membership in the organization
  const user = await db.user.findUnique({
    where: { email: token.email.toLowerCase() },
    include: {
      memberships: {
        where: { organizationId },
        select: { id: true, role: true }
      }
    }
  });

  if (!user || !user.memberships.length) {
    return NextResponse.json({ error: 'User not found or not a member of this organization' }, { status: 403 });
  }

  const membership = user.memberships[0];
  const membershipId = membership.id;
  const role = membership.role;

  if (!organizationId) {
    return NextResponse.json({ error: 'Organization ID required' }, { status: 400 });
  }

  try {
    // Build where clause for AI events
    let where: any = {
      organizationId: organizationId,
      isAiQuestion: true, // Only AI interviews
      status: 'ACTIVE', // Only active events
    };

    // Apply membership-based filtering for non-admin/owner roles
    if (!['admin', 'owner']?.includes(role?.toLowerCase())) {
      where.MembershipOnEventDetails = {
        some: {
          membershipId,
        },
      };
    }

    const aiEvents = await db.eventDetails.findMany({
      select: {
        id: true,
        name: true,
        role: true,
        level: true,
        status: true,
        createdAt: true,
        _count: {
          select: { careerPractices: true },
        },
      },
      where,
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(
      {
        items: aiEvents,
        count: aiEvents.length,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Error fetching AI events:', error);
    return NextResponse.json(
      {
        error: 'Error fetching AI events',
      },
      { status: 500 },
    );
  }
}
