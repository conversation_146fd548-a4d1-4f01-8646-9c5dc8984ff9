import { NextResponse } from 'next/server';

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const statusId = searchParams.get('id');
  try {
    let url = `${process.env.NEXT_PUBLIC_AI_AVATAR_API_URL}/status/${statusId}`;
    const response = await fetch(url, {
      method: 'GET',
      cache: 'no-store',
    });
    const data = await response.json();

    return NextResponse.json(
      {
        result: data,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('An error occurred:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
