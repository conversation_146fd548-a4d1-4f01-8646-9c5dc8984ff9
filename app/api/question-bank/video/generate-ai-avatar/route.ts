import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export async function POST(request: any) {
  const token = await getToken({ req: request });
  if (!token)
    return NextResponse.json({
      error: 'Unauthorized',
      status: 401,
    });
  const data = await request.json();

  try {
    const url = `${process.env.NEXT_PUBLIC_AI_AVATAR_API_URL}/inference`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': '123456',
      },
      body: JSON.stringify(data),
    });
    const res = await response?.json();
    return NextResponse.json({ res }, { status: 200 });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'create customQuestions failed' }, { status: 500 });
  }
}
