import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export async function POST(request: any) {
  const token = await getToken({ req: request });
  if (!token)
    return NextResponse.json({
      error: 'Unauthorized',
      status: 401,
    });
  const { question } = await request.json();

  try {
    let customQuestions = await db.customQuestions.create({
      data: { ...question },
    });
    return NextResponse.json({ customQuestions }, { status: 200 });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'create customQuestions failed' }, { status: 500 });
  }
}
