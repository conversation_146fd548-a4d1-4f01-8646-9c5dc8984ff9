import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

const CLOUDFRONT_URL = process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL;
const BUCKET = process.env.NEXT_PUBLIC_S3FOLDER;
export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const organizationId = searchParams.get('organizationId');
  try {
    const question = await db.customQuestions.findMany({
      where: {
        organizationId,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
    const result = question?.map((item) => {
      return {
        ...item,
        videoUrl: `${CLOUDFRONT_URL}/${BUCKET}/question/${item?.videoUrl}`,
      };
    });

    return NextResponse.json(
      {
        result,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('An error occurred:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
