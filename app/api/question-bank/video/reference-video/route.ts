import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

const CLOUDFRONT_URL = process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL;
const BUCKET = process.env.NEXT_PUBLIC_S3FOLDER;
export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const organizationId = searchParams.get('organizationId');
  try {
    const question = await db.customQuestions.findMany({
      where: {
        organizationId,
      },
    });
    let referenceVideoIds = Array.from(new Set(question?.map((item) => item?.referenceVideoUrl)));

    referenceVideoIds = referenceVideoIds?.map((item) => {
      const format = item?.toString()?.split('.')?.pop() === 'mp4' ? 'video' : 'audio';
      return { url: `${CLOUDFRONT_URL}/${BUCKET}/reference-video/${item}`, format };
    });

    return NextResponse.json(
      {
        result: referenceVideoIds,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('An error occurred:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
