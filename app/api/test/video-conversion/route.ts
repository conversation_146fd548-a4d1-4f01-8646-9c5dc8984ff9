import { NextResponse } from 'next/server';

import { runAllTests, testVideoProcessingConfig, testFFmpegAvailability, testFallbackFeedback } from '@/utils/test-video-conversion';

export const maxDuration = 60; // 1 minute timeout for tests

/**
 * Test endpoint for video conversion functionality
 * GET /api/test/video-conversion
 * 
 * Query parameters:
 * - practiceId: Optional practice ID to test with
 * - test: Specific test to run (config|ffmpeg|fallback|all)
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const practiceId = searchParams.get('practiceId');
    const testType = searchParams.get('test') || 'all';

    console.log(`Running video conversion test: ${testType}`);

    let results: any = {};

    switch (testType) {
      case 'config':
        results = await testVideoProcessingConfig();
        break;
        
      case 'ffmpeg':
        results = await testFFmpegAvailability();
        break;
        
      case 'fallback':
        results = testFallbackFeedback();
        break;
        
      case 'all':
      default:
        // Run all tests
        const configTest = await testVideoProcessingConfig();
        const ffmpegTest = await testFFmpegAvailability();
        const fallbackTest = testFallbackFeedback();
        
        results = {
          configuration: configTest,
          ffmpeg: ffmpegTest,
          fallback: fallbackTest,
          overall: {
            success: configTest.success && ffmpegTest.success && fallbackTest.success,
            readyForProduction: configTest.success && ffmpegTest.available && fallbackTest.success,
          }
        };
        
        // If practice ID provided, test workflow
        if (practiceId) {
          const { testVideoConversionWorkflow } = await import('@/utils/test-video-conversion');
          const workflowTest = await testVideoConversionWorkflow(practiceId);
          results.workflow = workflowTest;
          results.overall.workflowReady = workflowTest.success;
        }
        break;
    }

    return NextResponse.json({
      success: true,
      testType,
      practiceId,
      timestamp: new Date().toISOString(),
      results,
    });

  } catch (error) {
    console.error('Video conversion test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

/**
 * POST endpoint for testing actual video conversion
 * This is more comprehensive but requires valid practice ID and credentials
 */
export async function POST(request: Request) {
  try {
    const { practiceId, userId, organizationId } = await request.json();

    if (!practiceId) {
      return NextResponse.json({
        success: false,
        error: 'practiceId is required for conversion test',
      }, { status: 400 });
    }

    console.log(`Testing actual video conversion for practice: ${practiceId}`);

    // Import conversion function
    const { convertTailoredPracticeVideo } = await import('@/utils/video-conversion');

    // Test actual conversion (this will fail if video doesn't exist)
    const conversionResult = await convertTailoredPracticeVideo(practiceId, {
      userId: userId || 'test-user',
      organizationId,
      onProgress: (progress) => {
        console.log(`Conversion progress: ${progress}%`);
      },
    });

    return NextResponse.json({
      success: true,
      practiceId,
      conversionResult,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Video conversion test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
