import { NextResponse } from 'next/server';

import { OpenAIStreamPayload } from '@/lib/OpenAIStream';
import retryOpenAI from '@/lib/retryOpenApi';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';
import { FrappeClient } from '@/services/FrappeClient';

export const maxDuration = 299;

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing env var from OpenAI');
}

const generateFeedback = async (messages) => {
  const payload: OpenAIStreamPayload = {
    model: process.env.AZURE_OPEN_AI_MODEL || '',
    messages,
    temperature: 0.7,
    top_p: 0.8,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: false,
    n: 1,
    response_format: { type: 'json_object' },
  };
  const result = await retryOpenAI(payload, 3, true);
  return result;
};

export async function POST(request: any) {
  const { id } = await request.json();

  const careerPractice: any = await db.careerPractice.findUnique({
    where: {
      id: id,
    },
    include: {
      eventDetails: {
        include: {
          organization: {
            select: {
              hrmsAPIKey: true,
              hrmsAPISecret: true,
              hrmsUrl: true,
              coreValues: true,
            },
          },
        },
      },
    },
  });

  if (!(careerPractice?.conversation as any)?.length) {
    return NextResponse.json(
      {
        error: 'Error',
      },
      { status: 400 },
    );
  }

  const questions = (careerPractice?.conversation as any)?.map((item, index) => {
    if (item?.round === 'multiple-choice') return null;
    if (item?.round === 'written-interview') return null;
    if (item?.round === 'frontend-interview')
      return `frontend interview question ${index + 1}: ${item?.questionMarkdown},
    feedback summary: ${item?.feedback?.short_summary},
    score: ${item?.feedback?.overall_score},
     `;
    if (item?.round === 'coding-interview')
      return `coding interview question ${index + 1}: ${item?.question},
    feedback summary: ${item?.feedback?.short_summary},
    score: ${item?.feedback?.overall_score},
     `;
    return `question ${index + 1}: ${item?.answer},
    feedback summary: ${item?.feedback?.short_summary},
    score: ${item?.feedback?.overall_score},
    Proctoring Analysis:{
     ${
       item?.feedback?.proctoring?.camera_angle
         ? `camera angle:${item?.feedback?.proctoring?.camera_angle},`
         : ''
     } ${
       item?.feedback?.proctoring?.['eye_contact_&_posture']
         ? `eye contact and posture:${item?.feedback?.proctoring?.['eye_contact_&_posture']},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['suspicious_activity']
         ? `suspicious activity:${item?.feedback?.proctoring?.suspicious_activity},`
         : ''
     }${
       item?.feedback?.proctoring?.['words_per_minute']
         ? `words per minute:${item?.feedback?.proctoring?.words_per_minute},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['emotional_state']
         ? `emotional state:${item?.feedback?.proctoring?.emotional_state},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['candidate_movement']
         ? `candidate movement:${item?.feedback?.proctoring?.suspicious_activity},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['background']
         ? `background:${item?.feedback?.proctoring?.suspicious_activity},`
         : ''
     }
     }`;
  });
  const question = questions?.filter((element) => element !== null);
  let content,
    coreValues,
    mcqScore: any = {};
  const groupedData = (careerPractice?.conversation as any)?.reduce((acc, item) => {
    // Ensure there is an array for the round
    if (item.round === 'multiple-choice' && !acc[item.category]) {
      acc[item.category] = [];
    }
    if (item.round === 'multiple-choice') {
      acc[item.category].push(item);
    }

    return acc;
  }, {});
  if (Object.keys(groupedData ?? {})?.length !== 0) {
    Object.entries(groupedData ?? {})?.map(([key, value]: any, index) => {
      const { efficiency } = calculateAptitudeSkillAndEfficiency(value);
      mcqScore[key] = Math.round(Number(efficiency));
    });
    question.push(
      `MCQ round scores: ${Object.entries(mcqScore ?? {})?.map(
        ([key, value]: any, index) => `{topic: ${key}: score:${value}}`,
      )}`,
    );
  }
  if (question?.length === 0) {
    return;
  }
  content = await generatePrompt('CONSOLIDATED_FEEDBACK_PROMPT_STRATEGY', {
    role: careerPractice?.role,
    level: careerPractice?.level,
    questions: question?.join('\n'),
    tabSwitch: careerPractice?.proctorWarnings?.tabSwitch ?? '0',
    fullScreen: careerPractice?.proctorWarnings?.fullScreen ?? '0',
    evaluation: careerPractice?.eventDetails?.evaluation,
  });
  if (careerPractice?.eventDetails?.organization?.coreValues) {
    coreValues = await generatePrompt('CORE_VALUE_FEEDBACK_PROMPT_STRATEGY', {
      coreValues: careerPractice?.eventDetails?.organization?.coreValues,
      usersResponse: question?.join('\n'),
    });
  }

  const [feedback, coreValue] = await Promise.all([
    generateFeedback([
      {
        role: 'system',
        content,
      },
    ]),
    coreValues
      ? generateFeedback([
          {
            role: 'system',
            content: coreValues,
          },
        ])
      : Promise.resolve(null), // Default to null if coreValues is undefined
  ]);

  const response = await db.careerPractice.update({
    data: {
      feedback: { ...(careerPractice?.feedback as any), ...feedback, coreValue },
    },
    include: {
      user: { select: { email: true } },
    },
    where: {
      id: id,
    },
  });

  try {
    if (
      careerPractice?.eventDetails?.organization?.hrmsUrl &&
      careerPractice?.eventDetails?.organization?.hrmsAPIKey &&
      careerPractice?.eventDetails?.organization?.hrmsAPISecret
    ) {
      const headers = {
        'Content-Type': 'application/json',
        Authorization: `token ${careerPractice?.eventDetails?.organization?.hrmsAPIKey}:${careerPractice?.eventDetails?.organization?.hrmsAPISecret}`,
      };
      const payload = {
        doc: JSON.stringify({
          id: careerPractice?.id,
          references_id: careerPractice?.references_id, //result?.references_id,
        }),
        action: 'Save',
      };
      FrappeClient.postJSONApi(
        careerPractice?.eventDetails?.organization.hrmsUrl,
        'flinkk_hrms.overrides.interview.update_interview_status_result',
        payload,
        headers,
      ).catch((frappeError) => {
        console.error('Error occurred in FrappeClient:', frappeError);
      });
    }
  } catch (e) {
    console.error('Error sending feedback to hrms', e);
  }

  return NextResponse.json(
    {
      response,
    },
    { status: 200 },
  );
}

const calculateAptitudeSkillAndEfficiency = (questions) => {
  const totalQuestions = questions?.length;
  const correctAnswers = questions?.filter((item) => item.answer === item.correctAnswer).length;
  const efficiency = ((correctAnswers / totalQuestions) * 100).toFixed(2);
  return { efficiency };
};
