import { NextResponse } from 'next/server';

import { OpenAIStreamPayload } from '@/lib/OpenAIStream';
import retryOpenAI from '@/lib/retryOpenApi';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';
import { getToken } from 'next-auth/jwt';
import { v4 as uuid } from 'uuid';

export const maxDuration = 299;

interface ChatGPTMessage {
  role: 'system' | 'user';
  content: string;
}

interface ConversationItem {
  question: string;
  isAnswered: boolean;
  startTime: any;
  answer: string;
}

const conversationPrompt = (question: string, answer: string, role: string, level: String) => {
  return `Question: ${question}\nAnswer: ${answer}\nInterview me a follow-up question for ${role} role and ${level}. Limit it to 15 words.`;
};

const getMaximumQuestion = (data) => {
  switch (true) {
    case data?.eventDetails?.isAiQuestion && Number(data?.eventDetails?.aiQuestionCount) > 0:
      return data.eventDetails.aiQuestionCount;
    case data?.eventDetails?.isAiQuestion && !data?.eventDetails?.aiQuestionCount:
      return 5;
    default:
      return (data?.conversation as any)?.length;
  }
};

const updateConversation = async (
  data: any,
  transcript: string,
  questionId: string,
  s3Id: string,
  MAX_QUESTIONS: number,
) => {
  if (!data) {
    return null;
  }

  const conversation: any = data.conversation || [];

  if (conversation.filter((m) => m.isAnswered).length > MAX_QUESTIONS) {
    return conversation;
  }

  const updatedConversation = conversation.map((item) => {
    if (item?.id === questionId) {
      return {
        ...item,
        answer: transcript,
        s3Id: s3Id,
        isAnswered: true,
        completedTime: new Date(),
      };
    }
    return item;
  });
  return updatedConversation;
};

const generateMessages = async (role: string, level: string, conversation: ConversationItem[]) => {
  const systemPrompt = await generatePrompt(
    'INTERVIEWATHON_VIDEO_QUESTION_SYSTEM_PROMPT_STRATEGY',
    {
      role,
      level,
    },
  );
  const messages: ChatGPTMessage[] = [
    {
      role: 'system',
      content: systemPrompt,
    },
  ];

  const userPrompts: ChatGPTMessage[] = await Promise.all(
    conversation.map(async (item) => {
      const userPrompt = await generatePrompt(
        'INTERVIEWATHON_VIDEO_QUESTION_USER_PROMPT_STRATEGY',
        {
          role,
          level,
          question: item.question,
          answer: item.answer,
        },
      );

      return {
        role: 'user',
        content: userPrompt,
      };
    }),
  );
  messages.push(...userPrompts);
  return messages;
};

async function getUnansweredQuestions(careerPractice) {
  const unansweredQuestions = careerPractice?.conversation.filter((item) => !item.isAnswered);

  return unansweredQuestions;
}

export async function POST(request: any) {
  const token = await getToken({ req: request });

  const { transcript, question, id, s3Id, sub, questionId } = (await request.json()) as {
    transcript?: string;
    question?: string;
    id?: string;
    s3Id?: string;
    sub?: string;
    questionId?: string;
  };
  if (!sub && !token && !id)
    return NextResponse.json(
      {
        message: 'Unauthorized',
      },
      { status: 401 },
    );

  if (!id) {
    return NextResponse.json(
      {
        message: 'Missing testId',
      },
      { status: 400 },
    );
  }

  if (id && transcript && question && questionId) {
    const data = await db.careerPractice.findFirst({
      where: {
        id: id,
        userId: sub || token?.sub,
      },
      include: {
        eventDetails: {
          select: {
            isAiQuestion: true,
            aiQuestionCount: true,
            isPlacement: true,
          },
        },
      },
    });

    const MAX_QUESTIONS = getMaximumQuestion(data);

    const updatedConversation = await updateConversation(
      data,
      transcript,
      questionId,
      s3Id || '',
      MAX_QUESTIONS,
    );

    if (!updatedConversation) {
      return NextResponse.json(
        {
          message: 'Invalid testId',
        },
        { status: 400 },
      );
    }

    if (updatedConversation.length > MAX_QUESTIONS) {
      return NextResponse.json(
        {
          message: 'Maximum question limit reached',
        },
        { status: 400 },
      );
    }

    if (updatedConversation?.length === MAX_QUESTIONS) {
      const response = await db.careerPractice.update({
        data: {
          conversation: [...updatedConversation],
        },
        where: {
          id: id,
        },
      });

      if (!response) {
        return NextResponse.json(
          {
            message: 'Invalid id',
          },
          { status: 400 },
        );
      }

      const unansweredQuestions = await getUnansweredQuestions(response);

      if (unansweredQuestions?.length === 0) {
        return NextResponse.json(
          {
            message: 'Your interview session has been completed successfully.',
            isPlacement: data?.eventDetails?.isPlacement,
            event: response?.event,
            role: response?.role,
            status: 'success',
          },
          { status: 200 },
        );
      }
      const currentRoundTotal = (response as any)?.conversation?.filter(
        (item) => item?.round === unansweredQuestions[0]?.round,
      )?.length;
      const questionStatus = `${
        (response as any)?.conversation?.length - Number(unansweredQuestions?.length) + 1
      }/${currentRoundTotal}`;
      return NextResponse.json(
        {
          question: unansweredQuestions[0].question,
          questionId: unansweredQuestions[0].id,
          round: unansweredQuestions[0].round,
          event: response?.event,
          questionStatus: questionStatus,
          aiQuestionCount: data?.eventDetails?.aiQuestionCount,
        },
        { status: 200 },
      );
    }

    const messages = await generateMessages(
      data?.role || '',
      data?.level || '',
      updatedConversation,
    );

    if (!data?.eventDetails?.isAiQuestion) {
      const finalData = await db.careerPractice.update({
        data: {
          conversation: [...updatedConversation],
        },
        where: {
          id: id,
        },
      });

      if (!finalData) {
        return NextResponse.json(
          {
            message: 'Invalid id',
          },
          { status: 400 },
        );
      }

      const unansweredQuestions = await getUnansweredQuestions(finalData);

      if (unansweredQuestions?.length === 0) {
        return NextResponse.json(
          {
            message: 'Your interview session has been completed successfully.',
            isPlacement: data?.eventDetails?.isPlacement,
            event: finalData?.event,
            role: finalData?.role,
            status: 'success',
          },
          { status: 200 },
        );
      }
      const currentRoundTotal = (finalData as any)?.conversation?.filter(
        (item) => item?.round === unansweredQuestions[0]?.round,
      )?.length;

      const questionStatus = `${
        (finalData as any)?.conversation?.length - Number(unansweredQuestions?.length) + 1
      }/${currentRoundTotal}`;
      return NextResponse.json(
        {
          question: unansweredQuestions[0].question,
          questionId: unansweredQuestions[0].id,
          round: unansweredQuestions?.[0]?.round,
          role: finalData?.role,
          event: finalData?.event,
          questionStatus: questionStatus,
          aiQuestionCount: data?.eventDetails?.aiQuestionCount,
        },
        { status: 200 },
      );
    } else {
      const payload: OpenAIStreamPayload = {
        model: process.env.AZURE_OPEN_AI_MODEL || '',
        messages: messages,
        temperature: 0.7,
        top_p: 0.8,
        frequency_penalty: 0,
        presence_penalty: 0,
        stream: false,
        n: 1,
      };
      const result = await retryOpenAI(payload, 3, false);

      const finalData = await db.careerPractice.update({
        data: {
          conversation: [
            ...updatedConversation,
            {
              id: uuid(),
              question: result.replace(/^Question:\s+/, ''),
              round: 'video-interview',
              startTime: new Date(),
              isAnswered: false,
              s3Id: '',
              answer: '',
            },
          ],
        },
        where: {
          id: id,
        },
      });

      if (!finalData) {
        return NextResponse.json(
          {
            message: 'Invalid id',
          },
          { status: 400 },
        );
      }

      const unansweredQuestions = await getUnansweredQuestions(finalData);

      if (unansweredQuestions?.length === 0) {
        return NextResponse.json(
          {
            message: 'Your interview session has been completed successfully.',
            isPlacement: data?.eventDetails?.isPlacement,
            event: finalData?.event,
            role: finalData?.role,
            status: 'success',
          },
          { status: 200 },
        );
      }
      const currentRoundTotal = (finalData as any)?.conversation?.filter(
        (item) => item?.round === unansweredQuestions[0]?.round,
      )?.length;
      const questionStatus = `${
        (finalData as any)?.conversation?.length - Number(unansweredQuestions?.length) + 1
      }/${currentRoundTotal}`;
      return NextResponse.json(
        {
          question: unansweredQuestions[0].question,
          questionId: unansweredQuestions[0].id,
          role: finalData?.role,
          round: unansweredQuestions?.[0]?.round,
          event: finalData?.event,
          questionStatus: questionStatus,
          isPlacement: data?.eventDetails?.isPlacement,
          aiQuestionCount: data?.eventDetails?.aiQuestionCount,
        },
        { status: 200 },
      );
    }
  }

  if (id && transcript && !question) {
    return NextResponse.json(
      {
        message: 'Missing question',
      },
      { status: 400 },
    );
  }

  if (id && !transcript && question) {
    return NextResponse.json(
      {
        message: 'Transcript cannot be empty',
      },
      { status: 400 },
    );
  }

  if (id && !transcript && !question) {
    const careerPractice = await db.careerPractice.findFirst({
      where: {
        id: id,
        userId: sub || token?.sub,
      },
      include: {
        eventDetails: true,
      },
    });
    if (!careerPractice) {
      return NextResponse.json(
        {
          message: 'Invalid id',
        },
        { status: 400 },
      );
    }

    const unansweredQuestions = await getUnansweredQuestions(careerPractice);

    if (unansweredQuestions?.length === 0) {
      return NextResponse.json(
        {
          message: 'Your interview session has been completed successfully.',
          isPlacement: careerPractice?.eventDetails?.isPlacement,
          event: careerPractice?.event,
          role: careerPractice?.role,
          status: 'success',
        },
        { status: 200 },
      );
    }
    const currentRoundTotal = (careerPractice as any)?.conversation?.filter(
      (item) => item?.round === unansweredQuestions[0]?.round,
    )?.length;

    const questionStatus = `${
      (careerPractice as any)?.conversation?.length - Number(unansweredQuestions?.length) + 1
    }/${currentRoundTotal}`;
    return NextResponse.json(
      {
        round: unansweredQuestions?.[0]?.round,
        question: unansweredQuestions?.[0]?.question,
        questionId: unansweredQuestions?.[0]?.id,
        event: careerPractice?.event,
        role: careerPractice?.role,
        questionStatus: questionStatus,
        aiQuestionCount: careerPractice?.eventDetails?.aiQuestionCount,
      },
      { status: 200 },
    );
  }
}
