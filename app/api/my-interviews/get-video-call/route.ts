import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  const { userId } = await request.json();

  try {
    const videoCall = await db.videoCallInterview.findMany({
      where: {
        userId: userId,
        meetingStatus: 'PENDING',
        scheduleTime: {
          gte: new Date(new Date().setMinutes(new Date().getMinutes() - 90)),
        },
      },
      include: {
        careerPractice: {
          include: {
            eventDetails: {
              include: {
                organization: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });
    return NextResponse.json({ videoCall }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error }, { status: 200 });
  }
}
