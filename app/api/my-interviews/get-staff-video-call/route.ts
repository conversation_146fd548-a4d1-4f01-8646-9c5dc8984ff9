import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  const { userId, organizationId, isSuperUser, searchParams } = await request.json();
  const { page = 1, per_page = 50, meetingStatus = 'PENDING' } = searchParams;
  let where: any = {};
  if (!isSuperUser) {
    where = {
      interviewerId: {
        has: userId, // Changed from direct assignment to array contains check
      },
    };
  }
  if (meetingStatus?.toLowerCase() === 'pending') {
    where.meetingStatus = 'PENDING';
  }
  if (meetingStatus?.toLowerCase() === 'completed') {
    where.meetingStatus = 'COMPLETED';
  }
  try {
    let [videoCall, count]: any = await Promise.all([
      db.videoCallInterview.findMany({
        where: {
          ...where,
          meetingType: 'videoCall',
          organizationId: organizationId,
        },
        skip: Math.max(Number(page ?? 1) - 1, 0) * Number(per_page ?? 50),
        take: Number(per_page ?? 50),
        include: {
          careerPractice: {
            include: {
              eventDetails: {
                include: {
                  organization: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
          user: {
            include: {
              userProfile: {
                select: { fullName: true },
              },
            },
          },
        },
      }),
      db.videoCallInterview.count({
        where: {
          ...where,
          meetingType: 'videoCall',
          organizationId: organizationId,
        },
      }),
    ]);

    // Flatten the array of interviewer IDs
    const interviewersId: any = videoCall?.flatMap((item) => item?.interviewerId ?? []);
    const interviewers = await db.user.findMany({
      where: {
        id: { in: interviewersId },
      },
      include: {
        userProfile: {
          select: { fullName: true },
        },
      },
    });

    const finalData = videoCall?.flatMap((meeting) => {
      // Handle multiple interviewers for each meeting
      const meetingInterviewers = interviewers?.filter(
        (interviewer) => meeting?.interviewerId?.includes(interviewer?.id),
      );

      const interviewerNames = meetingInterviewers?.map((interviewer) =>
        interviewer?.userProfile?.fullName === '' || !interviewer?.userProfile?.fullName
          ? interviewer?.email
          : interviewer?.userProfile?.fullName,
      );

      return {
        ...meeting,
        interviewer: meetingInterviewers?.map((interviewer) => interviewer?.email).join(', '),
        interviewerName: interviewerNames?.join(', '),
      };
    });

    return NextResponse.json(
      { videoCall: finalData, totalPages: Math.ceil(count / Number(per_page)) },
      { status: 200 },
    );
  } catch (error) {
    return NextResponse.json({ error }, { status: 200 });
  }
}
