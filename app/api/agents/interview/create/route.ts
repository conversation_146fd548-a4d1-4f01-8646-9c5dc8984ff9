import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export const maxDuration = 299;

export async function POST(request: any) {
  const input = await request.json();
  console.log({ input: JSON.stringify(input) });
  const { role, level, organizationId, userId, name, questions, jobDescription, employment_type } =
    input;
  const memberResponse = await db.membership.findFirst({
    where: {
      organizationId: organizationId,
      userId: userId,
      role: { in: ['OWNER', 'MEMBER', 'ADMIN'] },
    },
  });
  console.log({ memberResponse });

  const response = await db.eventDetails.create({
    data: {
      name,
      organizationId: organizationId,
      role: role,
      level: level,
      questions: questions?.map((item) => ({ ...item, time: 2, round: 'video-interview' })),
      jobDescription,
      isProctor: true,
      isAiQuestion: false,
      employment_type: employment_type,
      isPlacement: false,
      timing: { duration: 45, linkValidity: 24 },
      createdById: userId,
      MembershipOnEventDetails: {
        create: [
          {
            membership: {
              connect: {
                id: memberResponse?.id,
              },
            },
          },
        ],
      },
    },
  });

  console.log({ response });
  if (!response?.id) {
    return NextResponse.json(
      {
        message: 'Failed to create an Intevriew',
      },
      { status: 400 },
    );
  }

  if (response && response?.id) {
    return NextResponse.json(
      {
        id: response?.id,
        result: 'Event Created Successfully',
      },
      { status: 200 },
    );
  }
}
