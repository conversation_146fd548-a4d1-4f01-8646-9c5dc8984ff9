import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { problem } = await request.json();

  try {
    const frontEndProblem = await db.frontEndProblems.create({
      data: { ...problem },
    });
    return NextResponse.json(
      {
        frontEndProblem,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Unable to create', status: 500 });
  }
}
