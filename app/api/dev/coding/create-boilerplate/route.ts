import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { boilerplate, id } = await request.json();

  try {
    const problem = await db.codingProblems.findUnique({ where: { id } });
    if (problem?.id) {
      const template: any = problem?.template;

      const codingProblem = await db.codingProblems.update({
        where: { id },
        data: { template: [...template, boilerplate] },
      });
      return NextResponse.json(
        {
          codingProblem,
        },
        { status: 200 },
      );
    }
    return NextResponse.json(
      {
        error: 'unable to find record',
      },
      { status: 500 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Unable to create' }, { status: 500 });
  }
}
