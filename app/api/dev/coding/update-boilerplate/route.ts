import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { boilerplate, id, languageId } = await request.json();

  try {
    const problem = await db.codingProblems.findUnique({ where: { id } });

    const template: any = problem?.template;
    const filterValue = template?.filter((item) => item?.languageId !== languageId);

    if (problem?.id) {
      let codingProblem = await db.codingProblems.update({
        where: { id },
        data: { template: [...filterValue, boilerplate] },
      });

      return NextResponse.json(
        {
          codingProblem,
        },
        { status: 200 },
      );
    }
    return NextResponse.json(
      {
        error: 'unable to find record',
      },
      { status: 500 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Unable to create', status: 500 });
  }
}
