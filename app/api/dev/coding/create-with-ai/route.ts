import { NextResponse } from 'next/server';

import { OpenAIStreamPayload } from '@/lib/OpenAIStream';
import retryOpenAI from '@/lib/retryOpenApi';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';
import base64 from 'base-64';

export async function POST(request: any) {
  const { problem } = await request.json();
  const prompt = await generatePrompt('CREATE_CODING_USER_PROMPT_STRATEGY', {
    questionName: problem?.name,
    slug: problem?.slug,
    description: problem?.description ?? '',
  });
  const systemPrompt = await generatePrompt('CREATE_CODING_SYSTEM_PROMPT_STRATEGY', {});
  try {
    const payload: OpenAIStreamPayload = {
      model: process.env.AZURE_OPEN_AI_MODEL || '',
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature: 0.7,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      n: 1,
      response_format: { type: 'json_object' },
    };

    const result = await retryOpenAI(payload, 3, true);
    const template = result?.template?.map((item) => {
      return { ...item, code: base64.encode(item?.code) };
    });

    let codingProblem = await db.codingProblems.create({
      data: {
        title: problem?.name,
        description: result?.description,
        time: result?.time,
        slug: problem?.slug,
        difficulty: problem?.difficulty,
        status: 'PUBLISHED',
        categories: problem?.categories,
        tags: problem?.tags,
        template,
      },
    });
    return NextResponse.json(
      {
        codingProblem,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Unable to create', status: 500 });
  }
}
