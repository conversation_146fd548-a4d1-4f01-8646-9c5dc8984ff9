import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { problem } = await request.json();

  try {
    let codingProblem = await db.codingProblems.create({
      data: { ...problem },
    });
    return NextResponse.json(
      {
        codingProblem,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Unable to create', status: 500 });
  }
}
