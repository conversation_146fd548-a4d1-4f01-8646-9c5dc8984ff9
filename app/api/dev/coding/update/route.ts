import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { data, id, slug } = await request.json();
  let where: any = {};
  if (id) {
    where.id = id;
  }
  if (slug) {
    where.slug = slug;
  }
  try {
    let codingProblem = await db.codingProblems.update({
      where,
      data: { ...data },
    });
    return NextResponse.json(
      {
        codingProblem,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Unable to create', status: 500 });
  }
}
