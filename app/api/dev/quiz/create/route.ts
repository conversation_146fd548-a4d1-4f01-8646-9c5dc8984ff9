import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { problem } = await request.json();

  try {
    const mcqProblem = await db.mcqProblem.create({
      data: { ...problem },
    });
    return NextResponse.json(
      {
        mcqProblem,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Unable to create', status: 500 });
  }
}
