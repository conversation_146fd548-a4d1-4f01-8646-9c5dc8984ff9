import { NextResponse } from 'next/server';

import { getToken } from 'next-auth/jwt';

export const maxDuration = 299;
const url = process.env.CHAT_URL || '';
export async function POST(request: any) {
  const token = await getToken({ req: request });
  const { message, tenantId } = await request.json();
  if (!token?.email) {
    return NextResponse.json({
      message: 'Not Authorized to make this call',
      status: 400,
    });
  }
  try {
    const messageResponse = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message, tenantId }),
    });
    const data = await messageResponse.json();

    if (data?.name === 'error') {
      console.log('error', { data });
      return NextResponse.json(
        {
          error: 'error getting results',
        },
        { status: 500 },
      );
    }
    return NextResponse.json(
      {
        message: data?.response,
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error,
      },
      { status: 500 },
    );
  }
}
