import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(req: NextRequest) {
  const url = req.url;
  const queryParams = new URLSearchParams(url.split('?')[1]); // Split the URL and get the query parameters
  const id = queryParams.get('id') ?? '';
  try {
    const groupMembershipMapping = await db.groupMembershipMapping.delete({
      where: {
        id,
      },
    });
    return NextResponse.json({ groupMembershipMapping }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error }, { status: 200 });
  }
}
