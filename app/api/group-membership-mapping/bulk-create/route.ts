import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { data } = await request.json();
  const { members, groupId } = data;

  try {
    const memberIds = members?.map((item) => item?.membershipId);
    const oldGroupMembershipMapping = await db.groupMembershipMapping.findMany({
      where: {
        groupId,
        membershipId: {
          in: memberIds,
        },
      },
    });
    const newMembers = members?.filter(
      (item) =>
        !oldGroupMembershipMapping?.some((oldItem) => oldItem?.membershipId === item?.membershipId),
    );
    const departmentMembershipMapping = await db.departmentMembershipMapping.findMany({
      where: {
        membershipId: {
          in: memberIds,
        },
      },
    });
    const membersWithoutDepartmentMapping = members?.filter(
      (item) =>
        !departmentMembershipMapping?.some(
          (oldItem) => oldItem?.membershipId === item?.membershipId,
        ),
    );
    if (membersWithoutDepartmentMapping?.length > 0) {
      const createDepartmentMembershipMapping = await db.departmentMembershipMapping.createMany({
        data: membersWithoutDepartmentMapping?.map((item) => ({
          membershipId: item?.membershipId,
          role: 'MEMBER',
          departmentId: item?.departmentId,
        })),
      });
    }

    const groupMembershipMapping = await db.groupMembershipMapping.createMany({
      data: newMembers?.map((item) => ({
        membershipId: item?.membershipId,
        role: item?.role,
        groupId,
      })),
    });
    return NextResponse.json(
      {
        items: groupMembershipMapping,
        status: 'success',
      },
      { status: 200 },
    );
  } catch (error) {
    console.log('Error Creating group membership mapping', error);
    return NextResponse.json(
      {
        error: 'Error Creating group membership mapping',
        status: 'error',
      },
      { status: 500 },
    );
  }
}
