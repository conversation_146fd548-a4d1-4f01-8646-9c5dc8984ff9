import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  const { groupId, role = [], organizationId } = await request.json();
  let where = {};
  if (role?.length > 0) {
    where = {
      role: {
        in: role,
      },
    };
  }
  if (organizationId) {
    where['department'] = {
      organizationId,
    };
  }
  if (groupId) {
    where['groupId'] = groupId;
  }
  try {
    const [groupMembershipMapping] = await Promise.all([
      db.groupMembershipMapping.findMany({
        where: {
          ...where,
        },
        include: {
          membership: {
            include: {
              user: {
                include: {
                  userProfile: true,
                },
              },
            },
          },
          group: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);
    return NextResponse.json({ groupMembershipMapping }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error }, { status: 200 });
  }
}
