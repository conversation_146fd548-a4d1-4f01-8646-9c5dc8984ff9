import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const data = await request.json();

  try {
    let createDepartmentMembershipMapping;
    const departmentMembershipMapping = await db.departmentMembershipMapping.findFirst({
      where: {
        departmentId: data?.departmentId,
        membershipId: data?.memberId,
      },
    });

    if (departmentMembershipMapping?.membershipId !== data?.memberId) {
      createDepartmentMembershipMapping = await db.departmentMembershipMapping.create({
        data: {
          departmentId: data?.departmentId,
          membershipId: data?.memberId,
          role: 'MEMBER',
        },
      });
    }
    const groupMembershipMapping = await db.groupMembershipMapping.create({
      data: {
        groupId: data?.groupId,
        membershipId: data?.memberId,
        role: data?.role,
      },
    });

    return NextResponse.json(
      {
        groupMembershipMapping,
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'webhook create failed' }, { status: 500 });
  }
}
