import { NextResponse } from 'next/server';

import { OpenAIStreamPayload } from '@/lib/OpenAIStream';
import retryOpenAI from '@/lib/retryOpenApi';
import { generatePrompt } from '@/prompt-service';

export const maxDuration = 299;

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing env var from OpenAI');
}

interface ChatGPTMessage {
  role: 'system' | 'user';
  content: string;
}

const generateFeedback = async (messages) => {
  const payload: OpenAIStreamPayload = {
    model: process.env.AZURE_OPEN_AI_MODEL || '',
    messages,
    temperature: 0.7,
    top_p: 0.8,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: false,
    n: 1,
    response_format: { type: 'json_object' },
  };
  const result = await retryOpenAI(payload, 3, true);
  return result;
};

export async function POST(request: any) {
  const { comment, status } = await request.json();

  const feedback = await generateFeedback([
    {
      role: 'system',
      content: await generatePrompt('INTERVIEW_FEEDBACK_COMMENT_PROMPT_STRATEGY', {
        message: comment,
        status: status,
      }),
    },
  ]);

  return NextResponse.json(
    {
      feedback,
    },
    { status: 200 },
  );
}
