import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { tenantId, searchParams, membershipId, role } = await request.json();
  const { page = 1, per_page = 50, id, name, sort } = searchParams;
  const isPlacement = false;
  const status = id ?? 'ACTIVE';
  let field = 'createdAt',
    order = 'desc';
  if (sort) {
    [field, order] = sort?.split('.');
  }

  let where: any = {
    organizationId: tenantId,
  };
  if (!['admin', 'owner']?.includes(role?.toLowerCase())) {
    where.MembershipOnEventDetails = {
      some: {
        membershipId,
      },
    };
  }

  if (status) {
    where.status = status ?? 'ACTIVE';
  }

  if (typeof isPlacement === 'boolean') {
    where.isPlacement = isPlacement;
  }

  if (name) {
    where.name = {
      contains: name,
      mode: 'insensitive',
    };
  }

  const parsedLimit = parseInt(per_page, 50);
  try {
    let [eventDetails, count]: any = await Promise.all([
      db.eventDetails.findMany({
        select: {
          id: true,
          name: true,
          role: true,
          level: true,
          round: true,
          status: true,
          _count: {
            select: { careerPractices: true },
          },
          careerPractices: {
            select: {
              interviewStatus: true,
              resultStatus: true,
            },
          },
        },
        where,
        skip: Math.max(Number(page ?? 1) - 1, 0) * Number(per_page ?? 50),
        take: Number(per_page ?? 50),
        orderBy: {
          [field]: order,
        },
      }),
      db.eventDetails.count({
        where,
      }),
    ]);
    eventDetails = eventDetails?.map((item) => ({
      ...item,
      interviewStatusCount: item?.careerPractices.filter(
        (practice) =>
          ['COMPLETED', 'PARTIALLY_COMPLETED'].includes(practice?.interviewStatus) &&
          practice?.resultStatus === null,
      ).length,
    }));
    return NextResponse.json(
      {
        items: eventDetails,
        count: count,
        currentPage: Math.max(page, 1),
        limit: parsedLimit,
        totalPages: Math.ceil(count / Number(per_page)),
      },
      { status: 200 },
    );
  } catch (error) {
    console.log('Error fetching interview list', error);
    return NextResponse.json(
      {
        error: 'Error fetching interview list',
      },
      { status: 500 },
    );
  }
}
