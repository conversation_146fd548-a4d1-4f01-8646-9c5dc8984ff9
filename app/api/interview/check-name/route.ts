import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(req: NextRequest) {
  const url = req.url;
  const queryParams = new URLSearchParams(url.split('?')[1]); // Split the URL and get the query parameters
  const name = queryParams.get('name') ?? '';
  const tenantId = queryParams.get('tenantId') ?? '';
  try {
    const eventDetails = await db.eventDetails.findFirst({
      where: {
        name: name?.trim(),
        organizationId: tenantId,
      },
    });
    if (eventDetails?.id) {
      return NextResponse.json({ valid: false }, { status: 200 });
    } else {
      return NextResponse.json({ valid: true }, { status: 200 });
    }
  } catch (error) {
    return NextResponse.json({ error }, { status: 500 });
  }
}
