import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export const maxDuration = 299;

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const tenantId = searchParams.get('tenantId') ?? '';
  const membershipId = searchParams.get('membershipId') ?? '';
  const role = searchParams.get('role') ?? '';

  if (!membershipId || !role) {
    return NextResponse.json(
      { error: 'Missing required access control parameters' },
      { status: 400 }
    );
  }

  // Get the first and last day of the current month
  const now = new Date();
  const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

  // Build access control where clause
  const isSuperUser = ['admin', 'owner'].includes(role?.toLowerCase());
  let eventAccessWhere: any = {
    organizationId: tenantId,
  };

  if (!isSuperUser) {
    eventAccessWhere.MembershipOnEventDetails = {
      some: {
        membershipId,
      },
    };
  }

  const count = await db.careerPractice.count({
    where: {
      eventDetails: eventAccessWhere,
      createdAt: {
        gte: firstDayOfMonth,
        lte: lastDayOfMonth,
      },
    },
  });

  return NextResponse.json(
    {
      count,
    },
    {
      status: 200,
    },
  );
}
