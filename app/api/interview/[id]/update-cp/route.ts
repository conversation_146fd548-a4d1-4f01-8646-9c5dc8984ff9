import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export async function POST(request: any) {
  const { id, data } = await request.json();
  const token = await getToken({ req: request });
  if (!token)
    return NextResponse.json({
      error: 'Unauthorized',
      status: 401,
    });
  try {
    let careerPractice = await db.careerPractice.update({
      where: { id },
      data,
    });
    let comments: any = careerPractice?.interviewerComments ?? [];
    if (comments?.length > 0) {
      let commentByIds = comments?.map((comment) => comment?.commentBy);
      commentByIds = commentByIds?.filter((item) => item);
      const commentByUsers: any = await db.user.findMany({
        where: {
          id: { in: commentByIds },
        },
        include: {
          userProfile: {
            select: { fullName: true },
          },
        },
      });
      comments = comments?.map((comment) => {
        return {
          ...comment,
          commentBy: commentByUsers.find((user) => user?.id === comment?.commentBy),
        };
      });
    }
    return NextResponse.json(
      {
        careerPractice: { ...careerPractice, interviewerComments: comments },
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'update careerPractice Not Found' }, { status: 500 });
  }
}
