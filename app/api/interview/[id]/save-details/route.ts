import { NextResponse } from 'next/server';

import { GCPVertexAI } from '@/lib/ai-models/src/models/gcp-vertexai';
import { VertexAIConfiguration } from '@/lib/ai-models/src/types/vertexai-model';
import Audit from '@/lib/audit';
import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';
import { v4 as uuid } from 'uuid';

export const maxDuration = 299;

export async function POST(request: any) {
  const token = await getToken({ req: request });
  const { url, question, userId, id, transcript, questionId, nextQuestion, isEnd }: any =
    await request.json();
  if (!token && !id)
    return NextResponse.json(
      {
        message: 'Unauthorized',
      },
      { status: 401 },
    );

  const cp = await db.careerPractice.findUnique({
    where: { id },
    include: {
      eventDetails: {
        select: {
          role: true,
          level: true,
          evaluation: true,
          aiQuestionCount: true,
          isPlacement: true,
        },
      },
    },
  });
  let response;
  try {
    const updatedConversation: any = updateConversation(
      cp,
      transcript,
      questionId,
      url || '',
      isEnd,
      {},
      nextQuestion,
    );

    const updatedCP = await db.careerPractice.update({
      data: {
        conversation: [...updatedConversation],
      },
      include: {
        eventDetails: {
          select: {
            isPlacement: true,
            jobDescription: true,
            aiQuestionCount: true,
            evaluation: true,
          },
        },
      },
      where: {
        id: id,
      },
    });

    const unansweredQuestions = getUnansweredQuestions(updatedCP);
    if (unansweredQuestions?.length === 0) {
      return NextResponse.json(
        {
          message: 'Your interview session has been completed successfully.',
          isPlacement: updatedCP?.eventDetails?.isPlacement,
          event: updatedCP?.event,
          role: updatedCP?.role,
          level: updatedCP?.level,
          status: 'success',
        },
        { status: 200 },
      );
    }
    let previous_conversation = updatedCP?.conversation?.map((item) => {
      if (item?.answer.trim()?.length === 0) return null;
      return {
        turns: [
          { parts: [{ text: item?.question }], role: 'model' },
          { parts: [{ text: item?.answer }], role: 'user' },
        ],
        turnComplete: true,
      };
    });
    previous_conversation = previous_conversation.filter((item) => item !== null);
    const currentRoundTotal = (updatedCP as any)?.conversation?.filter(
      (item) => item?.round === unansweredQuestions[0]?.round,
    )?.length;

    const questionStatus = `${
      (updatedCP as any)?.conversation?.length - Number(unansweredQuestions?.length) + 1
    }/${currentRoundTotal}`;
    return NextResponse.json(
      {
        videoUrl: unansweredQuestions?.[0]?.videoUrl,
        evaluation: updatedCP?.eventDetails?.evaluation,
        round: unansweredQuestions?.[0]?.round,
        question: unansweredQuestions?.[0]?.question,
        questionId: unansweredQuestions?.[0]?.id,
        event: updatedCP?.event,
        level: updatedCP?.level,
        role: updatedCP?.role,
        questionStatus: questionStatus,
        previous_conversation,
        aiQuestionCount: updatedCP?.eventDetails?.aiQuestionCount,
        jobDescription: updatedCP?.eventDetails?.jobDescription,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log('Interviewathon feedback error ', { error });
    Audit.logEvent({
      action: 'video_feedback_generation_failed',
      userId: userId,
      details: {
        screen: 'interviewathon',
        userId: userId,
        s3Id: url,
        error,
        result: response?.parts?.[0]?.text,
      },
    });
    return NextResponse.json({ error: 'Interviewathon feedback error ' }, { status: 500 });
  }
}

// const generateVideoResult = async ({ systemInput, url, question }) => {
//   const config: VertexAIConfiguration = {
//     project,
//     model,
//     location,
//     systemInput,
//     credentials: {
//       private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
//       client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
//       client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
//     },
//     generationConfig: {
//       maxOutputTokens: 8192,
//       temperature: 0.5,
//       topP: 0.5,
//     },
//   };

//   const vertexAI = new GCPVertexAI(config);
//   const stream = await vertexAI.generateContent({
//     video: {
//       fileData: {
//         mimeType: 'video/mp4',
//         fileUri: `gs://${bucket}/${folder}/${url}`,
//       },
//     },
//     question,
//   });

//   return stream;
// };

const updateConversation = (
  data: any,
  transcript: string,
  questionId: string,
  s3Id: string,
  isEnd: any,
  feedback,
  nextQuestion,
) => {
  if (!data) {
    return null;
  }

  const conversation: any = data.conversation || [];

  const updatedConversation = conversation.map((item) => {
    if (item?.id === questionId) {
      return {
        ...item,
        answer: transcript,
        s3Id: s3Id,
        isAnswered: true,
        completedTime: new Date(),
        videoOrigin: s3Id ? 'GCP_BUCKET' : '',
        feedback,
      };
    }
    return item;
  });
  if (isEnd) return updatedConversation;

  // if (updatedConversation.filter((m) => m.isAnswered).length > MAX_QUESTIONS) {
  //   return conversation;
  // }
  // if (updatedConversation.filter((m) => m.isAnswered).length === MAX_QUESTIONS) {
  //   return updatedConversation;
  // }

  return [
    ...updatedConversation,
    {
      id: uuid(),
      question: nextQuestion,
      round: 'video-interview',
      startTime: new Date(),
      isAnswered: false,
      s3Id: '',
      answer: '',
    },
  ];
};

function getUnansweredQuestions(careerPractice) {
  const unansweredQuestions = careerPractice?.conversation.filter((item) => !item.isAnswered);

  return unansweredQuestions;
}
