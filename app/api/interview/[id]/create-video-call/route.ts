import { NextResponse } from 'next/server';

import StaffInterviewNotification from '@/emails/staff-video-meeting';
import AcceptCandidate from '@/emails/video-call';
import campedMailer from '@/lib/campedMailer';
import { db } from '@/prisma/db';
import { addParticipant, createMeeting } from '@/services/apicall';
import { convertToIST } from '@/utils/utc-to-ist';
import { render } from '@react-email/render';
import { getToken } from 'next-auth/jwt';

export const maxDuration = 299;

export async function POST(request: any) {
  const token = await getToken({ req: request });
  let meeting, staffs;
  const {
    id,
    meetingType,
    meetingDateTime,
    meetingDuration,
    interviewers,
    address,
    userId,
    platform,
    endTime,
  } = await request.json();
  if (!token && !id)
    return NextResponse.json(
      {
        message: 'Unauthorized',
      },
      { status: 401 },
    );

  if (!id) {
    return NextResponse.json(
      {
        message: 'Missing testId',
      },
      { status: 400 },
    );
  }
  const careerPractice: any = await db.careerPractice.findUnique({
    where: {
      id,
    },
    include: {
      eventDetails: {
        include: {
          organization: true,
        },
      },
      user: {
        include: {
          userProfile: true,
        },
      },
    },
  });
  if (interviewers?.length > 0 || interviewers) {
    staffs = await db.user.findMany({
      where: {
        id: { in: interviewers?.map((interviewer: any) => interviewer?.userId) },
      },
      include: {
        userProfile: {
          select: {
            fullName: true,
          },
        },
      },
    });
  }
  if (!careerPractice) {
    return NextResponse.json({ error: 'Career practice not found' }, { status: 404 });
  }

  const candidateName =
    careerPractice?.user?.userProfile?.fullName === '' ||
    !careerPractice?.user?.userProfile?.fullName
      ? careerPractice?.user?.email
      : careerPractice?.user?.userProfile?.fullName;

  if (meetingType === 'videoCall') {
    const response = await createMeeting(
      `${careerPractice?.eventDetails?.name} - ${careerPractice?.user?.email}`,
    );
    const meetingId = response?.id?.data?.id;
    if (!meetingId) {
      return NextResponse.json({ error: 'Failed to create meeting' }, { status: 500 });
    }
    const userParticipant = await addParticipant(
      meetingId,
      careerPractice?.user?.id,
      'group_call_participant',
    );
    const staffParticipants = await Promise.all(
      staffs?.map(async (item) => {
        const staff = await addParticipant(meetingId, item?.id, 'group_call_host');
        return { ...staff, userId: item?.id };
      }),
    );

    const [userMail, staffEmail] = await Promise.all([
      sendEmail({
        email: careerPractice?.user?.email,
        location: address,
        inPerson: meetingType === 'inPerson',
        interviewTime: meetingDateTime,
        organization: careerPractice?.eventDetails?.organization,
        isStaff: false,
        candidateName: '',
        staffName: '',
        interviewRole: careerPractice?.role,
        candidateLink: '',
        platform,
        endTime: '',
      }),
      ...staffs?.map(async (item) => {
        const staffName =
          item?.userProfile?.fullName === '' || !item?.userProfile?.fullName
            ? item?.email
            : item?.userProfile?.fullName;

        const staff = await sendEmail({
          email: item?.email,
          location: address,
          inPerson: meetingType === 'inPerson',
          interviewTime: meetingDateTime,
          organization: careerPractice?.eventDetails?.organization,
          isStaff: true,
          candidateName,
          staffName,
          interviewRole: careerPractice?.role,
          platform,
          endTime,
          candidateLink: `${process.env.NEXT_PUBLIC_API_BASE_URL}/interviews/${careerPractice?.eventId}/${careerPractice?.id}`,
        });
        return staff;
      }),
    ]);
    if (!userMail) {
      return NextResponse.json({ error: 'Failed to send email' }, { status: 500 });
    }
    meeting = await db.videoCallInterview.create({
      data: {
        careerPracticeId: id,
        eventId: careerPractice?.eventDetails?.id,
        meetingId,
        participants: [
          {
            userId: careerPractice?.user?.id,
            email: careerPractice?.user?.email,
            meetingUserId: userParticipant?.id?.data?.id,
            meetingUserToken: userParticipant?.id?.data?.token,
            userMail,
          },
          ...staffs?.map((item) => {
            const staff = staffParticipants?.find(
              (participant) => participant?.userId === item?.id,
            );
            return {
              userId: item?.id,
              email: item?.email,
              meetingUserId: staff?.id?.data?.id,
              meetingUserToken: staff?.id?.data?.token,
            };
          }),
        ],
        meetingType,
        scheduleTime: new Date(meetingDateTime),
        createdById: userId,
        interviewerId: staffs?.map((item) => item?.id),
        userId: careerPractice?.user?.id,
        address: address,
        interviewDuration: meetingDuration,
        organizationId: careerPractice?.eventDetails?.organizationId,
        meetingDetails: {
          calenderEvent: staffEmail?.calenderEvent,
        },
      },
    });
    if (!meeting) {
      return NextResponse.json({ error: 'Failed to create meeting' }, { status: 500 });
    }
  } else {
    if (!careerPractice?.eventDetails?.organization?.hrmsUrl) {
      const [userMail, staffEmail] = await Promise.all([
        sendEmail({
          email: careerPractice?.user?.email,
          location: address,
          inPerson: meetingType === 'inPerson',
          interviewTime: meetingDateTime,
          organization: careerPractice?.eventDetails?.organization,
          isStaff: false,
          candidateName: '',
          staffName: '',
          interviewRole: careerPractice?.role,
          candidateLink: '',
          platform,
          endTime: '',
        }),
        ...staffs?.map(async (item) => {
          const staffName =
            item?.userProfile?.fullName === '' || !item?.userProfile?.fullName
              ? item?.email
              : item?.userProfile?.fullName;

          const staff = await sendEmail({
            email: item?.email,
            location: address,
            inPerson: meetingType === 'inPerson',
            interviewTime: meetingDateTime,
            organization: careerPractice?.eventDetails?.organization,
            isStaff: true,
            candidateName,
            staffName,
            interviewRole: careerPractice?.role,
            platform,
            endTime,
            candidateLink: `${process.env.NEXT_PUBLIC_API_BASE_URL}/interviews/${careerPractice?.eventId}/${careerPractice?.id}`,
          });
          return staff;
        }),
      ]);
      if (!userMail || !staffEmail) {
        return NextResponse.json({ error: 'Failed to send email' }, { status: 500 });
      }
    }
    meeting = await db.videoCallInterview.create({
      data: {
        careerPracticeId: id,
        eventId: careerPractice?.eventDetails?.id,
        meetingId: null,
        participants: [
          {
            userId: careerPractice?.user?.id,
            email: careerPractice?.user?.email,
          },
          ...staffs?.map((item) => {
            return {
              userId: item?.id,
              email: item?.email,
            };
          }),
        ],
        interviewerId: staffs?.map((item) => item?.id),
        meetingType,
        scheduleTime: new Date(meetingDateTime),
        createdById: userId,
        address: address,
        interviewDuration: meetingDuration,
        userId: careerPractice?.user?.id,
        organizationId: careerPractice?.eventDetails?.organizationId,
      },
    });
    if (!meeting) {
      return NextResponse.json({ error: 'Failed to create meeting' }, { status: 500 });
    }
  }

  return NextResponse.json({ meeting }, { status: 200 });
}

const generateEmailUrl = (baseUrl: string, userEmail: string, token: string) => {
  return `${baseUrl}/api/auth/callback/${encodeURIComponent('email')}?email=${encodeURIComponent(
    userEmail,
  )}&token=${encodeURIComponent(token)}&callbackUrl=${encodeURIComponent(
    `${baseUrl}/my-interviews`,
  )}`;
};

const sendEmail = async ({
  email,
  location,
  inPerson,
  interviewTime,
  organization,
  isStaff,
  candidateName,
  staffName,
  interviewRole,
  candidateLink,
  platform,
  endTime,
}) => {
  let emailUrl;
  if (email) {
    emailUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/sign-in?from=/my-interviews`;
  }
  if (isStaff) {
    const html = render(
      StaffInterviewNotification({
        meetingUrl: emailUrl,
        location,
        inPerson,
        staffName,
        candidateName,
        interviewRole,
        organization: organization?.name,
        emailIdentifier: null,
        interviewTime: convertToIST(interviewTime),
        candidateLink,
      }),
      {
        pretty: true,
      },
    );
    const mailResponse = await campedMailer.send({
      from: process.env.SEND_EMAIL_FROM || '',
      to: email,
      subject: `Interview scheduled with ${candidateName}`,
      html,
    });
    if (mailResponse && platform) {
      try {
        const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/integration/${platform}/calendar/create-event`;
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            date: interviewTime,
            endTime: endTime,
            organizationId: organization?.id,
            user: email,
            eventId: mailResponse?.MessageId,
            subject: `Interview scheduled with ${candidateName}`,
            body: html,
          }),
        });
        const responseBody = await response.json();
        return { ...mailResponse, calenderEvent: { ...responseBody?.calendar } };
      } catch (e) {
        console.log({ 'createVideoQuestion api fail': e });
      }
    }
    return { ...mailResponse };
  }
  const mailResponse = await campedMailer.send({
    from: process.env.SEND_EMAIL_FROM || '',
    to: email,
    subject: `Congratulations! You’re Moving Forward in the Interview Process at ${organization?.name}`,
    html: render(
      AcceptCandidate({
        organization: organization?.name,
        meetingUrl: emailUrl,
        location,
        inPerson,
        emailContent: null,
        emailIdentifier: null,
        interviewTime: convertToIST(interviewTime),
        role: interviewRole,
      }),
      {
        pretty: true,
      },
    ),
  });
  return mailResponse;
};
