import { NextResponse } from 'next/server';

import { GCPVertexAI } from '@/lib/ai-models/src/models/gcp-vertexai';
import { VertexAIConfiguration } from '@/lib/ai-models/src/types/vertexai-model';
import Audit from '@/lib/audit';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';
import { createInterviewAIQuestion, createInterviewathonAIQuestion } from '@/services/apicall';

export const maxDuration = 299;

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing env var from OpenAI');
}

const project = process.env.NEXT_PUBLIC_GCP_PROJECT_ID || '';
const model = process.env.NEXT_PUBLIC_GCP_MODEL || '';
const location = process.env.NEXT_PUBLIC_GCP_LOCATION || '';
const defaultBucket = process.env.NEXT_PUBLIC_GCP_BUCKET || '';
const folder = process.env.NEXT_PUBLIC_S3FOLDER || '';

// Function to get GCP bucket from organization or use default
async function getGCPBucket(organizationId) {
  if (!organizationId) return defaultBucket;

  try {
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { gcpBucket: true },
    });

    return organization?.gcpBucket || defaultBucket;
  } catch (error) {
    console.error('Error fetching organization GCP bucket:', error);
    return defaultBucket;
  }
}

export async function POST(request: any) {
  const {
    url,
    question,
    level,
    role,
    id,
    userId,
    evaluation,
    organizationId,
    isPlacement,
    questionId,
  }: any = await request.json();
  let response;
  try {
    // Get bucket from organization or use default
    const bucket = await getGCPBucket(organizationId);

    const systemInput = await generatePrompt('INTERVIEW_VIDEO_FEEDBACK_SYSTEM_PROMPT_STRATEGY', {
      role: role,
      level: level,
      evaluation,
    });
    response = await generateVideoResult({ systemInput, url, question, bucket });

    try {
      // Clean the response text by removing markdown code blocks and other potential formatting issues
      const cleanedText = response?.parts?.[0]?.text
        ?.replaceAll('```json', '')
        ?.replaceAll('```', '');
      const feedback = JSON.parse(cleanedText);

      // Save the feedback to database
      let saveResponse;
      if (isPlacement) {
        saveResponse = await createInterviewathonAIQuestion({
          questionId: questionId || id,
          question: question,
          transcript: feedback.transcript,
          s3Id: url,
          id: id,
          feedback: feedback,
        });
      } else {
        saveResponse = await createInterviewAIQuestion({
          questionId: questionId || id,
          question: question,
          transcript: feedback.transcript,
          s3Id: url,
          id: id,
          feedback: feedback,
        });
      }

      if (saveResponse?.error) {
        console.error('❌ Error saving feedback to database:', saveResponse.error);
        return NextResponse.json({ error: 'Failed to save feedback to database' }, { status: 500 });
      }

      console.log('✅ Video feedback saved to database successfully');
      return NextResponse.json(
        {
          success: true,
          message: 'Video feedback generated and saved successfully',
        },
        { status: 200 },
      );
    } catch (jsonError) {
      console.log('JSON error:', response?.parts?.[0]?.text);
      console.log('JSON parsing error:', jsonError);
      Audit.logEvent({
        action: 'video_feedback_json_parse_failed',
        userId,
        details: {
          screen: 'interview',
          id,
          s3Id: url,
          error: jsonError,
          result: response?.parts?.[0]?.text,
        },
      });

      // Attempt to regenerate the response
      console.log('Attempting to regenerate AI response due to invalid JSON format');
      response = await generateVideoResult({ systemInput, url, question, bucket });

      try {
        // Try parsing the regenerated response
        const cleanedText = response?.parts?.[0]?.text
          ?.replaceAll('```json', '')
          ?.replaceAll('```', '');
        const feedback = JSON.parse(cleanedText);

        // Save the feedback to database
        let saveResponse;
        if (isPlacement) {
          saveResponse = await createInterviewathonAIQuestion({
            questionId: questionId || id,
            question: question,
            transcript: feedback.transcript,
            s3Id: url,
            id: id,
            feedback: feedback,
          });
        } else {
          saveResponse = await createInterviewAIQuestion({
            questionId: questionId || id,
            question: question,
            transcript: feedback.transcript,
            s3Id: url,
            id: id,
            feedback: feedback,
          });
        }

        if (saveResponse?.error) {
          console.error('❌ Error saving feedback to database:', saveResponse.error);
          return NextResponse.json(
            { error: 'Failed to save feedback to database' },
            { status: 500 },
          );
        }

        console.log('✅ Video feedback saved to database successfully (after retry)');
        return NextResponse.json(
          {
            success: true,
            message: 'Video feedback generated and saved successfully',
          },
          { status: 200 },
        );
      } catch (retryError) {
        console.log('Retry JSON parsing error:', retryError);
        return NextResponse.json(
          { error: 'Failed to parse AI response after retry' },
          { status: 500 },
        );
      }
    }
  } catch (error) {
    console.log('Interview feedback error ', { error });
    Audit.logEvent({
      action: 'video_feedback_generation_failed',
      userId,
      details: {
        screen: 'interview',
        id: id,
        s3Id: url,
        error,
        result: response?.parts?.[0]?.text,
      },
    });
    return NextResponse.json({ error: 'Interview feedback error ' }, { status: 500 });
  }
}

const generateVideoResult = async ({ systemInput, url, question, bucket }) => {
  // Use provided bucket or default
  const bucketName = bucket || defaultBucket;

  const config: VertexAIConfiguration = {
    project,
    model,
    location,
    systemInput,
    credentials: {
      private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
      client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
      client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
    },
    generationConfig: {
      maxOutputTokens: 8192,
      temperature: 1,
      topP: 0.5,
    },
  };

  const vertexAI = new GCPVertexAI(config);
  const stream = await vertexAI.generateContent({
    video: {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: `gs://${bucketName}/${folder}/${url}`,
      },
    },
    question,
  });

  return stream;
};
