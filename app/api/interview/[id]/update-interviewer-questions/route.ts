import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { db } from '@/prisma/db';

export const maxDuration = 60;

export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    const token = await getToken({ req: request });
    const { interviewerQuestions } = await request.json();
    const { id } = params;

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!id) {
      return NextResponse.json(
        { error: 'Interview ID is required' },
        { status: 400 }
      );
    }

    // First, check if the interview exists at all
    const interview = await db.careerPractice.findFirst({
      where: {
        id,
      },
      include: {
        eventDetails: {
          select: {
            organizationId: true,
            name: true,
          },
        },
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    console.log('Interview lookup:', { id, found: !!interview, userId: token.sub });

    if (!interview) {
      return NextResponse.json(
        { error: 'Interview not found', debug: { id, userId: token.sub } },
        { status: 404 }
      );
    }

    // For now, skip the complex permission check and allow all authenticated users
    // TODO: Add proper permission check later
    console.log('Interview found:', {
      interviewId: interview.id,
      organizationId: interview.eventDetails?.organizationId,
      userId: token.sub
    });

    // Check if interview is completed
    if (interview.interviewStatus === 'COMPLETED') {
      return NextResponse.json(
        { error: 'Cannot modify questions for completed interviews' },
        { status: 400 }
      );
    }

    // Update the interview with interviewer questions
    const updatedInterview = await db.careerPractice.update({
      where: { id },
      data: {
        interviewerQuestions: interviewerQuestions || null,
      },
      select: {
        id: true,
        interviewerQuestions: true,
        interviewStatus: true,
        user: {
          select: {
            name: true,
            email: true,
          },
        },
        eventDetails: {
          select: {
            name: true,
            round: true,
          },
        },
      },
    });

    return NextResponse.json(
      {
        message: 'Interviewer questions updated successfully',
        interview: updatedInterview,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error updating interviewer questions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
