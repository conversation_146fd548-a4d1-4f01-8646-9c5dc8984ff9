import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { id } = await request.json();

  try {
    const MembershipOnEventDetails = await db.membershipOnEventDetails.delete({
      where: {
        id: id,
      },
    });

    return NextResponse.json(
      {
        items: MembershipOnEventDetails,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log('Error Deleting member access', error);
    return NextResponse.json(
      {
        error: 'Error Deleting member access',
      },
      { status: 500 },
    );
  }
}
