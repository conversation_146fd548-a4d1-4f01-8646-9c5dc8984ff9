import { NextResponse } from 'next/server';

import VideoCallInterview from '@/components/interview/video-call-interview';
import StaffRescheduledInterviewNotification from '@/emails/reschedule-staff-video-meeting';
import RescheduleCandidate from '@/emails/reschedule-video-call-candidate';
import StaffInterviewNotification from '@/emails/staff-video-meeting';
import campedMailer from '@/lib/campedMailer';
import { db } from '@/prisma/db';
import { addParticipant } from '@/services/apicall';
import { convertToIST } from '@/utils/utc-to-ist';
import { render } from '@react-email/render';
import { getToken } from 'next-auth/jwt';

export const maxDuration = 299;

export async function POST(request: any) {
  const token = await getToken({ req: request });
  let meeting, staffs; // Changed from staff to staffs
  const {
    id,
    meetingType,
    meetingDateTime,
    interviewers, // This will be an array of interviewer objects
    meetingDuration,
    address,
    userId,
    platform,
    endTime,
  } = await request.json();
  if (!token && !id)
    return NextResponse.json(
      {
        message: 'Unauthorized',
      },
      { status: 401 },
    );

  if (!id) {
    return NextResponse.json(
      {
        message: 'Missing testId',
      },
      { status: 400 },
    );
  }
  const careerPractice: any = await db.careerPractice.findUnique({
    where: {
      id,
    },
    include: {
      eventDetails: {
        include: {
          organization: true,
        },
      },
      VideoCallInterview: true,
      user: {
        include: {
          userProfile: true,
        },
      },
    },
  });

  if (!careerPractice) {
    return NextResponse.json({ error: 'Career practice not found' }, { status: 404 });
  }

  // Update staff query to handle array of interviewers
  if (interviewers?.length > 0) {
    staffs = await db.user.findMany({
      where: {
        id: { in: interviewers.map((interviewer: any) => interviewer.userId) },
      },
      include: {
        userProfile: {
          select: {
            fullName: true,
          },
        },
      },
    });
  }

  const candidateName =
    careerPractice?.user?.userProfile?.fullName === '' ||
    !careerPractice?.user?.userProfile?.fullName
      ? careerPractice?.user?.email
      : careerPractice?.user?.userProfile?.fullName;

  const videoCallInterview = careerPractice?.VideoCallInterview?.[0];
  if (meetingType === 'videoCall') {
    // Add user participant
    const userParticipant = await addParticipant(
      videoCallInterview?.meetingId,
      careerPractice?.user?.id,
      'group_call_participant',
    );

    // Add staff participants
    const staffParticipants = await Promise.all(
      staffs?.map(async (staff) => {
        const participant = await addParticipant(
          videoCallInterview?.meetingId,
          staff.id,
          'group_call_host',
        );
        return { ...participant, userId: staff.id };
      }),
    );

    // Send emails to user and all staff members
    const [userMail, ...staffEmails] = await Promise.all([
      sendEmail({
        email: careerPractice?.user?.email,
        location: address,
        inPerson: meetingType === 'inPerson',
        interviewTime: meetingDateTime,
        organization: careerPractice?.eventDetails?.organization,
        isStaff: false,
        candidateName: '',
        staffName: '',
        interviewRole: careerPractice?.role,
        candidateLink: '',
        platform,
        endTime: '',
        calendarEventId: '',
      }),
      ...staffs?.map((staff) => {
        const staffName = staff?.userProfile?.fullName || staff?.email;
        return sendEmail({
          email: staff.email,
          location: address,
          inPerson: meetingType === 'inPerson',
          interviewTime: meetingDateTime,
          organization: careerPractice?.eventDetails?.organization,
          isStaff: true,
          candidateName,
          staffName,
          interviewRole: careerPractice?.role,
          platform,
          endTime,
          calendarEventId: videoCallInterview?.interviewerId?.includes(staff.id)
            ? videoCallInterview?.meetingDetails?.calenderEvent?.id
            : null,
          candidateLink: `${process.env.NEXT_PUBLIC_API_BASE_URL}/interviews/${careerPractice?.eventId}/${careerPractice?.id}`,
        });
      }),
    ]);

    // Cancel existing calendar events if interviewers changed
    if (
      videoCallInterview?.meetingDetails?.calenderEvent?.id &&
      !videoCallInterview?.interviewerId?.some((id) => staffs?.some((staff) => staff.id === id))
    ) {
      const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/integration/${platform}/calendar/cancel-event`;
      await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId: careerPractice?.eventDetails?.organization?.id,
          calendarEventId: videoCallInterview?.meetingDetails?.calenderEvent?.id,
        }),
      });
    }

    // Update meeting with multiple participants
    meeting = await db.videoCallInterview.update({
      where: { id: videoCallInterview?.id },
      data: {
        participants: [
          {
            userId: careerPractice?.user?.id,
            email: careerPractice?.user?.email,
            meetingUserId: userParticipant?.id?.data?.id,
            meetingUserToken: userParticipant?.id?.data?.token,
            userMail,
          },
          ...staffParticipants.map((staff, index) => ({
            userId: staff.userId,
            email: staffs[index].email,
            meetingUserId: staff?.id?.data?.id,
            meetingUserToken: staff?.id?.data?.token,
          })),
        ],
        meetingType,
        scheduleTime: new Date(meetingDateTime),
        interviewDuration: meetingDuration,
        interviewerId: staffs.map((staff) => staff.id),
        meetingDetails: {
          calenderEvent: staffEmails[0]?.calenderEvent?.id
            ? staffEmails[0]?.calenderEvent
            : videoCallInterview?.meetingDetails?.calenderEvent,
        },
      },
    });

    if (!meeting) {
      return NextResponse.json({ error: 'Failed to create meeting' }, { status: 500 });
    }
  } else {
    if (!careerPractice?.eventDetails?.organization?.hrmsUrl) {
      const [userMail] = await Promise.all([
        sendEmail({
          email: careerPractice?.user?.email,
          location: address,
          inPerson: meetingType === 'inPerson',
          interviewTime: meetingDateTime,
          organization: careerPractice?.eventDetails?.organization,
          isStaff: false,
          candidateName: '',
          staffName: '',
          interviewRole: careerPractice?.role,
          candidateLink: '',
          platform,
          endTime: '',
          calendarEventId: '',
        }),
        ...staffs?.map(async (item) => {
          const staffName =
            item?.userProfile?.fullName === '' || !item?.userProfile?.fullName
              ? item?.email
              : item?.userProfile?.fullName;

          const staff = await sendEmail({
            email: careerPractice?.user?.email,
            location: address,
            inPerson: meetingType === 'inPerson',
            interviewTime: meetingDateTime,
            organization: careerPractice?.eventDetails?.organization,
            isStaff: true,
            candidateName,
            staffName,
            interviewRole: careerPractice?.role,
            candidateLink: `${process.env.NEXT_PUBLIC_API_BASE_URL}/interviews/${careerPractice?.eventId}/${careerPractice?.id}`,
            platform,
            endTime: '',
            calendarEventId: '',
          });
          return staff;
        }),
      ]);
      if (!userMail) {
        return NextResponse.json({ error: 'Failed to send email' }, { status: 500 });
      }
    }
    if (
      videoCallInterview?.meetingType === 'videoCall' &&
      videoCallInterview?.meetingDetails?.calenderEvent?.id
    ) {
      const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/integration/${platform}/calendar/cancel-event`;
      await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId: careerPractice?.eventDetails?.organization?.id,
          calendarEventId: videoCallInterview?.meetingDetails?.calenderEvent?.id,
        }),
      });
    }
    meeting = await db.videoCallInterview.update({
      where: { id: careerPractice?.VideoCallInterview?.[0]?.id },
      data: {
        participants: [
          {
            userId: careerPractice?.user?.id,
            email: careerPractice?.user?.email,
          },
          ...staffs?.map((item) => {
            return {
              userId: item?.id,
              email: item?.email,
            };
          }),
        ],
        interviewerId: staffs?.map((item) => item?.id),
        meetingType,
        address: address,
        interviewDuration: meetingDuration,
        scheduleTime: new Date(meetingDateTime),
        meetingDetails: {},
      },
    });
    if (!meeting) {
      return NextResponse.json({ error: 'Failed to create meeting' }, { status: 500 });
    }
  }

  return NextResponse.json({ meeting }, { status: 200 });
}

const generateEmailUrl = (baseUrl: string, userEmail: string, token: string) => {
  return `${baseUrl}/api/auth/callback/${encodeURIComponent('email')}?email=${encodeURIComponent(
    userEmail,
  )}&token=${encodeURIComponent(token)}&callbackUrl=${encodeURIComponent(
    `${baseUrl}/my-interviews`,
  )}`;
};

const sendEmail = async ({
  email,
  location,
  inPerson,
  interviewTime,
  organization,
  isStaff,
  candidateName,
  staffName,
  interviewRole,
  candidateLink,
  platform,
  endTime,
  calendarEventId,
}) => {
  let emailUrl;
  if (email) {
    emailUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/sign-in?from=/my-interviews`;
  }
  if (isStaff) {
    let html = calendarEventId
      ? render(
          StaffRescheduledInterviewNotification({
            meetingUrl: emailUrl,
            location,
            inPerson,
            staffName,
            candidateName,
            interviewRole,
            organization: organization?.name,
            emailIdentifier: null,
            interviewTime: convertToIST(interviewTime),
            candidateLink,
          }),
          {
            pretty: true,
          },
        )
      : render(
          StaffInterviewNotification({
            meetingUrl: emailUrl,
            location,
            inPerson,
            staffName,
            candidateName,
            interviewRole,
            organization: organization?.name,
            emailIdentifier: null,
            interviewTime: convertToIST(interviewTime),
            candidateLink,
          }),
          {
            pretty: true,
          },
        );
    const mailResponse = await campedMailer.send({
      from: process.env.SEND_EMAIL_FROM || '',
      to: email,
      subject: `Interview ${calendarEventId ? 'rescheduled' : 'scheduled'} with ${candidateName}`,
      html,
    });
    if (mailResponse && platform) {
      try {
        const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/integration/${platform}/calendar/${
          calendarEventId ? 'update' : 'create'
        }-event`;
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            date: interviewTime,
            endTime: endTime,
            organizationId: organization?.id,
            user: email,
            eventId: mailResponse?.MessageId,
            subject: `Interview ${
              calendarEventId ? 'rescheduled' : 'scheduled'
            } with ${candidateName}`,
            body: html,
            calendarEventId,
          }),
        });
        const responseBody = await response.json();
        return { ...mailResponse, calenderEvent: { ...responseBody?.calendar } };
      } catch (e) {
        console.log({ 'createVideoQuestion api fail': e });
      }
    }
    return { ...mailResponse };
  }
  const mailResponse = await campedMailer.send({
    from: process.env.SEND_EMAIL_FROM || '',
    to: email,
    subject: `Your interview has been rescheduled`,
    html: render(
      RescheduleCandidate({
        organization: organization?.name,
        meetingUrl: emailUrl,
        location,
        inPerson,
        emailContent: null,
        emailIdentifier: null,
        interviewTime: convertToIST(interviewTime),
        role: interviewRole,
      }),
      {
        pretty: true,
      },
    ),
  });
  return mailResponse;
};
