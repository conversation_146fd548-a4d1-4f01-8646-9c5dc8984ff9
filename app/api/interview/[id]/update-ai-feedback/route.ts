import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { id, data } = await request.json();

  try {
    // First, get the existing feedback
    const existingPractice = await db.careerPractice.findUnique({
      where: { id },
      select: { feedback: true, timing: true },
    });

    // Update with merged feedback
    let careerPractice = await db.careerPractice.update({
      where: { id },
      data: {
        conversation: [
          {
            s3Id: data?.s3Key,
            room_name: data?.room_name,
          },
        ],
        timing: {
          ...existingPractice?.timing,
          completedTime: new Date(),
          feedBackGenerateTime: new Date(),
        },
        completedTime: new Date(),
        finalScore: Number(data?.feedback?.overall_score ?? 0),
        interviewStatus: 'COMPLETED',
        feedback: {
          ...(existingPractice?.feedback || {}),
          ...data?.feedback,
        },
      },
    });

    return NextResponse.json(
      {
        careerPractice,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'update careerPractice Not Found' }, { status: 500 });
  }
}
