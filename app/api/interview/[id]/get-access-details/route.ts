import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { eventId, role, membershipId } = await request.json();
  let where: any = {
    id: eventId,
  };
  if (!['admin', 'owner']?.includes(role?.toLowerCase())) {
    where.MembershipOnEventDetails = {
      some: {
        membershipId,
      },
    };
  }
  try {
    const [eventDetails, careerPracticesCount] = await Promise.all([
      db.eventDetails.findFirst({
        include: {
          MembershipOnEventDetails: {
            where: {
              membership: {
                role: { in: ['ADMIN', 'MEMBER'] },
              },
            },
            include: {
              membership: {
                include: {
                  user: {
                    include: {
                      userProfile: {
                        select: {
                          fullName: true,
                        },
                      },
                    },
                  },
                  DepartmentMembershipMapping: {
                    select: {
                      role: true,
                      department: {
                        select: {
                          name: true,
                        },
                      },
                    },
                  },
                  GroupMembershipMapping: {
                    select: {
                      role: true,
                      group: {
                        select: {
                          name: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        where,
      }),
      db.careerPractice.count({
        where: {
          eventId,
        },
      }),
    ]);

    return NextResponse.json(
      {
        items: eventDetails,
        careerPracticesCount,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log('Error fetching interview list', error);
    return NextResponse.json(
      {
        error: 'Error fetching interview list',
      },
      { status: 500 },
    );
  }
}
