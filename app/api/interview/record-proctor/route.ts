import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { data, id } = await request.json();

  console.log('📥 API: Received proctoring data for ID:', id);
  console.log('📊 API: Proctoring data structure:', JSON.stringify(data, null, 2));

  try {
    let feedbacks = await db.careerPractice.update({
      where: { id },
      data: { proctorWarnings: data ?? {} },
    });

    console.log('✅ API: Successfully saved proctoring data to database');

    return NextResponse.json({
      feedbacks,
      status: 200,
    });
  } catch (error) {
    console.log('❌ API: Error saving proctoring data:', { error });
    return NextResponse.json({ error: 'Interview Not Found', status: 500 });
  }
}
