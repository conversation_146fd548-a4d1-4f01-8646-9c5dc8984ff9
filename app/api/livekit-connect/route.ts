import { NextRequest, NextResponse } from 'next/server';

import { AccessToken, AccessTokenOptions, VideoGrant } from 'livekit-server-sdk';

const API_KEY = process.env.LIVEKIT_API_KEY;
const API_SECRET = process.env.LIVEKIT_API_SECRET;
const LIVEKIT_URL = process.env.LIVEKIT_URL;

export const revalidate = 0;

export type ConnectionDetails = {
  serverUrl: string;
  roomName: string;
  participantName: string;
  participantToken: string;
  agentType: 'video' | 'audio';
  role: string;
  level: string;
};

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const agent = 'video'; // Default to video if not specified
    const role = 'Software Developer';
    const level = 'Mid-Level';
    const roomName = `room-${Math.random().toString(36).substring(2, 15)}`;

    if (!LIVEKIT_URL || !API_KEY || !API_SECRET) {
      throw new Error('Missing LiveKit environment variables');
    }

    const participantIdentity = `voice_assistant_user_${Math.floor(Math.random() * 10_000)}`;

    const participantToken = await createParticipantToken(
      {
        identity: participantIdentity,
        metadata: JSON.stringify({ agentType: agent }), // Pass agent type in metadata
      },
      roomName,
    );

    return NextResponse.json(
      {
        serverUrl: LIVEKIT_URL,
        roomName,
        participantName: participantIdentity,
        participantToken,
        agentType: agent, // Return agent type to the client
        role: role,
        agent: agent,
      },
      {
        headers: new Headers({ 'Cache-Control': 'no-store' }),
      },
    );
  } catch (error) {
    console.error(error);
    return new NextResponse(error instanceof Error ? error.message : 'Unknown error', {
      status: 500,
    });
  }
}

async function createParticipantToken(
  userInfo: AccessTokenOptions,
  roomName: string,
): Promise<string> {
  const at = new AccessToken(API_KEY!, API_SECRET!, {
    ...userInfo,
    ttl: '15m',
  });

  const grant: VideoGrant = {
    room: roomName,
    roomJoin: true,
    canPublish: true,
    canPublishData: true,
    canSubscribe: true,
  };

  at.addGrant(grant);

  return await at.toJwt();
}
