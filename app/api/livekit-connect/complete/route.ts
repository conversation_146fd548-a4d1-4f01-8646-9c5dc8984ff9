import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export const maxDuration = 299;

export async function POST(request: NextRequest) {
  try {
    const token = await getToken({ req: request });
    const { careerPracticeId } = await request.json();

    if (!careerPracticeId) {
      return NextResponse.json(
        {
          message: 'Career practice ID is required',
        },
        { status: 400 },
      );
    }

    // Verify the career practice exists and belongs to the user
    const careerPractice = await db.careerPractice.findFirst({
      where: {
        id: careerPracticeId,
        userId: token?.sub || '',
      },
    });

    if (!careerPractice) {
      return NextResponse.json(
        {
          message: 'Career practice not found or unauthorized',
        },
        { status: 404 },
      );
    }

    // Update the career practice to mark it as completed
    await db.careerPractice.update({
      where: { id: careerPractice.id },
      data: {
        interviewStatus: 'COMPLETED',
        completedTime: new Date(),
      },
    });

    return NextResponse.json(
      {
        message: 'Interview session completed successfully',
        status: 'success',
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Error completing interview session:', error);
    return NextResponse.json(
      {
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}
