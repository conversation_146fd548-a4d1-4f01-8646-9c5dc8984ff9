import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/prisma/db';

export async function GET(request: NextRequest) {
  try {
    // Fetch all active default assessment templates
    const templates = await db.assessmentTemplate.findMany({
      where: {
        isActive: true,
        isDefault: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
        roleType: true,
        criteria: true,
        isDefault: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return NextResponse.json(templates);
  } catch (error) {
    console.error('Error fetching assessment templates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
