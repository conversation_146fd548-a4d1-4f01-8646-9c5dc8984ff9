import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const data = await request.json();
  try {
    const support = await db.support.create({
      data: { ...data },
    });
    return NextResponse.json(
      {
        support,
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'support create failed' }, { status: 500 });
  }
}
