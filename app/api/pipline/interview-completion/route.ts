import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api`;

export const maxDuration = 299;

async function handleIntegrations(integrations, interview) {
  const webhook = await handleWebhooks(interview);
  if (!webhook.ok) {
    console.error('Error in webhook: ', webhook.error);
  }
  const discordWebhook = await handleDiscordIntegration(interview);
  if (!discordWebhook.ok) {
    console.error('Error in discord webhook: ', discordWebhook.error);
  }
  const teamsWebhook = await handleMsTeamsIntegration(interview);
  if (!teamsWebhook.ok) {
    console.error('Error in teams webhook: ', discordWebhook.error);
  }
  for (const integration of integrations) {
    switch (integration.platform) {
      case 'SLACK':
        const slackResult: any = await handleSlackIntegration(integration, interview);
        if (!slackResult.ok) {
          console.error('Error in slack integration: ', slackResult.error);
        }
        break;
    }
  }
}

async function handleSlackIntegration(integration, interview) {
  try {
    if (integration.page !== null && Object.keys(integration.page).length > 0) {
      const url = `${apiUrl}/integration/slack/send-data`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          access_token: integration?.accessToken,
          channelId: integration?.page?.id,
          interview,
        }),
      });
      return response;
    }
    return {
      ok: true,
      data: undefined,
    };
  } catch (err) {
    return {
      ok: false,
      error: err,
    };
  }
}

async function handleDiscordIntegration(interview) {
  try {
    const webhooks = await db.webhook.findMany({
      where: {
        source: { in: ['DISCORD'] },
        organizationId: interview?.eventDetails?.organization?.id,
      },
    });

    await Promise.all(
      webhooks?.map(async (webhook) => {
        const url = `${apiUrl}/integration/discord/send-data`;
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ url: webhook?.url, interview }),
        });
        return response;
      }),
    );

    return {
      ok: true,
      data: undefined,
    };
  } catch (err) {
    return {
      ok: false,
      error: err,
    };
  }
}

async function handleMsTeamsIntegration(interview) {
  try {
    const webhooks = await db.webhook.findMany({
      where: {
        source: { in: ['MS_TEAMS'] },
        organizationId: interview?.eventDetails?.organization?.id,
      },
    });

    await Promise.all(
      webhooks?.map(async (webhook) => {
        const url = `${apiUrl}/integration/teams/send-data`;
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ url: webhook?.url, interview }),
        });
        return response;
      }),
    );

    return {
      ok: true,
      data: undefined,
    };
  } catch (err) {
    return {
      ok: false,
      error: err,
    };
  }
}

async function handleWebhooks(interview) {
  try {
    const webhooks = await db.webhook.findMany({
      where: {
        source: { notIn: ['DISCORD'] },
        organizationId: interview?.eventDetails?.organization?.id,
      },
    });

    // send request to all webhooks
    await Promise.all(
      webhooks.map(async (webhook) => {
        await fetch(webhook.url, {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            webhookId: webhook.id,
            data: {
              interviewId: interview?.id,
              eventId: interview?.eventDetails?.id,
              name: interview?.event,
              level: interview?.Level,
              role: interview?.role,
              candidate: {
                emailId: interview?.user?.email,
                name: interview?.user?.userProfile?.fullName,
              },
            },
          }),
        });
      }),
    );
    return {
      ok: true,
      data: undefined,
    };
  } catch (err) {
    return {
      ok: false,
      error: err,
    };
  }
}

export async function POST(req: NextRequest) {
  try {
    const { integrations, interview } = await req.json();
    await handleIntegrations(integrations, interview);
    return NextResponse.json({ message: 'Success' });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to send data to Discord' }, { status: 500 });
  }
}
