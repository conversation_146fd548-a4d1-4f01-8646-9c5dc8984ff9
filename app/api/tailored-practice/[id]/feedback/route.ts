import { NextResponse } from 'next/server';

import { OpenAIStreamPayload } from '@/lib/OpenAIStream';
import retryOpenAI from '@/lib/retryOpenApi';
import { updateFeedbackBasedOnConversation } from '@/pages/api/server/admin/cron/feedback-result';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';

// Import video feedback functionality
import { GCPVertexAI } from '@/lib/ai-models/src/models/gcp-vertexai';
import { VertexAIConfiguration } from '@/lib/ai-models/src/types/vertexai-model';
import Audit from '@/lib/audit';
import { getUserSubscriptionPlan } from '@/pages/api/getSubscriptionStatus';
import { convertTailoredPracticeVideo } from '@/utils/video-conversion';
import { createComprehensiveFallbackFeedback } from '@/utils/video-feedback-fallback';

export const maxDuration = 299;

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing env var from OpenAI');
}

const generateFeedback = async (messages) => {
  const payload: OpenAIStreamPayload = {
    model: process.env.AZURE_OPEN_AI_MODEL || '',
    messages,
    temperature: 0.7,
    top_p: 0.8,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: false,
    n: 1,
    response_format: { type: 'json_object' },
  };
  const result = await retryOpenAI(payload, 3, true);
  return result;
};

export async function POST(request: any) {
  const requestBody = await request.json();

  // Check if this is a video feedback request (has video-related parameters)
  const isVideoFeedbackRequest = requestBody.url || requestBody.question || requestBody.userId;

  if (isVideoFeedbackRequest) {
    // Handle video-based feedback generation
    return handleVideoBasedFeedback(requestBody);
  }

  // Handle "Regenerate Feedback" case - get practice data and generate video feedback
  const { id, organizationId } = requestBody;

  if (id && !isVideoFeedbackRequest) {
    // This is likely a "Regenerate Feedback" request - get practice data and generate video feedback
    try {
      const careerPractice = await db.careerPractice.findUnique({
        where: {
          id: id,
        },
      });

      if (!careerPractice) {
        return NextResponse.json(
          {
            error: 'Practice not found',
            practiceId: id,
          },
          { status: 404 }
        );
      }

      // Create video feedback request using practice data
      const videoFeedbackRequest = {
        url: id, // Use practice ID as video URL for HLS videos
        question: "Tell me about yourself", // Default question for regeneration
        userId: careerPractice.userId,
        level: careerPractice.level,
        role: careerPractice.role,
        organizationId: organizationId,
        id: id,
        questionId: "regenerate", // Special question ID for regeneration
      };

      console.log('🔄 Regenerating feedback for practice:', id);
      return handleVideoBasedFeedback(videoFeedbackRequest);

    } catch (error) {
      console.error('Error fetching practice for regeneration:', error);
      return NextResponse.json(
        {
          error: 'Failed to fetch practice data for regeneration',
          practiceId: id,
        },
        { status: 500 }
      );
    }
  }

  // Handle legacy conversation-based feedback (fallback)
  try {
    const careerPractice = await db.careerPractice.findUnique({
      where: {
        id: id,
      },
    });

    if (!(careerPractice?.conversation as any)?.length) {
      return NextResponse.json(
        {
          error: 'No conversation data found. Please submit video responses first before generating overall feedback.',
          details: 'This practice session has no conversation data. Use the video-feedback endpoint to submit individual responses first.',
          practiceId: id,
          conversationLength: (careerPractice?.conversation as any)?.length || 0,
        },
        { status: 400 },
      );
    }
    const overAllResult = updateFeedbackBasedOnConversation(careerPractice?.conversation);

    const questions = (careerPractice?.conversation as any)?.map((item, index) => {
      return `question ${index + 1}: ${item?.answer},
    feedback summary: ${item?.feedback?.short_summary},
     ${
       item?.feedback?.proctoring?.camera_angle
         ? `camera angle:${item?.feedback?.proctoring?.camera_angle},`
         : ''
     }     ${
       item?.feedback?.proctoring?.professional_attire
         ? `professional attire:${item?.feedback?.proctoring?.professional_attire},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['eye_contact_&_posture']
         ? `eye contact and posture:${item?.feedback?.proctoring?.['eye_contact_&_posture']},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['suspicious_activity']
         ? `suspicious activity:${item?.feedback?.proctoring?.suspicious_activity},`
         : ''
     } ${
       item?.feedback?.proctoring?.['words_per_minute']
         ? `words per minute:${item?.feedback?.proctoring?.words_per_minute},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['emotional_state']
         ? `emotional state:${item?.feedback?.proctoring?.emotional_state},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['candidate_movement']
         ? `candidate movement:${item?.feedback?.proctoring?.suspicious_activity},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['background']
         ? `background:${item?.feedback?.proctoring?.suspicious_activity},`
         : ''
     }`;
    });
    const question = questions?.filter((element) => element !== null);

    if (question?.length === 0) {
      return NextResponse.json({ status: 200 });
    }
    const feedback = await generateFeedback([
      {
        role: 'system',
        content: await generatePrompt('CONSOLIDATED_FEEDBACK_PROMPT_STRATEGY', {
          role: careerPractice?.role,
          level: careerPractice?.level,
          questions: question?.join('\n'),
          fullScreen: '',
          tabSwitch: '',
          evaluation: 'medium',
        }),
      },
    ]);

    if (organizationId && careerPractice?.userId !== 'Visitor') {
      await db.leaderboard.create({
        data: {
          organizationId: organizationId,
          userId: careerPractice?.userId ?? '',
          score: Math.round(Number((overAllResult as any)?.overall_score) ?? 0),
          eventId: careerPractice?.eventId ?? careerPractice?.id ?? '',
          eventType: 'TAILORED_PRACTICE',
        },
      });
    }

    const response = await db.careerPractice.update({
      data: {
        feedback: {
          ...(careerPractice?.feedback as any),
          ...overAllResult,
          ...feedback,
        },
        timing: {
          ...(careerPractice as any)?.timing,
          feedBackGenerateTime: new Date(),
        },
        finalScore: Math.round((overAllResult as any)?.overall_score),
      },
      where: {
        id: id,
      },
    });
    return NextResponse.json(
      {
        response,
        ok: true,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log(`Error generating tailored feedback ${error}`);
    return NextResponse.json(
      {
        error: 'Error generating tailored interview feedback',
        ok: false,
      },
      { status: 500 },
    );
  }
}

// Helper function to get GCP bucket
async function getGCPBucket(organizationId?: string): Promise<string> {
  // Default bucket for tailored practice videos
  return 'aceprep-us';
}



// Video-based feedback handler function
async function handleVideoBasedFeedback(requestBody: any) {
  const { url, question, userId, level, role, organizationId, id, questionId } = requestBody;
  let plan = {};
  let membership, response;
  let convertedVideoPath: string | null = null;

  try {
    if (userId && userId !== 'Visitor') {
      membership = await db.membership.findMany({
        where: {
          user: { id: userId },
          role: { in: ['STUDENT'] },
        },
      });
      if (membership?.length === 0 && !membership?.length) {
        plan = await getUserSubscriptionPlan(userId);
      }
    }

    const hasAdvancedAnalysis =
      Boolean((plan as unknown as { plan?: string })?.plan) ||
      ['Software Engineer', 'History (Ancient & Medieval)']?.includes(role ?? '') ||
      membership?.length > 0;

    let systemPrompt;
    if (hasAdvancedAnalysis) {
      systemPrompt = await generatePrompt('TAILORED_PRACTICE_FEEDBACK_SYSTEM_PROMPT_STRATEGY', {
        role: role,
        level: level,
      });
    } else {
      systemPrompt = await generatePrompt(
        'TAILORED_PRACTICES_NON_PREMIUM_FEEDBACK_SYSTEM_PROMPT_STRATEGY',
        {
          role: role,
          level: level,
        },
      );
    }

    // Get bucket from organization or use default
    const bucket = await getGCPBucket(organizationId);

    // Check if this is an HLS video (tailored practice videos are stored as .m3u8)
    const practiceId = url;
    let videoPath = url;
    let isHLSVideo = false;

    // Detect if this is a tailored practice HLS video
    if (!url.includes('.') || url.endsWith('.m3u8')) {
      isHLSVideo = true;
      console.log(`Detected HLS video for practice ID: ${practiceId}`);

      try {
        // Convert HLS to MP4 for Gemini Vision API
        console.log('Starting HLS to MP4 conversion...');
        const conversionResult = await convertTailoredPracticeVideo(practiceId, {
          organizationId,
          userId,
          onProgress: (progress) => {
            console.log(`Conversion progress: ${progress}%`);
          },
        });

        if (!conversionResult.success) {
          throw new Error(`Video conversion failed: ${conversionResult.error}`);
        }

        convertedVideoPath = conversionResult.mp4FilePath;
        videoPath = convertedVideoPath.replace(`gs://${bucket}/`, '');

        console.log(`✅ HLS to MP4 conversion completed: ${convertedVideoPath}`);

        // Log successful conversion
        Audit.logEvent({
          action: 'tailored_practice_video_conversion_success',
          userId: userId,
          details: {
            screen: 'tailored practices',
            practiceId,
            organizationId,
            conversionTime: conversionResult.conversionTime,
            originalSize: conversionResult.originalSize,
            convertedSize: conversionResult.convertedSize,
          },
        });

      } catch (conversionError) {
        console.error('Video conversion failed:', conversionError);

        // Determine error type from conversion error
        let errorType = 'conversion_error';
        if (conversionError.message.includes('not found')) {
          errorType = 'file_not_found';
        } else if (conversionError.message.includes('timed out')) {
          errorType = 'timeout_error';
        } else if (conversionError.message.includes('size') && conversionError.message.includes('exceeds')) {
          errorType = 'file_too_large';
        } else if (conversionError.message.includes('FFmpeg is not available')) {
          errorType = 'system_error';
        }

        // Log conversion failure
        Audit.logEvent({
          action: 'tailored_practice_video_conversion_failed',
          userId: userId,
          details: {
            screen: 'tailored practices',
            practiceId,
            organizationId,
            error: conversionError.message,
            errorType,
          },
        });

        // Generate fallback feedback instead of returning error
        console.log('Generating fallback feedback due to conversion failure');
        const fallbackFeedback = createComprehensiveFallbackFeedback(
          question,
          role,
          level,
          errorType
        );

        // Save fallback feedback to practice record
        try {
          await db.careerPractice.update({
            where: { id: id },
            data: {
              feedback: fallbackFeedback,
              finalScore: Math.round(fallbackFeedback.overall_score || 0),
              timing: {
                feedBackGenerateTime: new Date(),
              },
            },
          });
          console.log('✅ Fallback feedback saved to practice record');
        } catch (dbError) {
          console.error('Failed to save fallback feedback to practice:', dbError);
        }

        return NextResponse.json(
          {
            success: true,
            message: 'Feedback generated with fallback due to video processing issues',
            fallback: true,
            feedback: fallbackFeedback,
          },
          {
            status: 200,
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0',
            },
          }
        );
      }
    }

    // Generate video analysis with timeout handling
    try {
      console.log('Starting Gemini Vision API analysis...');

      // Set a timeout for the video analysis
      const analysisTimeout = 5 * 60 * 1000; // 5 minutes
      const analysisPromise = generateVideoResult({
        systemInput: systemPrompt,
        url: videoPath,
        question,
        bucket,
        isHLSConverted: isHLSVideo
      });

      response = await Promise.race([
        analysisPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Video analysis timed out')), analysisTimeout)
        )
      ]);

      console.log('✅ Gemini Vision API analysis completed successfully');

    } catch (analysisError) {
      console.error('Video analysis failed:', analysisError);

      // Log analysis failure
      Audit.logEvent({
        action: 'tailored_practice_video_analysis_failed',
        userId: userId,
        details: {
          screen: 'tailored practices',
          practiceId: url,
          organizationId,
          error: analysisError.message,
          isHLSConverted: isHLSVideo,
        },
      });

      // Generate fallback feedback for analysis failure
      const fallbackFeedback = createComprehensiveFallbackFeedback(
        question,
        role,
        level,
        analysisError.message.includes('timed out') ? 'timeout_error' : 'system_error'
      );

      // Save fallback feedback to practice record
      try {
        await db.careerPractice.update({
          where: { id: id },
          data: {
            feedback: fallbackFeedback,
            finalScore: Math.round(fallbackFeedback.overall_score || 0),
            timing: {
              feedBackGenerateTime: new Date(),
            },
          },
        });
        console.log('✅ Analysis fallback feedback saved to practice record');
      } catch (dbError) {
        console.error('Failed to save analysis fallback feedback to practice:', dbError);
      }

      return NextResponse.json(
        {
          success: true,
          message: 'Feedback generated with fallback due to analysis issues',
          fallback: true,
          feedback: fallbackFeedback,
        },
        {
          status: 200,
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
        }
      );
    }

    // Log successful feedback generation
    Audit.logEvent({
      action: 'video_feedback_generation_success',
      userId: userId,
      details: {
        screen: 'tailored practices',
        userId: userId,
        s3Id: url,
        result: response?.parts?.[0]?.text,
        isHLSConverted: isHLSVideo,
      },
    });

    // Parse and save feedback data
    const feedbackData = JSON.parse(response?.parts?.[0]?.text || '{}');

    // Save successful feedback to practice record
    try {
      await db.careerPractice.update({
        where: { id: id },
        data: {
          feedback: feedbackData,
          finalScore: Math.round(feedbackData.overall_score || 0),
          timing: {
            feedBackGenerateTime: new Date(),
          },
        },
      });
      console.log('✅ Video feedback saved to practice record');
    } catch (dbError) {
      console.error('Failed to save video feedback to practice:', dbError);
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Video feedback generated successfully',
        feedback: feedbackData,
      },
      {
        status: 200,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      }
    );

  } catch (error) {
    console.log('tailored video feedback error ', { error });
    Audit.logEvent({
      action: 'video_feedback_generation_failed',
      userId: userId,
      details: {
        screen: 'tailored practices',
        userId: userId,
        s3Id: url,
        error,
        result: response?.parts?.[0]?.text,
      },
    });
    return NextResponse.json({ error: 'tailored video feedback error ' }, { status: 500 });
  } finally {
    // Cleanup converted video file if it was created
    if (convertedVideoPath && convertedVideoPath.includes('converted/')) {
      try {
        // Import cleanup function dynamically to avoid circular imports
        const { HLSToMP4Converter } = await import('@/utils/video-conversion');
        const converter = new HLSToMP4Converter();

        // Extract practice ID from the converted path for cleanup
        const practiceId = url; // Original URL is the practice ID
        converter.cleanupPracticeFiles(practiceId);

        console.log(`✅ Cleaned up converted video files for practice: ${practiceId}`);
      } catch (cleanupError) {
        console.error('Failed to cleanup converted video files:', cleanupError);
      }
    }
  }
}

// Generate video result using Gemini Vision API
const generateVideoResult = async ({ systemInput, url, question, bucket, isHLSConverted = false }) => {
  // Use provided bucket or default
  const bucketName = bucket || 'aceprep';
  const project = process.env.NEXT_PUBLIC_GCP_PROJECT_ID;
  const model = process.env.NEXT_PUBLIC_GCP_MODEL || 'gemini-1.5-pro';
  const location = process.env.NEXT_PUBLIC_GCP_LOCATION || 'us-central1';
  const folder = 'interview-ai';

  // For HLS converted videos, the URL already includes the full path
  // For regular videos, use the folder structure
  let videoUri;
  if (isHLSConverted) {
    // URL already contains the full path for converted videos
    videoUri = url.startsWith('gs://') ? url : `gs://${bucketName}/${url}`;
  } else {
    // Regular video path construction
    videoUri = `gs://${bucketName}/${folder}/${url}`;
  }

  console.log(`Processing video for Gemini Vision API: ${videoUri}`);

  const config: VertexAIConfiguration = {
    project,
    model,
    location,
    systemInput,
    credentials: {
      private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
      client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
      client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
    },
    generationConfig: {
      maxOutputTokens: 8192,
      temperature: 0.5,
      topP: 0.5,
    },
  };

  const vertexAI = new GCPVertexAI(config);
  const stream = await vertexAI.generateContent({
    video: {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: videoUri,
      },
    },
    question,
  });

  return stream;
};
