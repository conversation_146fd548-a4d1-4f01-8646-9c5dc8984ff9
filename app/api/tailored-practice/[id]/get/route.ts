import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

import { getUserSubscriptionPlan } from '../../../../../pages/api/getSubscriptionStatus';

export const maxDuration = 299;

function removeFieldsFromConversation(inputObject) {
  const fieldsToRemove = [
    'impact',
    'clarity',
    'passion',
    'strengths',
    'confidence',
    'communication',
    'areas_of_improvement',
    'language_proficiency',
  ];

  for (const item of inputObject.conversation) {
    for (const field of fieldsToRemove) {
      if (item.feedback && item.feedback[field]) {
        delete item.feedback[field];
      }
    }
  }
  return inputObject;
}

async function getUnansweredQuestions(careerPractice) {
  const unansweredQuestions = careerPractice?.conversation.filter((item) => !item.isAnswered);

  return unansweredQuestions;
}

export async function POST(request: any) {
  const token = await getToken({ req: request });

  const { id, sub } = await request.json();

  if (!id) {
    return NextResponse.json(
      {
        message: 'Missing testId or jobRole and jobLevel',
      },
      { status: 400 },
    );
  }

  const careerPractice = await db.careerPractice.findFirst({
    where: {
      id: id,
      userId: sub || token?.sub,
    },
  });
  if (!careerPractice) {
    return NextResponse.json(
      {
        message: 'Invalid id',
      },
      { status: 400 },
    );
  }

  const unansweredQuestions = await getUnansweredQuestions(careerPractice);

  if (unansweredQuestions?.length === 0) {
    return NextResponse.json(
      {
        message: 'Your interview session has been completed successfully.',
        role: careerPractice?.role,
        result: careerPractice,
      },
      { status: 200 },
    );
  }

  const questionStatus = `${
    (careerPractice as any)?.conversation?.length - Number(unansweredQuestions?.length) + 1
  }/${(careerPractice as any)?.conversation?.length}`;

  return NextResponse.json(
    {
      round: unansweredQuestions?.[0]?.round,
      question: unansweredQuestions?.[0]?.question,
      questionId: unansweredQuestions?.[0]?.id,
      role: careerPractice?.role,
      questionStatus: questionStatus,
    },
    { status: 200 },
  );
}
