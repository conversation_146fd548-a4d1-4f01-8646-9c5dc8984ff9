import { NextResponse } from 'next/server';

import { GCPVertexAI } from '@/lib/ai-models/src/models/gcp-vertexai';
import { VertexAIConfiguration } from '@/lib/ai-models/src/types/vertexai-model';
import Audit from '@/lib/audit';
import { getUserSubscriptionPlan } from '@/pages/api/getSubscriptionStatus';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';
import { convertTailoredPracticeVideo } from '@/utils/video-conversion';
import { createComprehensiveFallbackFeedback } from '@/utils/video-feedback-fallback';

export const maxDuration = 299;

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing env var from OpenAI');
}

const project = process.env.NEXT_PUBLIC_GCP_PROJECT_ID || '';
const model = process.env.NEXT_PUBLIC_GCP_MODEL || '';
const location = process.env.NEXT_PUBLIC_GCP_LOCATION || '';
const defaultBucket = process.env.NEXT_PUBLIC_GCP_BUCKET || '';
const folder = process.env.NEXT_PUBLIC_S3FOLDER || '';

// Function to get GCP bucket from organization or use default
async function getGCPBucket(organizationId) {
  if (!organizationId) return defaultBucket;

  try {
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { gcpBucket: true },
    });

    return organization?.gcpBucket || defaultBucket;
  } catch (error) {
    console.error('Error fetching organization GCP bucket:', error);
    return defaultBucket;
  }
}

// Function to update conversation with feedback
const updateConversation = async (
  data: any,
  transcript: string,
  questionId: string,
  s3Id: string,
  feedback: any,
) => {
  if (!data) {
    return null;
  }

  const conversation: any = data.conversation || [];

  const updatedConversation = conversation.map((item) => {
    if (item?.id === questionId) {
      return {
        ...item,
        answer: transcript,
        s3Id: s3Id,
        isAnswered: true,
        videoOrigin: s3Id ? 'GCP_BUCKET' : '',
        feedback,
        completedTime: new Date(),
      };
    }
    return item;
  });

  return updatedConversation;
};

export async function POST(request: any) {
  const { url, question, userId, level, role, organizationId, id, questionId }: any =
    await request.json();
  let plan = {};
  let membership, response;
  let convertedVideoPath: string | null = null;

  try {
    if (userId && userId !== 'Visitor') {
      membership = await db.membership.findMany({
        where: {
          user: { id: userId },
          role: { in: ['STUDENT'] },
        },
      });
      if (membership?.length === 0 && !membership?.length) {
        plan = await getUserSubscriptionPlan(userId);
      }
    }
    const hasAdvancedAnalysis =
      Boolean((plan as unknown as { plan?: string })?.plan) ||
      ['Software Engineer', 'History (Ancient & Medieval)']?.includes(role ?? '') ||
      membership?.length > 0;
    let systemPrompt;

    if (hasAdvancedAnalysis) {
      systemPrompt = await generatePrompt('TAILORED_PRACTICE_FEEDBACK_SYSTEM_PROMPT_STRATEGY', {
        role: role,
        level: level,
      });
    } else {
      systemPrompt = await generatePrompt(
        'TAILORED_PRACTICES_NON_PREMIUM_FEEDBACK_SYSTEM_PROMPT_STRATEGY',
        {
          role: role,
          level: level,
        },
      );
    }

    // Get bucket from organization or use default
    const bucket = await getGCPBucket(organizationId);

    // Check if this is an HLS video (tailored practice videos are stored as .m3u8)
    // The 'url' parameter for tailored practice is actually the practiceId
    const practiceId = url;
    let videoPath = url;
    let isHLSVideo = false;

    // Detect if this is a tailored practice HLS video
    // Tailored practice videos don't have file extensions in the URL parameter
    if (!url.includes('.') || url.endsWith('.m3u8')) {
      isHLSVideo = true;
      console.log(`Detected HLS video for practice ID: ${practiceId}`);

      try {
        // Convert HLS to MP4 for Gemini Vision API
        console.log('Starting HLS to MP4 conversion...');
        const conversionResult = await convertTailoredPracticeVideo(practiceId, {
          organizationId,
          userId,
          onProgress: (progress) => {
            console.log(`Conversion progress: ${progress}%`);
          },
        });

        if (!conversionResult.success) {
          throw new Error(`Video conversion failed: ${conversionResult.error}`);
        }

        convertedVideoPath = conversionResult.mp4FilePath;
        videoPath = convertedVideoPath.replace(`gs://${bucket}/`, '');

        console.log(`✅ HLS to MP4 conversion completed: ${convertedVideoPath}`);

        // Log successful conversion
        Audit.logEvent({
          action: 'tailored_practice_video_conversion_success',
          userId: userId,
          details: {
            screen: 'tailored practices',
            practiceId,
            organizationId,
            conversionTime: conversionResult.conversionTime,
            originalSize: conversionResult.originalSize,
            convertedSize: conversionResult.convertedSize,
          },
        });

      } catch (conversionError) {
        console.error('Video conversion failed:', conversionError);

        // Determine error type from conversion error
        let errorType = 'conversion_error';
        if (conversionError.message.includes('not found')) {
          errorType = 'file_not_found';
        } else if (conversionError.message.includes('timed out')) {
          errorType = 'timeout_error';
        } else if (conversionError.message.includes('size') && conversionError.message.includes('exceeds')) {
          errorType = 'file_too_large';
        } else if (conversionError.message.includes('FFmpeg is not available')) {
          errorType = 'system_error';
        }

        // Log conversion failure
        Audit.logEvent({
          action: 'tailored_practice_video_conversion_failed',
          userId: userId,
          details: {
            screen: 'tailored practices',
            practiceId,
            organizationId,
            error: conversionError.message,
            errorType,
          },
        });

        // Generate fallback feedback instead of returning error
        console.log('Generating fallback feedback due to conversion failure');
        const fallbackFeedback = createComprehensiveFallbackFeedback(
          question,
          role,
          level,
          errorType
        );

        // Save fallback feedback to database
        if (id && questionId) {
          try {
            const data = await db.careerPractice.findFirst({
              where: {
                id: id,
                userId: userId,
              },
            });

            const updatedConversation = await updateConversation(
              data,
              fallbackFeedback.transcript,
              questionId,
              url,
              fallbackFeedback,
            );

            if (updatedConversation) {
              await db.careerPractice.update({
                data: {
                  conversation: updatedConversation,
                },
                where: {
                  id: id,
                },
              });
              console.log('✅ Fallback feedback saved to database successfully');
            }
          } catch (dbError) {
            console.error('Failed to save fallback feedback:', dbError);
          }
        }

        return NextResponse.json(
          {
            success: true,
            message: 'Feedback generated with fallback due to video processing issues',
            fallback: true,
          },
          { status: 200 }
        );
      }
    }

    // Generate video analysis with timeout handling
    try {
      console.log('Starting Gemini Vision API analysis...');

      // Set a timeout for the video analysis
      const analysisTimeout = 5 * 60 * 1000; // 5 minutes
      const analysisPromise = generateVideoResult({
        systemInput: systemPrompt,
        url: videoPath,
        question,
        bucket,
        isHLSConverted: isHLSVideo
      });

      response = await Promise.race([
        analysisPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Video analysis timed out')), analysisTimeout)
        )
      ]);

      console.log('✅ Gemini Vision API analysis completed successfully');

    } catch (analysisError) {
      console.error('Video analysis failed:', analysisError);

      // Log analysis failure
      Audit.logEvent({
        action: 'tailored_practice_video_analysis_failed',
        userId: userId,
        details: {
          screen: 'tailored practices',
          practiceId: url,
          organizationId,
          error: analysisError.message,
          isHLSConverted: isHLSVideo,
        },
      });

      // Generate fallback feedback for analysis failure
      const fallbackFeedback = createComprehensiveFallbackFeedback(
        question,
        role,
        level,
        analysisError.message.includes('timed out') ? 'timeout_error' : 'system_error'
      );

      // Save fallback feedback to database
      if (id && questionId) {
        try {
          const data = await db.careerPractice.findFirst({
            where: {
              id: id,
              userId: userId,
            },
          });

          const updatedConversation = await updateConversation(
            data,
            fallbackFeedback.transcript,
            questionId,
            url,
            fallbackFeedback,
          );

          if (updatedConversation) {
            await db.careerPractice.update({
              data: {
                conversation: updatedConversation,
              },
              where: {
                id: id,
              },
            });
            console.log('✅ Fallback feedback saved to database after analysis failure');
          }
        } catch (dbError) {
          console.error('Failed to save fallback feedback after analysis failure:', dbError);
        }
      }

      return NextResponse.json(
        {
          success: true,
          message: 'Feedback generated with fallback due to analysis issues',
          fallback: true,
        },
        { status: 200 }
      );
    }

    try {
      // Clean the response text by removing markdown code blocks and other potential formatting issues
      const cleanedText = response?.parts?.[0]?.text
        ?.replaceAll('```json', '')
        ?.replaceAll('```', '');
      const feedback = JSON.parse(cleanedText);

      // Save the feedback to database
      if (id && questionId) {
        const data = await db.careerPractice.findFirst({
          where: {
            id: id,
            userId: userId,
          },
        });

        const updatedConversation = await updateConversation(
          data,
          feedback.transcript || '',
          questionId,
          url,
          feedback,
        );

        if (updatedConversation) {
          await db.careerPractice.update({
            data: {
              conversation: updatedConversation,
            },
            where: {
              id: id,
            },
          });
          console.log('✅ Tailored practice feedback saved to database successfully');
        }
      }

      return NextResponse.json(
        {
          success: true,
          message: 'Video feedback generated and saved successfully',
        },
        { status: 200 },
      );
    } catch (jsonError) {
      console.log('JSON parsing error:', jsonError);
      Audit.logEvent({
        action: 'video_feedback_json_parse_failed',
        userId: userId,
        details: {
          screen: 'tailored practices',
          userId: userId,
          s3Id: url,
          error: jsonError,
          result: response?.parts?.[0]?.text,
        },
      });

      // Attempt to regenerate the response
      console.log('Attempting to regenerate AI response due to invalid JSON format');
      response = await generateVideoResult({ systemInput: systemPrompt, url, question, bucket });

      try {
        // Try parsing the regenerated response
        const cleanedText = response?.parts?.[0]?.text
          ?.replaceAll('```json', '')
          ?.replaceAll('```', '');
        const feedback = JSON.parse(cleanedText);

        // Save the feedback to database
        if (id && questionId) {
          const data = await db.careerPractice.findFirst({
            where: {
              id: id,
              userId: userId,
            },
          });

          const updatedConversation = await updateConversation(
            data,
            feedback.transcript || '',
            questionId,
            url,
            feedback,
          );

          if (updatedConversation) {
            await db.careerPractice.update({
              data: {
                conversation: updatedConversation,
              },
              where: {
                id: id,
              },
            });
            console.log(
              '✅ Tailored practice feedback saved to database successfully (after retry)',
            );
          }
        }

        return NextResponse.json(
          {
            success: true,
            message: 'Video feedback generated and saved successfully',
          },
          { status: 200 },
        );
      } catch (retryError) {
        console.log('Retry JSON parsing error:', retryError);
        return NextResponse.json(
          { error: 'Failed to parse AI response after retry' },
          { status: 500 },
        );
      }
    }
  } catch (error) {
    console.log('tailored video feedback error ', { error });
    Audit.logEvent({
      action: 'video_feedback_generation_failed',
      userId: userId,
      details: {
        screen: 'tailored practices',
        userId: userId,
        s3Id: url,
        error,
        result: response?.parts?.[0]?.text,
      },
    });
    return NextResponse.json({ error: 'tailored video feedback error ' }, { status: 500 });
  } finally {
    // Cleanup converted video file if it was created
    if (convertedVideoPath && convertedVideoPath.includes('converted/')) {
      try {
        // Import cleanup function dynamically to avoid circular imports
        const { HLSToMP4Converter } = await import('@/utils/video-conversion');
        const converter = new HLSToMP4Converter();

        // Extract practice ID from the converted path for cleanup
        const practiceId = url; // Original URL is the practice ID
        converter.cleanupPracticeFiles(practiceId);

        console.log(`✅ Cleaned up converted video files for practice: ${practiceId}`);
      } catch (cleanupError) {
        console.error('Failed to cleanup converted video files:', cleanupError);
      }
    }
  }
}

const generateVideoResult = async ({ systemInput, url, question, bucket, isHLSConverted = false }) => {
  // Use provided bucket or default
  const bucketName = bucket || defaultBucket;

  // For HLS converted videos, the URL already includes the full path
  // For regular videos, use the folder structure
  let videoUri;
  if (isHLSConverted) {
    // URL already contains the full path for converted videos
    videoUri = url.startsWith('gs://') ? url : `gs://${bucketName}/${url}`;
  } else {
    // Regular video path construction
    videoUri = `gs://${bucketName}/${folder}/${url}`;
  }

  console.log(`Processing video for Gemini Vision API: ${videoUri}`);

  const config: VertexAIConfiguration = {
    project,
    model,
    location,
    systemInput,
    credentials: {
      private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
      client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
      client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
    },
    generationConfig: {
      maxOutputTokens: 8192,
      temperature: 0.5,
      topP: 0.5,
    },
  };

  const vertexAI = new GCPVertexAI(config);
  const stream = await vertexAI.generateContent({
    video: {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: videoUri,
      },
    },
    question,
  });

  return stream;
};
