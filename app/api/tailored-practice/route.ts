import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export const maxDuration = 299;

export async function POST(request: any) {
  const token = await getToken({ req: request });
  const { role, level } = await request.json();
  const userId = token?.sub;

  if (!(role && level)) {
    return NextResponse.json(
      {
        message: 'Role is Missing',
      },
      { status: 403 },
    );
  }

  if (
    !token &&
    !['Software Engineer', 'History (Ancient & Medieval)']?.includes(role) &&
    level !== 'Fresher'
  )
    return NextResponse.json(
      {
        message: 'Unauthorized',
      },
      { status: 401 },
    );

  const response = await db.careerPractice.create({
    data: {
      userId: userId || 'Visitor',
      role: role || '',
      level: level || '',
      event: 'tailored-practice',
      conversation: [], // Initialize with empty conversation array
      timing: {
        startTime: new Date().toISOString(),
      },
    },
  });

  return NextResponse.json({
    id: response.id,
    status: 200,
  });
}
