import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import crypto from 'crypto';
import Razorpay from 'razorpay';

const razorpayInstance = new Razorpay({
  key_id: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID ?? ' ',
  key_secret: process.env.NEXT_PUBLIC_RAZORPAY_KEY_SECRET ?? '',
});

const generatedSignature = (razorpay_payment_id: string, razorpay_subscription_id: string) => {
  const keySecret = process.env.NEXT_PUBLIC_RAZORPAY_KEY_SECRET;
  if (!keySecret) {
    throw new Error('Razorpay key secret is not defined in environment variables.');
  }

  const sig = crypto
    .createHmac('sha256', keySecret)
    .update(razorpay_payment_id + '|' + razorpay_subscription_id)
    .digest('hex');
  return sig;
};

export async function POST(request: NextRequest) {
  const { razorpay_subscription_id, razorpay_payment_id, razorpay_signature, email, plan, planId } =
    await request.json();

  const signature = generatedSignature(razorpay_payment_id, razorpay_subscription_id);
  if (signature !== razorpay_signature) {
    return NextResponse.json({ message: 'Payment failed', isOk: false }, { status: 400 });
  }
  const subscriptions: any = await razorpayInstance?.subscriptions.fetch(razorpay_subscription_id);

  const user = await db.user.update({
    data: {
      premium: true,
    },
    where: {
      email: email,
    },
  });
  await db.userSubscription.create({
    data: {
      userId: (user as any).id,
      stripeSubscriptionId: razorpay_subscription_id,
      stripeCustomerId: subscriptions?.customer_id ?? '',
      stripePriceId: subscriptions?.plan_id,
      stripeCurrentPeriodEnd: new Date(subscriptions?.current_end * 1000),
      plan: plan || '',
      status: 'active',
    },
  });
  return NextResponse.json({ message: 'Payment success', isOk: true }, { status: 200 });
}
