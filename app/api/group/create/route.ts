import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { data } = await request.json();
  try {
    const group = await db.group.create({
      data,
    });
    return NextResponse.json(
      {
        group,
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'group create failed' }, { status: 500 });
  }
}
