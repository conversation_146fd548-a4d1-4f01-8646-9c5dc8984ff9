import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(req: NextRequest) {
  const { membershipId, organizationId } = await req.json();

  try {
    const [group] = await Promise.all([
      db.group.findMany({
        where: {
          organizationId,
          GroupMembershipMapping: {
            some: {
              ...(membershipId ? { membershipId } : {}),
              role: {
                in: ['ADMIN', 'MEMBER'],
              },
            },
          },
        },
        include: {
          department: {
            include: {
              DepartmentMembershipMapping: {
                include: {
                  membership: {
                    include: {
                      user: {
                        include: {
                          userProfile: {
                            select: {
                              fullName: true,
                            },
                          },
                        },
                      },
                    },
                  },
                },
                where: {
                  role: {
                    in: ['ADMIN'],
                  },
                },
              },
            },
          },
          GroupMembershipMapping: {
            include: {
              membership: {
                include: {
                  user: {
                    include: {
                      userProfile: {
                        select: {
                          fullName: true,
                        },
                      },
                    },
                  },
                },
              },
            },
            where: {
              role: {
                in: ['ADMIN', 'MEMBER'],
              },
            },
          },
        },
      }),
    ]);
    return NextResponse.json({ group }, { status: 200 });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error }, { status: 500 });
  }
}
