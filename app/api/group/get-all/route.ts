import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(req: NextRequest) {
  const { organizationId, groupId, membershipId } = await req.json();

  if (groupId) {
    try {
      const [group] = await Promise.all([
        db.group.findMany({
          where: {
            id: groupId,
            organizationId,
          },
          include: {
            department: true,
          },
        }),
      ]);

      return NextResponse.json({ group: group }, { status: 200 });
    } catch (error) {
      console.log({ error });
      return NextResponse.json({ error }, { status: 500 });
    }
  }

  if (membershipId) {
    try {
      const membership = await db.membership.findFirst({
        where: { id: membershipId, organizationId },
        include: {
          DepartmentMembershipMapping: {
            include: {
              department: true,
            },
          },
        },
      });

      const isDepAdmin = membership?.DepartmentMembershipMapping.filter(
        (mapping) => mapping.role === 'ADMIN',
      );

      const [group] = await Promise.all([
        db.group.findMany({
          where: {
            OR: [
              {
                GroupMembershipMapping: {
                  some: {
                    membershipId,
                  },
                },
              },
              ...(isDepAdmin
                ? [
                    {
                      departmentId: {
                        in: isDepAdmin.map((mapping) => mapping?.departmentId ?? ''),
                      },
                    },
                  ]
                : []),
            ],
          },
          include: {
            department: true,
          },
        }),
      ]);
      return NextResponse.json({ group: group }, { status: 200 });
    } catch (error) {
      console.log({ error });
      return NextResponse.json({ error }, { status: 500 });
    }
  }

  try {
    const [group] = await Promise.all([
      db.group.findMany({
        where: {
          organizationId,
        },
        include: {
          department: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);
    return NextResponse.json({ group }, { status: 200 });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error }, { status: 500 });
  }
}
