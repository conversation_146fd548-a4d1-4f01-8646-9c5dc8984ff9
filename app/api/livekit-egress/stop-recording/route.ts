import { NextRequest, NextResponse } from 'next/server';
import { EgressClient } from 'livekit-server-sdk';

const API_KEY = process.env.LIVEKIT_API_KEY;
const API_SECRET = process.env.LIVEKIT_API_SECRET;
const LIVEKIT_URL = process.env.LIVEKIT_URL;

if (!LIVEKIT_URL || !API_KEY || !API_SECRET) {
  throw new Error('Missing LiveKit environment variables');
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { egressId } = body;

    if (!egressId) {
      return NextResponse.json(
        { error: 'Egress ID is required' },
        { status: 400 }
      );
    }

    // Initialize the egress client
    const egressClient = new EgressClient(LIVEKIT_URL, API_KEY, API_SECRET);

    console.log('🛑 Stopping LiveKit egress recording:', { egressId });

    // Stop the egress recording
    const egressInfo = await egressClient.stopEgress(egressId);

    console.log('✅ LiveKit egress recording stopped:', {
      egressId: egressInfo.egressId,
      status: egressInfo.status,
      endedAt: egressInfo.endedAt
    });

    return NextResponse.json({
      success: true,
      egressId: egressInfo.egressId,
      status: egressInfo.status,
      endedAt: egressInfo.endedAt,
      message: 'Recording stopped successfully'
    });

  } catch (error) {
    console.error('❌ Failed to stop LiveKit egress recording:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to stop recording',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
