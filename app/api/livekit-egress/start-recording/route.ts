import { NextRequest, NextResponse } from 'next/server';
import { EgressClient, RoomCompositeEgressRequest, EncodingOptionsPreset } from 'livekit-server-sdk';

const API_KEY = process.env.LIVEKIT_API_KEY;
const API_SECRET = process.env.LIVEKIT_API_SECRET;
const LIVEKIT_URL = process.env.LIVEKIT_URL;

if (!LIVEKIT_URL || !API_KEY || !API_SECRET) {
  throw new Error('Missing LiveKit environment variables');
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      roomName, 
      layout = 'speaker',
      width = 1920,
      height = 1080,
      videoCodec = 'H264_MAIN',
      audioBitrate = 128,
      videoBitrate = 4500,
      outputType = 'file' // 'file' or 'stream'
    } = body;

    if (!roomName) {
      return NextResponse.json(
        { error: 'Room name is required' },
        { status: 400 }
      );
    }

    // Initialize the egress client
    const egressClient = new EgressClient(LIVEKIT_URL, API_KEY, API_SECRET);

    // Get the base URL for the custom recording template
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';
    const customBaseUrl = `${baseUrl}/lk-recording-view`;

    console.log('🎬 Starting LiveKit egress recording:', {
      roomName,
      layout,
      customBaseUrl,
      dimensions: `${width}x${height}`
    });

    // Configure the egress request
    const egressRequest: RoomCompositeEgressRequest = {
      roomName,
      layout,
      customBaseUrl,
      
      // Video encoding options
      videoOnly: false,
      audioOnly: false,
      
      // Advanced encoding options
      advanced: {
        width,
        height,
        depth: 24,
        framerate: 30,
        audioCodec: 'OPUS',
        videoCodec,
        keyFrameInterval: 2.0,
      },
      
      // Output configuration
      output: outputType === 'stream' ? {
        case: 'stream',
        value: {
          protocol: 'RTMP',
          urls: [process.env.RTMP_OUTPUT_URL || ''], // Configure your RTMP endpoint
        }
      } : {
        case: 'file',
        value: {
          case: 'gcp',
          value: {
            bucket: process.env.NEXT_PUBLIC_GCP_BUCKET || '',
            filepath: `${process.env.NEXT_PUBLIC_S3FOLDER || 'recordings'}/livekit-egress/${roomName}-${Date.now()}.mp4`,
            credentials: JSON.stringify({
              type: 'service_account',
              project_id: process.env.NEXT_PUBLIC_GCP_PROJECT_ID,
              private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
              client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
              client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
              auth_uri: 'https://accounts.google.com/o/oauth2/auth',
              token_uri: 'https://oauth2.googleapis.com/token',
              auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
            }),
          }
        }
      }
    };

    // Start the egress recording
    const egressInfo = await egressClient.startRoomCompositeEgress(egressRequest);

    console.log('✅ LiveKit egress recording started:', {
      egressId: egressInfo.egressId,
      roomName: egressInfo.roomName,
      status: egressInfo.status
    });

    return NextResponse.json({
      success: true,
      egressId: egressInfo.egressId,
      roomName: egressInfo.roomName,
      status: egressInfo.status,
      customBaseUrl,
      message: 'Recording started successfully'
    });

  } catch (error) {
    console.error('❌ Failed to start LiveKit egress recording:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to start recording',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check egress status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const egressId = searchParams.get('egressId');

    if (!egressId) {
      return NextResponse.json(
        { error: 'Egress ID is required' },
        { status: 400 }
      );
    }

    const egressClient = new EgressClient(LIVEKIT_URL, API_KEY, API_SECRET);
    const egressInfo = await egressClient.listEgress({ egressId });

    return NextResponse.json({
      success: true,
      egress: egressInfo.length > 0 ? egressInfo[0] : null
    });

  } catch (error) {
    console.error('❌ Failed to get egress status:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get recording status',
        details: error.message
      },
      { status: 500 }
    );
  }
}
