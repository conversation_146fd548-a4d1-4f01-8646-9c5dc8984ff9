import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const category = searchParams.get('category');
  const topic = searchParams.get('topic');
  const tags = searchParams.get('tags');
  try {
    const where: any = { isPractices: true };
    if (category && topic) {
      where.category = category;
      where.topic = topic;
    } else if (category && tags) {
      where.category = category;
      where.tags = { has: tags };
    } else if (category) {
      where.category = category;
    } else if (topic) {
      where.topic = topic;
    } else if (tags) {
      where.tags = { has: tags };
    }

    const question = await db.mcqProblem.findMany({ where });
    const finalQuestion = getRandomRecords(question, 10);
    return NextResponse.json(
      {
        result: finalQuestion,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('An error occurred:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
function getRandomRecords(array, numRecords) {
  if (array.length <= numRecords) {
    return array; // Return the array if it has 10 or fewer records
  }

  // Shuffle the array
  const shuffled = array.slice().sort(() => 0.5 - Math.random());

  // Return the first 'numRecords' elements from the shuffled array
  return shuffled.slice(0, numRecords);
}
