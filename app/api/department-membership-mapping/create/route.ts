import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const data = await request.json();

  try {
    const departmentMembershipMapping = await db.departmentMembershipMapping.create({
      data: {
        departmentId: data?.departmentId,
        membershipId: data?.memberId,
        role: data?.role,
      },
    });

    return NextResponse.json(
      {
        departmentMembershipMapping,
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'webhook create failed' }, { status: 500 });
  }
}
