import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  const { departmentId, role = [], organizationId } = await request.json();
  let where = {};
  if (role?.length > 0) {
    where['role'] = {
      in: role,
    };
  }
  if (organizationId) {
    where['department'] = {
      organizationId,
    };
  }
  if (departmentId) {
    where['departmentId'] = departmentId;
  }
  try {
    const [departmentMembershipMapping] = await Promise.all([
      db.departmentMembershipMapping.findMany({
        where: {
          ...where,
        },
        include: {
          membership: {
            include: {
              user: {
                include: {
                  userProfile: true,
                },
              },
            },
          },
          department: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);
    return NextResponse.json({ departmentMembershipMapping }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error }, { status: 200 });
  }
}
