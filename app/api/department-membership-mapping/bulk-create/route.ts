import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { data } = await request.json();
  const { members, departmentId } = data;
  try {
    const memberIds = members?.map((item) => item?.membershipId);
    const oldDepartmentMembershipMapping = await db.departmentMembershipMapping.findMany({
      where: {
        departmentId,
        membershipId: {
          in: memberIds,
        },
      },
    });
    const newMembers = members?.filter(
      (item) =>
        !oldDepartmentMembershipMapping?.some(
          (oldItem) => oldItem?.membershipId === item?.membershipId,
        ),
    );

    const departmentMembershipMapping = await db.departmentMembershipMapping.createMany({
      data: newMembers?.map((item) => ({
        membershipId: item?.membershipId,
        role: item?.role,
        departmentId,
      })),
    });
    return NextResponse.json(
      {
        items: departmentMembershipMapping,
        status: 'success',
      },
      { status: 200 },
    );
  } catch (error) {
    console.log('Error Creating department membership mapping', error);
    return NextResponse.json(
      {
        error: 'Error Creating department membership mapping',
        status: 'error',
      },
      { status: 500 },
    );
  }
}
