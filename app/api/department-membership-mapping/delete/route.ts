import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(req: NextRequest) {
  const url = req.url;
  const queryParams = new URLSearchParams(url.split('?')[1]); // Split the URL and get the query parameters
  const id = queryParams.get('id') ?? '';
  try {
    const departmentMembershipMapping = await db.departmentMembershipMapping.delete({
      where: {
        id,
      },
    });
    return NextResponse.json({ departmentMembershipMapping }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error }, { status: 200 });
  }
}
