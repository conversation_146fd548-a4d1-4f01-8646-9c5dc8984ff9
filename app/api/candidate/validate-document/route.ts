import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { 
  RekognitionClient, 
  DetectTextCommand, 
  DetectFacesCommand,
  TextDetection,
  FaceDetail 
} from '@aws-sdk/client-rekognition';

interface DocumentValidationResult {
  isValid: boolean;
  reason: string;
  details?: {
    textDetections: number;
    faceDetections: number;
    documentFaceDetected: boolean;
    hasKeywords: boolean;
    textCoverage: number;
    confidence: number;
  };
}

// Keywords that commonly appear on ID documents
const ID_KEYWORDS = [
  'passport', 'license', 'identification', 'identity', 'id', 'card',
  'driver', 'driving', 'national', 'government', 'issued', 'official',
  'date of birth', 'dob', 'born', 'expires', 'expiry', 'valid',
  'country', 'state', 'province', 'republic', 'kingdom', 'united',
  'name', 'surname', 'first', 'last', 'address', 'sex', 'gender',
  'height', 'weight', 'eyes', 'hair', 'class', 'restrictions'
];

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { imageData, userId, interviewId } = await request.json();

    if (!imageData || !userId || !interviewId) {
      return NextResponse.json(
        { error: 'Missing required fields: imageData, userId, interviewId' },
        { status: 400 }
      );
    }

    // Initialize AWS Rekognition client
    const rekognitionClient = new RekognitionClient({
      region: process.env.AWS_DEFAULT_REGION || 'ap-south-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
    });

    // Convert base64 image to Uint8Array
    const imageBuffer = new Uint8Array(
      Buffer.from(imageData.replace(/^data:image\/[a-z]+;base64,/, ''), 'base64')
    );

    // Perform text detection and face detection in parallel
    const [textResult, faceResult] = await Promise.all([
      rekognitionClient.send(new DetectTextCommand({
        Image: { Bytes: imageBuffer }
      })),
      rekognitionClient.send(new DetectFacesCommand({
        Image: { Bytes: imageBuffer },
        Attributes: ['ALL']
      }))
    ]);

    // Validate the document
    const validation = validateDocument(textResult.TextDetections || [], faceResult.FaceDetails || []);

    return NextResponse.json({
      success: true,
      validation,
      debug: process.env.NODE_ENV === 'development' ? {
        textDetections: textResult.TextDetections?.length || 0,
        faceDetections: faceResult.FaceDetails?.length || 0,
        detectedTexts: textResult.TextDetections?.map(t => t.DetectedText).slice(0, 10) // First 10 for debugging
      } : undefined
    });

  } catch (error) {
    console.error('Document validation error:', error);

    // Handle specific AWS Rekognition errors
    if (error.name === 'InvalidImageFormatException') {
      return NextResponse.json({
        success: false,
        validation: {
          isValid: false,
          reason: 'Invalid image format. Please ensure the image is in JPEG or PNG format.'
        }
      }, { status: 400 });
    }

    if (error.name === 'ImageTooLargeException') {
      return NextResponse.json({
        success: false,
        validation: {
          isValid: false,
          reason: 'Image too large. Please use a smaller image.'
        }
      }, { status: 400 });
    }

    if (error.name === 'InvalidParameterException') {
      return NextResponse.json({
        success: false,
        validation: {
          isValid: false,
          reason: 'Invalid image data. Please capture a clear photo.'
        }
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      validation: {
        isValid: false,
        reason: 'Technical error during document validation. Please try again.'
      }
    }, { status: 500 });
  }
}

function validateDocument(textDetections: TextDetection[], faceDetails: FaceDetail[]): DocumentValidationResult {
  // Check if we have any text detections
  if (!textDetections || textDetections.length === 0) {
    return {
      isValid: false,
      reason: 'No text detected in the image. Please ensure your ID document is clearly visible and well-lit.'
    };
  }

  // Filter for high-confidence text detections (LINE type for better accuracy)
  const highConfidenceTexts = textDetections.filter(
    detection => 
      detection.Type === 'LINE' && 
      detection.Confidence && 
      detection.Confidence > 80
  );

  // Check minimum text blocks requirement
  if (highConfidenceTexts.length < 3) {
    return {
      isValid: false,
      reason: 'Insufficient text detected. Please ensure your entire ID document is visible and the image is clear.'
    };
  }

  // Check for face detection that appears to be ON the document (not just in camera frame)
  const hasValidDocumentFace = validateDocumentFace(faceDetails, highConfidenceTexts);

  // Calculate text coverage (how much of the image contains text)
  const textCoverage = calculateTextCoverage(highConfidenceTexts);

  // Check for ID-related keywords
  const allDetectedText = textDetections
    .map(detection => detection.DetectedText?.toLowerCase() || '')
    .join(' ');
  
  const hasKeywords = ID_KEYWORDS.some(keyword => 
    allDetectedText.includes(keyword.toLowerCase())
  );

  // Calculate overall confidence score
  const avgTextConfidence = highConfidenceTexts.reduce(
    (sum, detection) => sum + (detection.Confidence || 0), 0
  ) / highConfidenceTexts.length;

  const faceConfidence = hasValidDocumentFace ?
    Math.max(...faceDetails.map(face => face.Confidence || 0)) : 0;

  const overallConfidence = (
    avgTextConfidence * 0.4 + 
    faceConfidence * 0.3 + 
    (hasKeywords ? 100 : 0) * 0.2 + 
    textCoverage * 0.1
  );

  // Validation logic - more intelligent scoring
  let isValid = true;
  let reason = 'Document appears to be valid.';

  // Primary validation: Must have face photo ON the document
  if (!hasValidDocumentFace) {
    isValid = false;
    reason = 'No clear face photo detected on the document. Please ensure your ID document includes a visible photo.';
  }
  // Secondary validation: Must have reasonable text quality
  else if (avgTextConfidence < 80) {
    isValid = false;
    reason = 'Text quality too low. Please ensure good lighting and focus when capturing your ID.';
  }
  // Tertiary validation: Must have sufficient text OR keywords
  else if (highConfidenceTexts.length < 3) {
    isValid = false;
    reason = 'Insufficient text detected. Please ensure your entire ID document is visible and the image is clear.';
  }
  // Quaternary validation: If low text count, must have keywords
  else if (highConfidenceTexts.length < 5 && !hasKeywords && textCoverage < 2) {
    isValid = false;
    reason = 'This does not appear to be a valid ID document. Please use a government-issued ID.';
  }

  return {
    isValid,
    reason,
    details: {
      textDetections: highConfidenceTexts.length,
      faceDetections: faceDetails.length,
      documentFaceDetected: hasValidDocumentFace,
      hasKeywords,
      textCoverage: Math.round(textCoverage),
      confidence: Math.round(overallConfidence)
    }
  };
}

function calculateTextCoverage(textDetections: TextDetection[]): number {
  if (!textDetections || textDetections.length === 0) return 0;

  // Calculate the total area covered by text bounding boxes
  let totalTextArea = 0;

  textDetections.forEach(detection => {
    if (detection.Geometry?.BoundingBox) {
      const box = detection.Geometry.BoundingBox;
      const area = (box.Width || 0) * (box.Height || 0);
      totalTextArea += area;
    }
  });

  // Convert to percentage (bounding box coordinates are normalized 0-1)
  return totalTextArea * 100;
}

function validateDocumentFace(faceDetails: FaceDetail[], textDetections: TextDetection[]): boolean {
  if (!faceDetails || faceDetails.length === 0) return false;

  // Filter for high-confidence faces
  const highConfidenceFaces = faceDetails.filter(face =>
    face.Confidence && face.Confidence > 85
  );

  if (highConfidenceFaces.length === 0) return false;

  // If no text detected, we can't determine if face is on document
  if (!textDetections || textDetections.length === 0) return false;

  // Calculate text bounding area to determine document region
  const textBounds = calculateDocumentBounds(textDetections);
  if (!textBounds) return false;

  // Check if any face is within or overlapping the document text region
  for (const face of highConfidenceFaces) {
    if (face.BoundingBox && isFaceOnDocument(face.BoundingBox, textBounds)) {
      return true;
    }
  }

  return false;
}

function calculateDocumentBounds(textDetections: TextDetection[]) {
  if (!textDetections || textDetections.length === 0) return null;

  let minLeft = 1, minTop = 1, maxRight = 0, maxBottom = 0;

  textDetections.forEach(detection => {
    if (detection.Geometry?.BoundingBox) {
      const box = detection.Geometry.BoundingBox;
      const left = box.Left || 0;
      const top = box.Top || 0;
      const right = left + (box.Width || 0);
      const bottom = top + (box.Height || 0);

      minLeft = Math.min(minLeft, left);
      minTop = Math.min(minTop, top);
      maxRight = Math.max(maxRight, right);
      maxBottom = Math.max(maxBottom, bottom);
    }
  });

  // Add some padding to account for document margins
  const padding = 0.05; // 5% padding
  return {
    left: Math.max(0, minLeft - padding),
    top: Math.max(0, minTop - padding),
    right: Math.min(1, maxRight + padding),
    bottom: Math.min(1, maxBottom + padding),
    width: maxRight - minLeft + (2 * padding),
    height: maxBottom - minTop + (2 * padding)
  };
}

function isFaceOnDocument(faceBoundingBox: any, documentBounds: any): boolean {
  const faceLeft = faceBoundingBox.Left || 0;
  const faceTop = faceBoundingBox.Top || 0;
  const faceRight = faceLeft + (faceBoundingBox.Width || 0);
  const faceBottom = faceTop + (faceBoundingBox.Height || 0);

  // Calculate overlap area
  const overlapLeft = Math.max(faceLeft, documentBounds.left);
  const overlapTop = Math.max(faceTop, documentBounds.top);
  const overlapRight = Math.min(faceRight, documentBounds.right);
  const overlapBottom = Math.min(faceBottom, documentBounds.bottom);

  // Check if there's any overlap
  if (overlapLeft >= overlapRight || overlapTop >= overlapBottom) {
    return false; // No overlap
  }

  // Calculate overlap percentage relative to face size
  const faceArea = (faceBoundingBox.Width || 0) * (faceBoundingBox.Height || 0);
  const overlapArea = (overlapRight - overlapLeft) * (overlapBottom - overlapTop);
  const overlapPercentage = faceArea > 0 ? overlapArea / faceArea : 0;

  // Face should have significant overlap with document area (at least 30%)
  return overlapPercentage > 0.3;
}
