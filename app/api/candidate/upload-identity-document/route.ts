import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const token = await getToken({ req: request });
    if (!token?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { imageData, documentType, userId, interviewId } = await request.json();

    if (!imageData || !documentType || !userId || !interviewId) {
      return NextResponse.json(
        { error: 'Missing required fields: imageData, documentType, userId, interviewId' },
        { status: 400 }
      );
    }

    // Validate document type
    if (!['id-document', 'face-photo'].includes(documentType)) {
      return NextResponse.json(
        { error: 'Invalid document type. Must be "id-document" or "face-photo"' },
        { status: 400 }
      );
    }

    // Initialize S3 Client
    const s3Client = new S3Client({
      region: process.env.AWS_DEFAULT_REGION || 'ap-south-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
    });

    const bucketName = process.env.NEXT_PUBLIC_S3BUCKET ?? 'aceprep-bucket';

    // Generate predictable filename using userId and interviewId
    const fileExtension = 'jpg';
    const fileName = `${process.env.NEXT_PUBLIC_S3FOLDER}/identity-verification/${documentType}s/${userId}-${interviewId}.${fileExtension}`;

    // Convert base64 to buffer
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // Upload to S3
    const putCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: fileName,
      Body: imageBuffer,
      ContentType: 'image/jpeg',
      Metadata: {
        userId: userId,
        interviewId: interviewId,
        documentType: documentType,
        uploadedAt: new Date().toISOString(),
      },
    });

    await s3Client.send(putCommand);

    // Generate public URL
    const publicUrl = `${process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL}/${fileName}`;

    // If this is a face photo, update the user's profile image
    if (documentType === 'face-photo') {
      // First check if user profile exists
      const existingProfile = await db.userProfile.findUnique({
        where: { userId: userId },
      });

      if (existingProfile) {
        // Update existing profile
        await db.userProfile.update({
          where: { userId: userId },
          data: {
            updatedAt: new Date(),
          },
        });
      } else {
        // Create new profile with just the userId and updatedAt
        await db.userProfile.create({
          data: {
            userId: userId,
            fullName: '', // Will be updated by the name form
            updatedAt: new Date(),
          },
        });
      }
    }

    return NextResponse.json({
      success: true,
      fileUrl: publicUrl,
      fileName: fileName,
      documentType: documentType,
    });

  } catch (error) {
    console.error('Error uploading identity document:', error);
    return NextResponse.json(
      { error: 'Failed to upload document' },
      { status: 500 }
    );
  }
}
