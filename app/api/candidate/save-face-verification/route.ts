import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { 
      userId, 
      interviewId, 
      verificationScore, 
      verificationStatus, 
      faceMatches, 
      unmatched,
      verificationMessage 
    } = await request.json();

    if (!userId || !interviewId || verificationScore === undefined || !verificationStatus) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, interviewId, verificationScore, verificationStatus' },
        { status: 400 }
      );
    }

    // Save face verification results to the CareerPractice record
    const updatedCareerPractice = await db.careerPractice.update({
      where: { id: interviewId },
      data: {
        faceVerificationScore: verificationScore,
        faceVerificationStatus: verificationStatus,
        faceVerificationData: {
          score: verificationScore,
          status: verificationStatus,
          faceMatches: faceMatches || 0,
          unmatched: unmatched || 0,
          message: verificationMessage || '',
          verifiedAt: new Date().toISOString(),
        },
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Face verification results saved successfully',
      verificationScore: verificationScore,
      verificationStatus: verificationStatus,
    });

  } catch (error) {
    console.error('Error saving face verification results:', error);
    return NextResponse.json(
      { error: 'Failed to save face verification results' },
      { status: 500 }
    );
  }
}
