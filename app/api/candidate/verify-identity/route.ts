import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { RekognitionClient, CompareFacesCommand } from '@aws-sdk/client-rekognition';

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { facePhoto, idDocument } = await request.json();

    if (!facePhoto || !idDocument) {
      return NextResponse.json(
        { error: 'Missing required images for verification' },
        { status: 400 }
      );
    }

    // Initialize AWS Rekognition client
    const rekognitionClient = new RekognitionClient({
      region: process.env.AWS_DEFAULT_REGION || 'ap-south-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
    });

    // Convert base64 images to Uint8Array
    const faceImageBuffer = new Uint8Array(
      Buffer.from(facePhoto.replace(/^data:image\/[a-z]+;base64,/, ''), 'base64')
    );
    const idImageBuffer = new Uint8Array(
      Buffer.from(idDocument.replace(/^data:image\/[a-z]+;base64,/, ''), 'base64')
    );

    // Use AWS Rekognition to compare faces
    const compareFacesCommand = new CompareFacesCommand({
      SourceImage: {
        Bytes: idImageBuffer,
      },
      TargetImage: {
        Bytes: faceImageBuffer,
      },
      SimilarityThreshold: 10, // 60% similarity threshold (reduced for more lenient verification)
    });

    const rekognitionResult = await rekognitionClient.send(compareFacesCommand);

    // Check if faces match
    const faceMatches = rekognitionResult.FaceMatches || [];
    const isMatch = faceMatches.length > 0;
    const confidence = isMatch ? faceMatches[0].Similarity || 0 : 0;

    return NextResponse.json({
      success: isMatch,
      confidence: Math.round(confidence * 100) / 100, // Round to 2 decimal places
      message: isMatch
        ? `Face verification successful with ${confidence.toFixed(1)}% confidence`
        : 'Face verification failed - faces do not match',
      faceMatches: faceMatches.length,
      unmatched: rekognitionResult.UnmatchedFaces?.length || 0,
    });
  } catch (error) {
    console.error('Face verification error:', error);

    // Handle specific AWS Rekognition errors
    if (error.name === 'InvalidImageFormatException') {
      return NextResponse.json(
        { error: 'Invalid image format. Please ensure images are in JPEG or PNG format.' },
        { status: 400 }
      );
    }

    if (error.name === 'ImageTooLargeException') {
      return NextResponse.json(
        { error: 'Image too large. Please use smaller images.' },
        { status: 400 }
      );
    }

    if (error.name === 'InvalidParameterException') {
      return NextResponse.json(
        { error: 'Invalid image data. Please capture clear photos.' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Face verification failed', details: error.message },
      { status: 500 }
    );
  }
}



