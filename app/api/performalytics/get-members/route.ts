import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(req: NextRequest) {
  const { organizationId, role, searchParams } = await req.json();
  const { department, group } = searchParams;
  const where: any = {
    organizationId,
    role: { in: role },
  };
  if (department) {
    where.DepartmentMembershipMapping = {
      some: {
        departmentId: department,
        role: { in: role },
      },
    };
  }
  if (group) {
    where.GroupMembershipMapping = {
      some: {
        groupId: group,
        role: { in: role },
      },
    };
  }
  const memberDetails = await db.membership.findMany({
    where,
    select: {
      id: true,
      role: true,
      userId: true,
      user: {
        select: {
          email: true,
          userProfile: true,
          name: true,
        },
      },
      DepartmentMembershipMapping: {
        select: {
          departmentId: true,
          department: {
            select: {
              name: true,
            },
          },
        },
      },
      GroupMembershipMapping: {
        select: {
          groupId: true,
          group: {
            select: {
              name: true,
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
  return NextResponse.json({ items: memberDetails }, { status: 200 });
}
