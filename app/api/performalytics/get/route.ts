import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(req: NextRequest) {
  const { organizationId, searchParams } = await req.json();
  const department = searchParams?.department;
  const group = searchParams?.group;

  try {
    const careerPractices = await db.careerPractice.findMany({
      where: {
        eventDetails: {
          organizationId,
        },
        interviewStatus: 'COMPLETED',
        user: {
          memberships: {
            some: {
              organizationId,
              ...(department && {
                DepartmentMembershipMapping: {
                  some: { departmentId: department },
                },
              }),
              ...(group && {
                GroupMembershipMapping: {
                  some: { groupId: group },
                },
              }),
            },
          },
        },
      },
      select: {
        feedback: true,
      },
    });

    const aggregatedScores = calculateAggregatedScores(careerPractices);

    return NextResponse.json({ aggregatedScores, total: careerPractices.length }, { status: 200 });
  } catch (error) {
    console.error('Error in performalytics GET:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

function calculateAggregatedScores(careerPractices: any[]) {
  const numericFields = [
    'impact',
    'clarity',
    'passion',
    'confidence',
    'communication',
    'language_proficiency',
    'code_quality',
    'problem_solving',
    'efficiency',
    'complexity',
    'frontend_choice',
    'readability',
    'algorithm_choice',
  ];

  const totals: Record<string, number> = {};
  const count: Record<string, number> = {};

  careerPractices.forEach((practice) => {
    const feedback = practice?.feedback;
    numericFields?.forEach((field) => {
      totals[field] = (totals?.[field] || 0) + (feedback?.[field] || 0);
      count[field] = feedback?.[field] ? (count?.[field] || 0) + 1 : count?.[field] || 0;
    });
  });

  const result: Record<string, number> = {};
  numericFields.forEach((field) => {
    result[field] = count[field] > 0 ? Math.round(totals?.[field] / count[field]) : 0;
  });
  return result;
}
