import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(req: NextRequest) {
  const { organizationId, role, searchParams } = await req.json();
  const { department, group } = searchParams;
  const where: any = {
    group: {
      organizationId,
    },
  };
  if (department) {
    where.group.departmentId = department;
  }
  if (group) {
    where.group.id = group;
  }

  const groupStudentCount = await db.groupMembershipMapping.groupBy({
    by: ['groupId'],
    where: {
      role: 'STUDENT',
      ...where,
    },
    _count: {
      _all: true,
    },
  });

  const groupStudentCountWithDetails = await Promise.all(
    groupStudentCount.map(async (item) => {
      const group = await db.group.findUnique({
        where: { id: item.groupId },
        select: {
          name: true,
          department: {
            select: {
              name: true,
            },
          },
        },
      });

      return {
        groupId: item.groupId,
        groupName: group?.name,
        departmentName: group?.department?.name,
        studentCount: item._count._all,
        group: group,
      };
    }),
  );

  return NextResponse.json({ items: groupStudentCountWithDetails }, { status: 200 });
}
