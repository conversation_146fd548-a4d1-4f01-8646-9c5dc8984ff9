import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    const studentCounts = await db.membership.count({
      where: {
        organizationId: organizationId ?? '',
        role: 'STUDENT',
      },
    });

    return NextResponse.json({ studentCounts }, { status: 200 });
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'Unable to fetch student Counts',
      },
      { status: 500 },
    );
  }
}
