import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getToken } from 'next-auth/jwt';

export async function POST(request: any) {
  const { organizationId, emails } = await request.json();

  // if (!token?.email) {
  //   return NextResponse.json({
  //     message: 'Not Authorized to make this call',
  //     status: 400,
  //   })
  // }
  const where: any = {
    user: {
      memberships: { some: { role: 'STUDENT', organizationId } },
    },
  };
  let field = 'createdAt',
    order = 'desc';

  if (emails) {
    where.user = {
      memberships: { some: { role: 'STUDENT', organizationId } },
      email: {
        in: emails,
      },
    };
  }

  try {
    const [student] = await Promise.all([
      db.userProfile.findMany({
        where,
        select: {
          userId: true,
          fullName: true,
          user: {
            include: {
              memberships: {
                select: {
                  id: true,
                },
                where: { role: 'STUDENT', organizationId },
              },
            },
          },
        },
      }),
    ]);

    return NextResponse.json(
      {
        student,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'student Not Found' }, { status: 500 });
  }
}
