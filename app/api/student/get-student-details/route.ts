import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { organizationId, searchParams } = await request.json();

  const { page = 1, department, group, email, per_page = 50, sort, memberShipId } = searchParams;

  const where: any = {};
  let field = 'createdAt',
    order = 'desc';

  if (sort) {
    [field, order] = sort?.split('.');
  }
  let adminDepartments: any = [];
  let memberGroups: any = [];

  if (memberShipId) {
    const memberShip: any = await db.membership.findUnique({
      where: {
        id: memberShipId,
      },
      include: {
        DepartmentMembershipMapping: {
          include: {
            department: true,
          },
        },
        GroupMembershipMapping: {
          include: {
            group: true,
          },
        },
      },
    });

    adminDepartments = memberShip.DepartmentMembershipMapping.filter((m) => m.role === 'ADMIN').map(
      (m) => m.department.id,
    );
    memberGroups = memberShip.GroupMembershipMapping.map((m) => m.group.id);
  }

  where.user = {
    memberships: {
      some: {
        role: 'STUDENT',
        organizationId,
        ...(memberShipId &&
          !department &&
          !group && {
            OR: [
              {
                DepartmentMembershipMapping: {
                  some: {
                    department: {
                      id: {
                        in: [...adminDepartments],
                      },
                    },
                  },
                },
              },
              {
                GroupMembershipMapping: {
                  some: {
                    group: {
                      id: {
                        in: memberGroups,
                      },
                    },
                  },
                },
              },
            ],
          }),
        ...(department && {
          DepartmentMembershipMapping: {
            some: {
              department: {
                id: {
                  in: [department],
                },
              },
            },
          },
          ...(!adminDepartments.includes(department) &&
            memberShipId && {
              GroupMembershipMapping: {
                some: {
                  group: {
                    id: {
                      in: memberGroups,
                    },
                  },
                },
              },
            }),
        }),
        ...(group && {
          GroupMembershipMapping: {
            some: {
              group: {
                id: {
                  in: [group],
                },
              },
            },
          },
        }),
        ...(email && {
          user: {
            email: {
              contains: email,
              mode: 'insensitive',
            },
          },
        }),
      },
    },
  };

  try {
    const [student, count] = await Promise.all([
      db.userProfile.findMany({
        where,
        select: {
          fullName: true,
          userId: true,
          degree: true,
          user: {
            include: {
              memberships: {
                include: {
                  DepartmentMembershipMapping: {
                    select: {
                      department: {
                        select: {
                          name: true,
                          id: true,
                        },
                      },
                    },
                  },
                  GroupMembershipMapping: {
                    select: {
                      group: {
                        select: {
                          name: true,
                          id: true,
                        },
                      },
                    },
                  },
                },
                where: {
                  role: 'STUDENT',
                  organizationId,
                },
              },
            },
          },
        },
        skip: Math.max(Number(page ?? 1) - 1, 0) * Number(per_page ?? 50),
        take: Number(per_page ?? 50),
        orderBy: {
          [field]: order,
        },
      }),
      db.userProfile.count({
        where,
      }),
    ]);

    const userIds = student?.map((item) => item.userId);
    const userIdsString = userIds?.map((s) => `'${s}'`).join(',');
    const query = `
    WITH BestScores AS (
    SELECT
        l."userId",
        l."organizationId",
        l."eventId",
        l."eventType",
        MAX(l.score) AS best_score
    FROM
        leaderboard l
    WHERE
        l."organizationId" IS NOT NULL
        AND l.score IS NOT NULL
        AND l."eventType" IN ('CODING_PRACTICE', 'FRONTEND_PRACTICE', 'MOCK_INTERVIEW', 'CAREER_PRACTICE', 'TAILORED_PRACTICE','INTERVIEWATHON') 
    GROUP BY
        l."userId",
        l."organizationId",
        l."eventId",
        l."eventType"
),
AggregatedScores AS (
    SELECT
        b."userId",
        b."organizationId",
        SUM(b.best_score) AS total_score,
        AVG(b.best_score) AS avg_score,
        SUM(CASE 
            WHEN b."eventType" IN ('CODING_PRACTICE', 'FRONTEND_PRACTICE', 'MOCK_INTERVIEW', 'CAREER_PRACTICE', 'TAILORED_PRACTICE') 
            THEN 1 
            ELSE 0 
        END) AS total_practices,
        COUNT(DISTINCT CASE 
            WHEN b."eventType" = 'INTERVIEWATHON' 
            THEN b."eventId" 
            ELSE NULL 
        END) AS unique_interviewathon
    FROM
        BestScores b
    WHERE
        b."organizationId" = '${organizationId}'
    GROUP BY
        b."userId",
        b."organizationId"
),
RankedScores AS (
    SELECT
        a."userId",
        a."organizationId",
        a.total_score AS "totalFinalScore",
        a.avg_score AS "averageScore",
        a.total_practices,
        a.unique_interviewathon,
        DENSE_RANK() OVER (ORDER BY a.total_score DESC, a.avg_score DESC) AS rank
    FROM
        AggregatedScores a
)
SELECT
    r."userId",
    r."totalFinalScore",
    r."averageScore",
    r.total_practices,
    r.unique_interviewathon,
    r.rank
FROM
    RankedScores r
WHERE
    r."userId" IN (${userIdsString})
ORDER BY
    r."totalFinalScore" DESC, r."averageScore" DESC;`;
    let leaderboardData: any = [];
    try {
      if (userIds?.length > 0) leaderboardData = await db.$queryRawUnsafe(query);
    } catch (error) {
      console.log(error);
      return NextResponse.json({ error: 'student Not Found' }, { status: 500 });
    }

    const studentData = student?.map((item) => {
      const currentUser = leaderboardData?.find((practice) => practice.userId === item.userId);

      return {
        ...item,
        practice: Number(currentUser?.total_practices) ?? 0,
        interviewathon: Number(currentUser?.unique_interviewathon) ?? 0,
        totalFinalScore: Number(currentUser?.totalFinalScore) ?? 0,
        averageScore: Number(currentUser?.averageScore) ?? 0,
        rank: Number(currentUser?.rank) ?? 0,
      };
    });

    return NextResponse.json(
      {
        student: studentData,
        totalPages: Math.ceil((count ?? 0) / Number(per_page)),
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'student Not Found' }, { status: 500 });
  }
}
