import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    const eventCounts = await db.leaderboard.groupBy({
      by: ['userId'],
      where: {
        organizationId: organizationId,
        eventType: {
          in: [
            'CODING_PRACTICE',
            'FRONTEND_PRACTICE',
            'MOCK_INTERVIEW',
            'CAREER_PRACTICE',
            'TAILORED_PRACTICE',
          ],
        },
      },
      _count: {
        _all: true,
      },
    });

    const totalPractices = eventCounts.map((eventCount) => ({
      userId: eventCount.userId,
      event_count: eventCount._count._all,
    }));

    return NextResponse.json({ totalPractices }, { status: 200 });
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'Unable to fetch leadership data',
      },
      { status: 500 },
    );
  }
}
