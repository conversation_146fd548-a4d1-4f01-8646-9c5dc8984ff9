import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') ?? '';
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const [practices, interviewathon, graph] = await Promise.all([
      db.leaderboard.groupBy({
        by: ['userId'],
        where: {
          userId: userId,
          eventType: {
            in: [
              'CODING_PRACTICE',
              'FRONTEND_PRACTICE',
              'MOCK_INTERVIEW',
              'CAREER_PRACTICE',
              'TAILORED_PRACTICE',
              'COMPETITIVE_INTERVIEW',
            ],
          },
        },
        _count: {
          _all: true,
        },
      }),
      db.leaderboard.groupBy({
        by: ['userId'],
        where: {
          userId: userId,
          eventType: {
            in: ['INTERVIEWATHON'],
          },
        },
        _count: {
          _all: true,
        },
      }),
      db.leaderboard.groupBy({
        by: ['createdAt'],
        where: {
          userId: userId,
          createdAt: {
            gte: sixMonthsAgo,
          },
        },
        _count: {
          _all: true,
        },
      }),
    ]);

    const activities = aggregateActivitiesByDate(graph);

    return NextResponse.json(
      {
        practices_count: (practices as any)?.[0]?._count._all,
        interviewathon_count: (interviewathon as any)?.[0]?._count._all,
        activities,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'Unable to fetch leadership data',
      },
      { status: 500 },
    );
  }
}

const aggregateActivitiesByDate = (activities) => {
  const data = activities.reduce((acc, activity) => {
    const date = new Date(activity?.createdAt).toISOString().split('T')[0];
    if (!acc[date]) {
      acc[date] = 0;
    }
    acc[date] += activity?._count._all;
    return acc;
  }, {});
  return Object.entries(data ?? {})?.map(([key, value]) => ({
    created_date: key,
    activity_count: value,
  }));
};
