import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  try {
    const { searchParams, organizationId, userId } = await request.json();

    const { page = 1, per_page = 50, department, group, memberShipId } = searchParams;

    const skip = Math.max(Number(page) - 1, 0) * Number(per_page);
    const take = Number(per_page);
    const baseQuery = !userId
      ? `WITH BestScores AS (
    SELECT
        l."userId",
        l."organizationId",
        l."eventId",
        l."eventType",
        MAX(l.score) AS best_score
    FROM
        leaderboard l
    WHERE
        l."organizationId" IS NOT NULL
        AND l.score IS NOT NULL
        AND l."eventType" IN ('CODING_PRACTICE', 'FRONTEND_PRACTICE', 'MOCK_INTERVIEW', 'CAREER_PRACTICE', 'TAILORED_PRACTICE', 'INTERVIEWATHON') 
    GROUP BY
        l."userId",
        l."organizationId",
        l."eventId",
        l."eventType"
),
AggregatedScores AS (
    SELECT
        b."userId",
        b."organizationId",
        SUM(b.best_score) AS total_score,
        AVG(b.best_score) AS avg_score,
        COUNT(*) AS record_count,
        COUNT(DISTINCT b."eventId") AS unique_record_count,
        SUM(CASE 
            WHEN b."eventType" IN ('CODING_PRACTICE', 'FRONTEND_PRACTICE', 'MOCK_INTERVIEW', 'CAREER_PRACTICE', 'TAILORED_PRACTICE') 
            THEN 1 
            ELSE 0 
        END) AS total_practices,
        COUNT(DISTINCT CASE 
            WHEN b."eventType" = 'INTERVIEWATHON' 
            THEN b."eventId" 
            ELSE NULL 
        END) AS unique_interviewathon
    FROM
        BestScores b
    WHERE
        b."organizationId" = $1
    GROUP BY
        b."userId",
        b."organizationId"
),
UserRoles AS (
    SELECT
        m."userId",
        m."organizationId",
        m."id" AS "membershipId",
        d."id" AS "departmentId",
        d."name" AS "departmentName",
        g."id" AS "groupId",
        g."name" AS "groupName",
        CASE 
            WHEN dmm.role = 'ADMIN' THEN 'ADMIN'
            ELSE 'MEMBER'
        END AS department_role,
        CASE 
            WHEN gmm.role = 'ADMIN' THEN 'ADMIN'
            ELSE 'MEMBER'
        END AS group_role
    FROM
        membership m
    LEFT JOIN
        department_membership_mapping dmm ON m."id" = dmm."membershipId"
    LEFT JOIN
        department d ON dmm."departmentId" = d."id"
    LEFT JOIN
        group_membership_mapping gmm ON m."id" = gmm."membershipId"
    LEFT JOIN
        "group" g ON gmm."groupId" = g."id"
    WHERE
        m."id" = $4 OR $4 IS NULL
),
FilteredAccess AS (
    SELECT DISTINCT
        a."userId",
        a."organizationId",
        a.total_score AS "totalFinalScore",
        a.avg_score AS "averageScore",
        a.total_practices,
        a.unique_interviewathon,
        up."fullName",
        COALESCE(d."name", ur."departmentName") AS "department",
        COALESCE(g."name", ur."groupName") AS "group"
    FROM
        AggregatedScores a
    JOIN
        "user_profile" up ON a."userId" = up."userId"
    JOIN
        membership m ON a."userId" = m."userId" AND a."organizationId" = m."organizationId"
    LEFT JOIN
        department_membership_mapping dmm ON m."id" = dmm."membershipId"
    LEFT JOIN
        department d ON dmm."departmentId" = d."id"
    LEFT JOIN
        group_membership_mapping gmm ON m."id" = gmm."membershipId"
    LEFT JOIN
        "group" g ON gmm."groupId" = g."id"
    LEFT JOIN
        UserRoles ur ON a."organizationId" = ur."organizationId"
    WHERE
        ($4 IS NULL OR (
            (ur.department_role = 'ADMIN' AND d."id" = ur."departmentId")
            OR (ur.department_role = 'MEMBER' AND ur.group_role = 'ADMIN' AND g."id" = ur."groupId")
            OR (ur.department_role = 'MEMBER' AND ur.group_role = 'MEMBER' AND g."id" = ur."groupId")
        ))
        AND ($5::text IS NULL OR d."id" = $5::text)
        AND ($6::text IS NULL OR g."id" = $6::text)
)
SELECT
    f."userId",
    f."organizationId",
    f."totalFinalScore",
    f."averageScore",
    f.total_practices,
    f.unique_interviewathon,
    f."fullName",
    f."department",
    f."group",
    DENSE_RANK() OVER (ORDER BY f."totalFinalScore" DESC, f."averageScore" DESC) AS rank
FROM
    FilteredAccess f
ORDER BY
    f."totalFinalScore" DESC,
    f."averageScore" DESC
OFFSET $2 LIMIT $3;
`
      : `
WITH BestScores AS (
    SELECT
        l."userId",
        l."organizationId",
        l."eventId",
        l."eventType",
        MAX(l.score) AS best_score
    FROM
        leaderboard l
    WHERE
        l."organizationId" IS NOT NULL
        AND l.score IS NOT NULL
        AND l."eventType" IN ('CODING_PRACTICE', 'FRONTEND_PRACTICE', 'MOCK_INTERVIEW', 'CAREER_PRACTICE', 'TAILORED_PRACTICE', 'INTERVIEWATHON')
    GROUP BY
        l."userId",
        l."organizationId",
        l."eventId",
        l."eventType"
),
AggregatedScores AS (
    SELECT
        b."userId",
        b."organizationId",
        SUM(b.best_score) AS total_score,
        AVG(b.best_score) AS avg_score,
        SUM(CASE 
            WHEN b."eventType" IN ('CODING_PRACTICE', 'FRONTEND_PRACTICE', 'MOCK_INTERVIEW', 'CAREER_PRACTICE', 'TAILORED_PRACTICE') 
            THEN 1 
            ELSE 0 
        END) AS total_practices,
        COUNT(DISTINCT CASE 
            WHEN b."eventType" = 'INTERVIEWATHON' 
            THEN b."eventId" 
            ELSE NULL 
        END) AS unique_interviewathon
    FROM
        BestScores b
    WHERE
        b."organizationId" = $1
    GROUP BY
        b."userId",
        b."organizationId"
),
UserRoles AS (
    SELECT
        m."userId",
        m."organizationId",
        m."id" AS "membershipId",
        d."id" AS "departmentId",
        d."name" AS "departmentName",
        g."id" AS "groupId",
        g."name" AS "groupName",
        CASE 
            WHEN dmm.role = 'ADMIN' THEN 'ADMIN'
            ELSE 'MEMBER'
        END AS department_role,
        CASE 
            WHEN gmm.role = 'ADMIN' THEN 'ADMIN'
            ELSE 'MEMBER'
        END AS group_role
    FROM
        membership m
    LEFT JOIN
        department_membership_mapping dmm ON m."id" = dmm."membershipId"
    LEFT JOIN
        department d ON dmm."departmentId" = d."id"
    LEFT JOIN
        group_membership_mapping gmm ON m."id" = gmm."membershipId"
    LEFT JOIN
        "group" g ON gmm."groupId" = g."id"
    WHERE
        m."userId" = $4
),
FilteredAccess AS (
    SELECT DISTINCT
        a."userId",
        a."organizationId",
        a.total_score AS "totalFinalScore",
        a.avg_score AS "averageScore",
        a.total_practices,
        a.unique_interviewathon,
        up."fullName",
        COALESCE(d."name", ur."departmentName") AS "department",
        COALESCE(g."name", ur."groupName") AS "group",
        ur.department_role,
        ur.group_role
    FROM
        AggregatedScores a
    JOIN
        "user_profile" up ON a."userId" = up."userId"
    JOIN
        membership m ON a."userId" = m."userId" AND a."organizationId" = m."organizationId"
    LEFT JOIN
        department_membership_mapping dmm ON m."id" = dmm."membershipId"
    LEFT JOIN
        department d ON dmm."departmentId" = d."id"
    LEFT JOIN
        group_membership_mapping gmm ON m."id" = gmm."membershipId"
    LEFT JOIN
        "group" g ON gmm."groupId" = g."id"
    JOIN
        UserRoles ur ON a."userId" = ur."userId"
    WHERE
        a."userId" = $4
),
RankedScores AS (
    SELECT
        a."userId",
        a."organizationId",
        a.total_score AS "totalFinalScore",
        a.avg_score AS "averageScore",
        a.total_practices,
        a.unique_interviewathon,
        DENSE_RANK() OVER (ORDER BY a.total_score DESC, a.avg_score DESC) AS rank
    FROM
        AggregatedScores a
)
SELECT
    f."userId",
    f."organizationId",
    f."totalFinalScore",
    f."averageScore",
    f.total_practices,
    f.unique_interviewathon,
    f."fullName",
    f."department",
    f."group",
    f.department_role,
    f.group_role,
    r.rank
FROM
    FilteredAccess f
JOIN
    RankedScores r ON f."userId" = r."userId"
WHERE
    f."userId" = $4
    ${department ? `AND  d."id" = $5` : ''}
    ${group ? `AND g."id" = $6` : ''}

`;

    const countQuery = `
   WITH BestScores AS (
    SELECT
        l."userId",
        l."organizationId",
        l."eventId",
        l."eventType",
        MAX(l.score) AS best_score
    FROM
        leaderboard l
    WHERE
        l."organizationId" IS NOT NULL
        AND l.score IS NOT NULL
        AND l."eventType" IN ('CODING_PRACTICE', 'FRONTEND_PRACTICE', 'MOCK_INTERVIEW', 'CAREER_PRACTICE', 'TAILORED_PRACTICE', 'INTERVIEWATHON') 
    GROUP BY
        l."userId",
        l."organizationId",
        l."eventId",
        l."eventType"
),
AggregatedScores AS (
    SELECT
        b."userId",
        b."organizationId",
        SUM(b.best_score) AS total_score,
        AVG(b.best_score) AS avg_score,
        COUNT(*) AS record_count,
        COUNT(DISTINCT b."eventId") AS unique_record_count,
        SUM(CASE 
            WHEN b."eventType" IN ('CODING_PRACTICE', 'FRONTEND_PRACTICE', 'MOCK_INTERVIEW', 'CAREER_PRACTICE', 'TAILORED_PRACTICE') 
            THEN 1 
            ELSE 0 
        END) AS total_practices,
        COUNT(DISTINCT CASE 
            WHEN b."eventType" = 'INTERVIEWATHON' 
            THEN b."eventId" 
            ELSE NULL 
        END) AS unique_interviewathon
    FROM
        BestScores b
    WHERE
        b."organizationId" = $1
    GROUP BY
        b."userId",
        b."organizationId"
),
UserRoles AS (
    SELECT
        m."userId",
        m."organizationId",
        m."id" AS "membershipId",
        d."id" AS "departmentId",
        d."name" AS "departmentName",
        g."id" AS "groupId",
        g."name" AS "groupName",
        CASE 
            WHEN dmm.role = 'ADMIN' THEN 'ADMIN'
            ELSE 'MEMBER'
        END AS department_role,
        CASE 
            WHEN gmm.role = 'ADMIN' THEN 'ADMIN'
            ELSE 'MEMBER'
        END AS group_role
    FROM
        membership m
    LEFT JOIN
        department_membership_mapping dmm ON m."id" = dmm."membershipId"
    LEFT JOIN
        department d ON dmm."departmentId" = d."id"
    LEFT JOIN
        group_membership_mapping gmm ON m."id" = gmm."membershipId"
    LEFT JOIN
        "group" g ON gmm."groupId" = g."id"
    WHERE
        m."id" = $2 OR $2 IS NULL
),
FilteredAccess AS (
    SELECT DISTINCT
        a."userId",
        a."organizationId",
        a.total_score AS "totalFinalScore",
        a.avg_score AS "averageScore",
        a.total_practices,
        a.unique_interviewathon,
        up."fullName",
        COALESCE(d."name", ur."departmentName") AS "department",
        COALESCE(g."name", ur."groupName") AS "group"
    FROM
        AggregatedScores a
    JOIN
        "user_profile" up ON a."userId" = up."userId"
    JOIN
        membership m ON a."userId" = m."userId" AND a."organizationId" = m."organizationId"
    LEFT JOIN
        department_membership_mapping dmm ON m."id" = dmm."membershipId"
    LEFT JOIN
        department d ON dmm."departmentId" = d."id"
    LEFT JOIN
        group_membership_mapping gmm ON m."id" = gmm."membershipId"
    LEFT JOIN
        "group" g ON gmm."groupId" = g."id"
    LEFT JOIN
        UserRoles ur ON a."organizationId" = ur."organizationId"
    WHERE
        ($2 IS NULL OR (
            (ur.department_role = 'ADMIN' AND d."id" = ur."departmentId")
            OR (ur.department_role = 'MEMBER' AND ur.group_role = 'ADMIN' AND g."id" = ur."groupId")
            OR (ur.department_role = 'MEMBER' AND ur.group_role = 'MEMBER' AND g."id" = ur."groupId")
        ))
        AND ($3::text IS NULL OR d."id" = $3::text)
        AND ($4::text IS NULL OR g."id" = $4::text)
)
SELECT COUNT(*) as total_records
FROM (
    SELECT
        f."userId",
        f."organizationId",
        f."totalFinalScore",
        f."averageScore",
        f.total_practices,
        f.unique_interviewathon,
        f."fullName",
        f."department",
        f."group",
        DENSE_RANK() OVER (ORDER BY f."totalFinalScore" DESC, f."averageScore" DESC) AS rank
    FROM
        FilteredAccess f
) AS total_count_query;
    `;

    const baseQueryParams = userId
      ? [organizationId, skip, take, userId]
      : [organizationId, skip, take, memberShipId || null, department || null, group || null];

    const countQueryParams = [
      organizationId,
      memberShipId || null,
      department || null,
      group || null,
    ];
    const [leaderboardData, totalCount, totalInterviewathon]: any = await Promise.all([
      db.$queryRawUnsafe(baseQuery, ...baseQueryParams),
      db.$queryRawUnsafe(countQuery, ...countQueryParams),
      db.eventDetails.count({ where: { isPlacement: true, organizationId } }),
    ]);

    const currentUser: any = (leaderboardData as any).find((item) => item?.userId === userId);

    const userProfile = (leaderboardData as any)?.map((item) => {
      return {
        ...item,
        totalFinalScore: Number(item?.totalFinalScore),
        total_practices: Number(item?.total_practices),
        unique_interviewathon: Number(item?.unique_interviewathon),
        averageScore: Number(item?.averageScore?.toFixed(2) ?? 0),
        totalInterviewathon: Number(totalInterviewathon),
        rank: Number(item?.rank),
      };
    });

    let user = null;
    if (userId && currentUser) {
      user = {
        ...currentUser,
        totalFinalScore: Number(currentUser?.['totalFinalScore']),
        unique_interviewathon: Number(currentUser['unique_interviewathon']),
        total_practices: Number(currentUser['total_practices']),
        averageScore: Number(currentUser['averageScore']?.toFixed(2) ?? 0),
        totalInterviewathon: Number(totalInterviewathon),
        rank: Number(currentUser['rank']),
      };
    }
    const totalRecords = totalCount?.[0].total_records;
    const totalPages = Math.ceil(Number(totalRecords) / Number(per_page));

    return NextResponse.json(
      {
        userProfile: userId ? [] : userProfile,
        totalPages,
        user: userId ? user : null,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({
      error: 'Unable to fetch leadership data',
    });
  }
}
