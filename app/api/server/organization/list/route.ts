import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  try {
    let organization = await db.organization.findMany({
      include: {
        eventDetails: {
          select: {
            id: true,
            name: true,
          },
          where: { status: 'ACTIVE' },
        },
      },
    });
    return NextResponse.json(
      {
        organization,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'Unable to fetch Organization',
      },
      { status: 500 },
    );
  }
}
