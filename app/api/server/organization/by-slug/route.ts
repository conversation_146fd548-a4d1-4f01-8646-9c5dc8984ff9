import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const slug = searchParams.get('slug') ?? '';

  try {
    let organization = await db.organization.findFirst({
      where: { slug },
      include: {
        eventDetails: {
          select: {
            id: true,
            organizationId: true,
            role: true,
            level: true,
            name: true,
            jobDescription: true,
            timing: true,
          },
          where: { status: 'ACTIVE' },
        },
      },
    });
    return NextResponse.json(
      {
        organization,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'Unable to fetch Organization',
      },
      { status: 500 },
    );
  }
}
