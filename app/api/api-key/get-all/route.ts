import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  const { searchParams, organizationId } = await request.json();
  const { page = 1, per_page = 10 } = searchParams;

  try {
    const [api, count] = await Promise.all([
      db.apiKeys.findMany({
        where: {
          organizationId,
        },
        skip: Math.max(Number(page ?? 1) - 1, 0) * Number(per_page ?? 10),
        take: Number(per_page ?? 10),
        orderBy: {
          createdAt: 'desc',
        },
      }),
      db.apiKeys.count({
        where: {
          organizationId,
        },
      }),
    ]);

    return NextResponse.json(
      { api, pageCount: Math.ceil((count ?? 0) / Number(per_page)) },
      { status: 200 },
    );
  } catch (error) {
    return NextResponse.json({ error }, { status: 200 });
  }
}
