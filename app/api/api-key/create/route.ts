import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import generateApi<PERSON>ey from 'generate-api-key';

export async function POST(request: any) {
  const data = await request.json();
  const token = generateApiKey({ method: 'base62' });

  const inputWithToken: any = {
    ...data,
    token: token,
  };

  try {
    const api = await db.apiKeys.create({
      data: inputWithToken,
    });
    return NextResponse.json(
      {
        api,
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'webhook create failed' }, { status: 500 });
  }
}
