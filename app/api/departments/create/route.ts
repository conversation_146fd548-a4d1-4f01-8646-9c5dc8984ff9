import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { data } = await request.json();
  try {
    const department = await db.department.create({
      data,
    });
    return NextResponse.json(
      {
        department,
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'department create failed' }, { status: 500 });
  }
}
