import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(req: NextRequest) {
  const { membershipId, organizationId } = await req.json();

  try {
    const [department] = await Promise.all([
      db.department.findMany({
        where: {
          organizationId,
          DepartmentMembershipMapping: {
            some: {
              membershipId,
              role: {
                in: ['ADMIN', 'MEMBER'],
              },
            },
          },
        },
        include: {
          DepartmentMembershipMapping: {
            include: {
              membership: {
                include: {
                  user: {
                    include: {
                      userProfile: {
                        select: {
                          fullName: true,
                        },
                      },
                    },
                  },
                },
              },
            },
            where: {
              role: {
                in: ['ADMIN', 'MEMBER'],
              },
            },
          },
        },
      }),
    ]);
    return NextResponse.json({ department }, { status: 200 });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error }, { status: 500 });
  }
}
