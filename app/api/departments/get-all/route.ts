import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(req: NextRequest) {
  const { organizationId, departmentId, membershipId } = await req.json();

  if (departmentId) {
    try {
      const [department] = await Promise.all([
        db.department.findMany({
          where: {
            id: departmentId,
            organizationId,
          },
        }),
      ]);

      return NextResponse.json({ department: department }, { status: 200 });
    } catch (error) {
      return NextResponse.json({ error }, { status: 500 });
    }
  }
  if (membershipId) {
    try {
      const [department] = await Promise.all([
        db.department.findMany({
          where: {
            organizationId,
            DepartmentMembershipMapping: {
              some: {
                membershipId,
              },
            },
          },
        }),
      ]);
      return NextResponse.json({ department }, { status: 200 });
    } catch (error) {
      console.log({ error });
      return NextResponse.json({ error }, { status: 500 });
    }
  }

  try {
    const [department] = await Promise.all([
      db.department.findMany({
        where: {
          organizationId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);
    return NextResponse.json({ department }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error }, { status: 500 });
  }
}
