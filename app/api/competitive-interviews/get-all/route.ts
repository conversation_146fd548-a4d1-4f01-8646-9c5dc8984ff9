import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId');

  if (!userId) {
    return NextResponse.json(
      {
        message: 'userId is Missing',
      },
      { status: 403 },
    );
  }
  try {
    const result = await db.careerPractice.findMany({
      where: {
        userId: userId,
        event: 'competitive-interviews',
      },
      select: {
        role: true,
        level: true,
        createdAt: true,
        id: true,
        feedback: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(
      {
        items: result,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'Error while fetching data',
      },
      { status: 500 },
    );
  }
}
