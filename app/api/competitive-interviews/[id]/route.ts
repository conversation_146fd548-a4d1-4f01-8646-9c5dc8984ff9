import { NextResponse } from 'next/server';

import { OpenAIStreamPayload } from '@/lib/OpenAIStream';
import retryOpenAI from '@/lib/retryOpenApi';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';
import { getToken } from 'next-auth/jwt';
import { v4 as uuid } from 'uuid';

import { getUserSubscriptionPlan } from '../../../../pages/api/getSubscriptionStatus';

export const maxDuration = 299;

const MAX_QUESTIONS = 3;

interface ChatGPTMessage {
  role: 'system' | 'user';
  content: string;
}

interface ConversationItem {
  question: string;
  isAnswered: boolean;
  startTime: any;
  answer: string;
}

const conversationPrompt = (question: string, answer: string, role: string, level: String) => {
  return `Question: ${question}\nAnswer: ${answer}\nInterview me a follow-up question for ${role} subject and level :${level}. Limit it to 15 words.`;
};

function removeFieldsFromConversation(inputObject) {
  const fieldsToRemove = [
    'impact',
    'clarity',
    'passion',
    'strengths',
    'confidence',
    'communication',
    'areas_of_improvement',
    'language_proficiency',
  ];

  for (const item of inputObject.conversation) {
    for (const field of fieldsToRemove) {
      if (item.feedback && item.feedback[field]) {
        delete item.feedback[field];
      }
    }
  }
  return inputObject;
}

const updateConversation = async (
  data: any,
  transcript: string,
  questionId: string,
  s3Id: string,
  feedback,
) => {
  if (!data) {
    return null;
  }

  const conversation: any = data.conversation || [];

  if (conversation.filter((m) => m.isAnswered).length > MAX_QUESTIONS) {
    return conversation;
  }

  const updatedConversation = conversation.map((item) => {
    if (item?.id === questionId) {
      return {
        ...item,
        answer: transcript,
        s3Id: s3Id,
        isAnswered: true,
        videoOrigin: s3Id ? 'GCP_BUCKET' : '',
        feedback,
        completedTime: new Date(),
      };
    }
    return item;
  });

  return updatedConversation;
};

const generateMessages = async (role: string, level: string, conversation: ConversationItem[]) => {
  const systemPrompt = await generatePrompt(
    'COMPETITIVE_INTERVIEW_QUESTION_SYSTEM_PROMPT_STRATEGY',
    {},
  );
  const messages: ChatGPTMessage[] = [
    {
      role: 'system',
      content: systemPrompt,
    },
  ];

  const userPrompts: ChatGPTMessage[] = await Promise.all(
    conversation.map(async (item) => {
      const userPrompt = await generatePrompt(
        'COMPETITIVE_INTERVIEW_QUESTION_USER_PROMPT_STRATEGY',
        {
          role,
          level,
          question: item.question,
          answer: item.answer,
        },
      );

      return {
        role: 'user',
        content: userPrompt,
      };
    }),
  );
  messages.push(...userPrompts);
  return messages;
};

async function getUnansweredQuestions(careerPractice) {
  const unansweredQuestions = careerPractice?.conversation.filter((item) => !item.isAnswered);

  return unansweredQuestions;
}

export async function POST(request: any) {
  const token = await getToken({ req: request });

  const { transcript, question, id, s3Id, sub, questionId, organizationId, feedback } =
    (await request.json()) as {
      transcript?: string;
      question?: string;
      id?: string;
      s3Id?: string;
      sub?: string;
      questionId?: string;
      organizationId?: string;
      feedback?: any;
    };
  if (!id) {
    return NextResponse.json(
      {
        message: 'Missing testId or jobRole and jobLevel',
      },
      { status: 400 },
    );
  }

  if (id && transcript && question && questionId) {
    const data = await db.careerPractice.findFirst({
      where: {
        id: id,
        userId: sub || token?.sub,
      },
    });

    const updatedConversation = await updateConversation(
      data,
      transcript,
      questionId,
      s3Id || '',
      feedback,
    );

    if (!updatedConversation) {
      return NextResponse.json(
        {
          message: 'Invalid testId',
        },
        { status: 400 },
      );
    }

    if (updatedConversation.length > MAX_QUESTIONS) {
      return NextResponse.json(
        {
          message: 'Maximum question limit reached',
        },
        { status: 400 },
      );
    }
    if (updatedConversation?.length === MAX_QUESTIONS) {
      const response = await db.careerPractice.update({
        data: {
          conversation: [...updatedConversation],
        },
        where: {
          id: id,
        },
      });

      if (!response) {
        return NextResponse.json(
          {
            message: 'Invalid id',
          },
          { status: 400 },
        );
      }

      const unansweredQuestions = await getUnansweredQuestions(response);

      if (unansweredQuestions?.length === 0) {
        const response = await db.careerPractice.update({
          data: {
            completedTime: new Date(),
          },
          where: {
            id: id,
          },
        });
        const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/competitive-interviews/${id}/feedback`;
        fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: response?.id, organizationId }),
        });

        let plan = {};
        if (sub || token?.sub) {
          plan = await getUserSubscriptionPlan(sub || token?.sub || '');
        }

        return NextResponse.json(
          {
            message: 'Your interview session has been completed successfully.',
            role: response?.role,
            level: response?.level,
          },
          { status: 200 },
        );
      }

      const questionStatus = `${
        (response as any)?.conversation?.length - Number(unansweredQuestions?.length) + 1
      }/${(response as any)?.conversation?.length}`;
      return NextResponse.json(
        {
          question: unansweredQuestions[0].question,
          questionId: unansweredQuestions[0].id,
          round: unansweredQuestions[0].round,
          questionStatus: questionStatus,
          role: response?.role,
          level: response?.level,
        },
        { status: 200 },
      );
    }

    const messages = await generateMessages(
      data?.role || '',
      data?.level || '',
      updatedConversation,
    );

    const payload: OpenAIStreamPayload = {
      model: process.env.AZURE_OPEN_AI_MODEL || '',
      messages: messages,
      temperature: 0.7,
      top_p: 0.8,
      frequency_penalty: 0,
      presence_penalty: 0,
      stream: false,
      n: 1,
    };
    const result = await retryOpenAI(payload, 3, false);

    const finalData = await db.careerPractice.update({
      data: {
        conversation: [
          ...updatedConversation,
          {
            id: uuid(),
            question: result.replace(/^Question:\s+/, ''),
            startTime: new Date(),
            isAnswered: false,
            s3Id: '',
            answer: '',
          },
        ],
      },
      where: {
        id: id,
      },
    });

    if (!finalData) {
      return NextResponse.json(
        {
          message: 'Invalid id',
        },
        { status: 400 },
      );
    }

    const unansweredQuestions = await getUnansweredQuestions(finalData);

    if (unansweredQuestions?.length === 0) {
      return NextResponse.json(
        {
          message: 'Your interview session has been completed successfully.',
          role: finalData?.role,
          result: finalData,
          level: finalData?.level,
        },
        { status: 200 },
      );
    }

    const questionStatus = `${
      (finalData as any)?.conversation?.length - Number(unansweredQuestions?.length) + 1
    }/${(finalData as any)?.conversation?.length}`;
    return NextResponse.json(
      {
        question: unansweredQuestions[0].question,
        questionId: unansweredQuestions[0].id,
        role: finalData?.role,
        level: finalData?.level,
        round: unansweredQuestions?.[0]?.round,
        questionStatus: questionStatus,
      },
      { status: 200 },
    );
  }

  if (id && transcript && !question) {
    return NextResponse.json(
      {
        message: 'Missing question',
      },
      { status: 400 },
    );
  }

  if (id && !transcript && question) {
    return NextResponse.json(
      {
        message: 'Transcript cannot be empty',
      },
      { status: 400 },
    );
  }
}
