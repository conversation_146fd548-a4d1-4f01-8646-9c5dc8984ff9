import { NextResponse } from 'next/server';

import { OpenAIStreamPayload } from '@/lib/OpenAIStream';
import retryOpenAI from '@/lib/retryOpenApi';
import { updateFeedbackBasedOnConversation } from '@/pages/api/server/admin/cron/feedback-result';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';

export const maxDuration = 299;

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing env var from OpenAI');
}

const generateFeedback = async (messages) => {
  const payload: OpenAIStreamPayload = {
    model: process.env.AZURE_OPEN_AI_MODEL || '',
    messages,
    temperature: 0.7,
    top_p: 0.8,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: false,
    n: 1,
    response_format: { type: 'json_object' },
  };
  const result = await retryOpenAI(payload, 3, true);
  return result;
};

export async function POST(request: any) {
  const { id, organizationId } = await request.json();
  try {
    const careerPractice = await db.careerPractice.findUnique({
      where: {
        id: id,
      },
      include: {
        eventDetails: {
          select: {
            evaluation: true,
          },
        },
      },
    });

    if (!(careerPractice?.conversation as any)?.length) {
      return NextResponse.json(
        {
          error: 'Error',
        },
        { status: 400 },
      );
    }
    const overAllResult = updateFeedbackBasedOnConversation(careerPractice?.conversation);

    const questions = (careerPractice?.conversation as any)?.map((item, index) => {
      return `question ${index + 1}: ${item?.answer},
    feedback summary: ${item?.feedback?.short_summary},
     ${
       item?.feedback?.proctoring?.camera_angle
         ? `camera angle:${item?.feedback?.proctoring?.camera_angle},`
         : ''
     }     ${
       item?.feedback?.proctoring?.professional_attire
         ? `professional attire:${item?.feedback?.proctoring?.professional_attire},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['eye_contact_&_posture']
         ? `eye contact and posture:${item?.feedback?.proctoring?.['eye_contact_&_posture']},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['suspicious_activity']
         ? `suspicious activity:${item?.feedback?.proctoring?.suspicious_activity},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['words_per_minute']
         ? `words per minute:${item?.feedback?.proctoring?.words_per_minute},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['emotional_state']
         ? `emotional state:${item?.feedback?.proctoring?.emotional_state},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['candidate_movement']
         ? `candidate movement:${item?.feedback?.proctoring?.suspicious_activity},`
         : ''
     }     ${
       item?.feedback?.proctoring?.['background']
         ? `background:${item?.feedback?.proctoring?.suspicious_activity},`
         : ''
     }`;
    });
    const question = questions?.filter((element) => element !== null);

    if (question?.length === 0) {
      return NextResponse.json({ status: 200 });
    }
    const feedback = await generateFeedback([
      {
        role: 'system',
        content: await generatePrompt('CONSOLIDATED_FEEDBACK_PROMPT_STRATEGY', {
          role: careerPractice?.role,
          level: careerPractice?.level,
          questions: question?.join('\n'),
          fullScreen: '',
          tabSwitch: '',
          evaluation: careerPractice?.eventDetails?.evaluation,
        }),
      },
    ]);
    if (organizationId && careerPractice?.userId !== 'Visitor') {
      await db.leaderboard.create({
        data: {
          organizationId: organizationId,
          userId: careerPractice?.userId ?? '',
          score: Math.round(Number((overAllResult as any)?.overall_score) ?? 0),
          eventId: careerPractice?.id ?? '',
          eventType: 'COMPETITIVE_INTERVIEW',
        },
      });
    }

    const response = await db.careerPractice.update({
      data: {
        feedback: { ...(careerPractice?.feedback as any), ...overAllResult, ...feedback },
        timing: {
          ...(careerPractice as any)?.timing,
          feedBackGenerateTime: new Date(),
        },
        finalScore: Math.round((overAllResult as any)?.overall_score),
      },
      where: {
        id: id,
      },
    });
    return NextResponse.json(
      {
        response,
        ok: true,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log(`Error generating competitive feedback ${error}`);
    return NextResponse.json(
      {
        error: 'Error generating competitive interview feedback',
        ok: false,
      },
      { status: 500 },
    );
  }
}
