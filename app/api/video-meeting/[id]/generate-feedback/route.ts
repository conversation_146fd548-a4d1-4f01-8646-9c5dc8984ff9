import { NextResponse } from 'next/server';

import { GCPVertexAI } from '@/lib/ai-models/src/models/gcp-vertexai';
import { VertexAIConfiguration } from '@/lib/ai-models/src/types/vertexai-model';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';

export const maxDuration = 299;

const project = process.env.NEXT_PUBLIC_GCP_PROJECT_ID || '';
const model = process.env.NEXT_PUBLIC_GCP_MODEL || '';
const location = process.env.NEXT_PUBLIC_GCP_LOCATION || '';
const defaultBucket = process.env.NEXT_PUBLIC_GCP_BUCKET || '';
// const folder = process.env.NEXT_PUBLIC_S3FOLDER || ''; // Unused variable

// Function to get GCP bucket from organization or use default
async function getGCPBucket(organizationId) {
  if (!organizationId) return defaultBucket;

  try {
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { gcpBucket: true },
    });

    return organization?.gcpBucket || defaultBucket;
  } catch (error) {
    console.error('Error fetching GCP bucket:', error);
    return defaultBucket;
  }
}

export async function POST(request: any, { params }: { params: { id: string } }) {
  const id = params.id;

  if (!id) {
    return NextResponse.json({ error: 'Missing video meeting ID' }, { status: 400 });
  }

  try {
    // Get the video meeting with related data
    const videoMeeting = await db.videoCallInterview.findUnique({
      where: { id },
      include: {
        user: {
          include: {
            userProfile: true,
          },
        },
        careerPractice: {
          include: {
            eventDetails: {
              include: {
                organization: true,
              },
            },
          },
        },
        eventDetails: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!videoMeeting) {
      return NextResponse.json({ error: 'Video meeting not found' }, { status: 404 });
    }

    // Check if feedback already exists
    if (videoMeeting.feedback) {
      return NextResponse.json(
        {
          message: 'Feedback already generated',
          feedback: videoMeeting.feedback,
        },
        { status: 200 },
      );
    }

    if (!videoMeeting.s3RecordingId) {
      return NextResponse.json({ error: 'No recording found for this meeting' }, { status: 400 });
    }

    // Get role and level from the actual job application
    const organization =
      videoMeeting.eventDetails?.organization ||
      videoMeeting.careerPractice?.eventDetails?.organization;

    // Get role and level from EventDetails or CareerPractice
    const role =
      videoMeeting.eventDetails?.role || videoMeeting.careerPractice?.role || 'General Position';

    const level =
      videoMeeting.eventDetails?.level || videoMeeting.careerPractice?.level || 'Mid-level';

    console.log('Video Interview Feedback Generation:', {
      videoMeetingId: id,
      role,
      level,
      hasCareerPractice: !!videoMeeting.careerPractice,
      hasEventDetails: !!videoMeeting.eventDetails,
      careerPracticeRole: videoMeeting.careerPractice?.role,
      careerPracticeLevel: videoMeeting.careerPractice?.level,
    });

    // Get bucket from organization
    const bucket = await getGCPBucket(organization?.id);

    // Get additional context for better feedback
    const jobDescription =
      videoMeeting.eventDetails?.jobDescription ||
      videoMeeting.careerPractice?.eventDetails?.jobDescription;

    const skillsAndKeywords =
      videoMeeting.eventDetails?.skillsAndKeywords ||
      videoMeeting.careerPractice?.eventDetails?.skillsAndKeywords ||
      [];

    // Generate system prompt for video interview feedback
    const systemInput = await generatePrompt(
      'VIDEO_INTERVIEW_COMPREHENSIVE_FEEDBACK_SYSTEM_PROMPT',
      {
        role: role,
        level: level,
        interviewType: videoMeeting.meetingType || 'General Interview',
        jobDescription: jobDescription,
        requiredSkills: skillsAndKeywords.join(', '),
      },
    );

    // Generate feedback using video analysis
    const feedbackResult = await generateVideoInterviewFeedback({
      systemInput,
      videoId: videoMeeting.s3RecordingId,
      bucket,
      isAiInterview: false,
    });

    // Check if feedback generation was successful
    if (feedbackResult.success === false) {
      return NextResponse.json(
        {
          error: 'Failed to generate feedback',
          details: feedbackResult.error || 'AI response parsing failed',
        },
        { status: 500 },
      );
    }

    // Update the video meeting with generated feedback
    await db.videoCallInterview.update({
      where: { id },
      data: {
        feedback: feedbackResult.feedback,
      },
    });

    return NextResponse.json(
      {
        message: 'Feedback generated successfully',
        feedback: feedbackResult.feedback,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Error generating video interview feedback:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate feedback',
        details: error.message,
      },
      { status: 500 },
    );
  }
}

const generateVideoInterviewFeedback = async ({ systemInput, videoId, bucket, isAiInterview }) => {
  // Use provided bucket or default
  const bucketName = bucket || defaultBucket;
  // Video meetings are stored in: recordings/video-meeting/filename
  const videoFolder = isAiInterview
    ? ''
    : `${process.env.NEXT_PUBLIC_S3FOLDER || 'recordings'}/video-meeting`;
  const videoPath = videoFolder ? `${videoFolder}/${videoId}` : videoId;

  const config: VertexAIConfiguration = {
    project,
    model,
    location,
    systemInput,
    credentials: {
      private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
      client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
      client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
    },
    generationConfig: {
      maxOutputTokens: 8192,
      temperature: 0.7,
      topP: 0.8,
    },
  };

  const generateContent = async () => {
    const vertexAI = new GCPVertexAI(config);
    return await vertexAI.generateContent({
      video: {
        fileData: {
          mimeType: 'video/mp4',
          fileUri: `gs://${bucketName}/${videoPath}`,
        },
      },
      question:
        'Analyze this video interview and provide comprehensive feedback including job fit analysis, communication skills, and overall assessment.',
    });
  };

  let stream = await generateContent();

  // Parse the response with robust error handling
  try {
    let responseText = stream?.parts?.[0]?.text || '';

    // Log the raw response for debugging
    console.log('Raw AI Response:', responseText.substring(0, 500) + '...');

    // Clean up the response text
    responseText = responseText
      .replace(/```json/gi, '')
      .replace(/```/g, '')
      .replace(/^\s*[\r\n]+/gm, '') // Remove empty lines
      .replace(/\{\{/g, '{') // Fix double opening braces
      .replace(/\}\}/g, '}') // Fix double closing braces
      .trim();

    // Try to find JSON content between braces - handle incomplete responses
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      responseText = jsonMatch[0];
    } else {
      // If no complete JSON found, try to find the start and construct a minimal valid JSON
      const startMatch = responseText.match(/\{[\s\S]*/);
      if (startMatch) {
        responseText = startMatch[0];
        // If the JSON is incomplete, try to close it properly
        if (!responseText.endsWith('}')) {
          // Count open braces vs close braces
          const openBraces = (responseText.match(/\{/g) || []).length;
          const closeBraces = (responseText.match(/\}/g) || []).length;
          const missingBraces = openBraces - closeBraces;

          // Add missing closing braces
          for (let i = 0; i < missingBraces; i++) {
            responseText += '}';
          }
        }
      }
    }

    // Additional cleanup for common AI response issues
    responseText = responseText
      .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
      .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // Quote unquoted keys
      .replace(/:\s*([^",{\[\]}\s][^",{\[\]}\n]*?)(\s*[,}\]])/g, ': "$1"$2'); // Quote unquoted string values

    console.log('Cleaned Response:', responseText.substring(0, 500) + '...');

    // Try multiple parsing strategies
    let parsedResponse: any;

    // Strategy 1: Direct parsing
    try {
      parsedResponse = JSON.parse(responseText);
    } catch (e1) {
      console.log('Strategy 1 failed, trying strategy 2...');

      // Strategy 2: Try to fix common JSON issues
      try {
        const fixedText = responseText
          .replace(/'/g, '"') // Replace single quotes with double quotes
          .replace(/(\w+):/g, '"$1":') // Ensure all keys are quoted
          .replace(/,\s*}/g, '}') // Remove trailing commas before closing braces
          .replace(/,\s*]/g, ']'); // Remove trailing commas before closing brackets

        parsedResponse = JSON.parse(fixedText);
      } catch (e2) {
        console.log('Strategy 2 failed, trying strategy 3...');

        // Strategy 3: Extract just the content between first { and last }
        const start = responseText.indexOf('{');
        const end = responseText.lastIndexOf('}');
        if (start !== -1 && end !== -1 && end > start) {
          const extractedJson = responseText.substring(start, end + 1);
          parsedResponse = JSON.parse(extractedJson);
        } else {
          throw new Error('No valid JSON structure found in AI response');
        }
      }
    }

    return {
      success: true,
      feedback: parsedResponse,
    };
  } catch (error) {
    console.error('Error parsing feedback response:', error);
    console.error('Failed response text:', stream?.parts?.[0]?.text?.substring(0, 1000));

    // Attempt to regenerate the response once
    console.log('Attempting to regenerate AI response due to invalid JSON format');
    try {
      stream = await generateContent();

      // Try parsing the regenerated response
      let retryResponseText = stream?.parts?.[0]?.text || '';

      // Clean up the retry response text
      retryResponseText = retryResponseText
        .replace(/```json/gi, '')
        .replace(/```/g, '')
        .replace(/^\s*[\r\n]+/gm, '')
        .trim();

      const retryJsonMatch = retryResponseText.match(/\{[\s\S]*\}/);
      if (retryJsonMatch) {
        retryResponseText = retryJsonMatch[0];
      }

      const retryParsedResponse = JSON.parse(retryResponseText);

      console.log('✅ Successfully parsed feedback JSON on retry');
      return {
        success: true,
        feedback: retryParsedResponse,
      };
    } catch (retryError) {
      console.error('Retry JSON parsing also failed:', retryError);
      return {
        success: false,
        error: `AI response parsing failed after retry: ${retryError.message}`,
        feedback: null,
      };
    }
  }
};
