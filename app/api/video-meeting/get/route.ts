import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id') ?? '';
  const status = searchParams.get('status') ?? '';
  let where: any = {};
  if (status) {
    where.meetingStatus = status;
  }
  try {
    let meeting = await db.videoCallInterview.findFirst({
      where: { id, ...where },
      include: {
        user: {
          include: {
            userProfile: {
              select: { fullName: true },
            },
          },
        },
        careerPractice: {
          select: {
            interviewerComments: true,
          },
        },
      },
    });
    let comments: any = meeting?.careerPractice?.interviewerComments ?? [];
    if (comments?.length > 0) {
      let commentByIds = comments?.map((comment) => comment?.commentBy);
      commentByIds = commentByIds?.filter((item) => item);
      const commentByUsers: any = await db.user.findMany({
        where: {
          id: { in: commentByIds },
        },
        include: {
          userProfile: {
            select: { fullName: true },
          },
        },
      });
      comments = comments?.map((comment) => {
        return {
          ...comment,
          commentBy: commentByUsers.find((user) => user?.id === comment?.commentBy),
        };
      });
    }
    return NextResponse.json(
      {
        meeting: { ...meeting, comments },
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ 'Error updating meeting': error });
    return NextResponse.json({ error: 'Error updating meeting' }, { status: 500 });
  }
}
