import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const input: any = await request.json();
  const meeting = await db.videoCallInterview.findFirst({
    where: {
      meetingId: input?.recording?.meetingId ?? input?.meeting?.id,
    },
  });
  if (
    input?.reason !== 'HOST_ENDED_MEETING' &&
    meeting?.meetingId === input?.recording?.meetingId
  ) {
    await db.videoCallInterview.update({
      where: { id: meeting?.id },
      data: { s3RecordingId: input?.recording?.outputFileName },
    });
  }
  if (input?.reason === 'HOST_ENDED_MEETING' && meeting?.meetingId === input?.meeting?.id) {
    await db.videoCallInterview.update({
      where: { id: meeting?.id },
      data: { meetingStatus: 'COMPLETED' },
    });
  }

  try {
    return NextResponse.json(
      {
        message: 's3 video id update',
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ 'Error updating meeting': error });
    return NextResponse.json({ error: 'Error updating meeting' }, { status: 500 });
  }
}
