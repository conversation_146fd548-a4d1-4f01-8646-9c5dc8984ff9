import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { id, data } = await request.json();
  try {
    let videoCallInterview = await db.videoCallInterview.update({
      where: { id },
      data,
    });
    return NextResponse.json(
      {
        videoCallInterview,
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'videoCallInterview Not Found' }, { status: 500 });
  }
}
