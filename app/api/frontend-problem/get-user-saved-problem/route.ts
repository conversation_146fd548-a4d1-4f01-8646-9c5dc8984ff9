import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { Prisma } from '@prisma/client';
import { getToken } from 'next-auth/jwt';
import { v4 as uuid } from 'uuid';

export const maxDuration = 299;

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const questionId = searchParams.get('questionId') ?? '';
  const userId = searchParams.get('userId') ?? '';

  const problem = await db.saveFrontendProblem.findFirst({
    where: {
      questionId: questionId,
      userId: userId,
    },
    orderBy: {
      updatedAt: 'desc',
    },
  });

  return NextResponse.json({
    problem: problem,
    status: 200,
  });
}
