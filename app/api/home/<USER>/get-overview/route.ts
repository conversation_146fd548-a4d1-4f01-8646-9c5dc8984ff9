import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import {
  endOfMonth,
  startOfDay,
  startOfMonth,
  startOfWeek,
  subHours,
  subMinutes,
  subMonths,
} from 'date-fns';

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const organizationId = searchParams.get('organizationId') ?? '';

  const timeFilter =
    searchParams.get('filter') !== 'undefined' ? searchParams.get('filter') : 'last1hour';
  const where: any = {
    eventDetails: {
      organizationId,
      isPlacement: true,
    },
  };

  const now = new Date();
  if (timeFilter === 'last15min') {
    where.completedTime = {
      gte: subMinutes(now, 15),
    };
  } else if (timeFilter === 'last1hour') {
    where.completedTime = {
      gte: subHours(now, 1),
    };
  } else if (timeFilter === 'last4hour') {
    where.completedTime = {
      gte: subHours(now, 4),
    };
  } else if (timeFilter === 'today') {
    where.completedTime = {
      gte: startOfDay(now),
    };
  } else if (timeFilter === 'thisWeek') {
    where.completedTime = {
      gte: startOfWeek(now),
    };
  } else if (timeFilter === 'thisMonth') {
    where.completedTime = {
      gte: startOfMonth(now),
    };
  } else if (timeFilter === 'lastMonth') {
    const lastMonthStart = startOfMonth(subMonths(now, 1));
    const lastMonthEnd = endOfMonth(subMonths(now, 1));
    where.completedTime = {
      gte: lastMonthStart,
      lte: lastMonthEnd,
    };
  }

  try {
    const [practices, totalParticipation] = await Promise.all([
      db.careerPractice.findMany({
        where,
        select: {
          completedTime: true,
          interviewStatus: true,
          resultStatus: true,
        },
      }),
      db.careerPractice.count({
        where: {
          createdAt: where.completedTime,
          eventDetails: {
            organizationId,
            isPlacement: true,
          },
        },
      }),
    ]);

    let completedCount = 0;
    let partialCount = 0;

    practices.forEach((practice) => {
      if (practice.completedTime !== null && practice.interviewStatus !== 'PARTIALLY_COMPLETED') {
        completedCount += 1;
      }
      if (practice.interviewStatus === 'PARTIALLY_COMPLETED') {
        partialCount += 1;
      }
    });

    return NextResponse.json(
      {
        count: {
          completedCount,
          partialCount,
          totalParticipation,
        },
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'error fetching interview counts',
      },
      { status: 500 },
    );
  }
}
