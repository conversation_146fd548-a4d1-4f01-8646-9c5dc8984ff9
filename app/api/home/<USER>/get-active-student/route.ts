import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import {
  endOfDay,
  endOfMonth,
  startOfDay,
  startOfMonth,
  startOfWeek,
  subHours,
  subMinutes,
  subMonths,
} from 'date-fns';

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const organizationId = searchParams.get('organizationId') ?? '';
  const page: any = searchParams.get('page') ?? 1;
  const membershipIdString = searchParams.get('membershipId') ?? '';
  const memberShipId = ['undefined', 'null'].includes(membershipIdString?.toLowerCase())
    ? null
    : membershipIdString;
  const timeFilter =
    searchParams.get('filter') !== 'undefined' ? searchParams.get('filter') : 'last1hour';
  const pageSize = 10;
  const skip: any = (page - 1) * pageSize;

  const take: any = pageSize;
  const where: any = {
    organizationId: organizationId,
  };
  let adminDepartments: any = [];
  let memberGroups: any = [];

  if (memberShipId) {
    const memberShip: any = await db.membership.findUnique({
      where: {
        id: memberShipId,
      },
      include: {
        DepartmentMembershipMapping: {
          include: {
            department: true,
          },
        },
        GroupMembershipMapping: {
          include: {
            group: true,
          },
        },
      },
    });

    adminDepartments = memberShip.DepartmentMembershipMapping.filter((m) => m.role === 'ADMIN').map(
      (m) => m.department.id,
    );
    memberGroups = memberShip.GroupMembershipMapping.map((m) => m.group.id);
  }

  // If membershipId is available, apply filters; otherwise, show all students
  if (memberShipId) {
    where.user = {
      memberships: {
        some: {
          role: 'STUDENT',
          organizationId,
          OR: [
            {
              DepartmentMembershipMapping: {
                some: {
                  department: {
                    id: {
                      in: [...adminDepartments],
                    },
                  },
                },
              },
            },
            {
              GroupMembershipMapping: {
                some: {
                  group: {
                    id: {
                      in: memberGroups,
                    },
                  },
                },
              },
            },
          ],
        },
      },
    };
  } else {
    // If no membershipId, show all students for the organization
    where.user = {
      memberships: {
        some: {
          role: 'STUDENT',
          organizationId,
        },
      },
    };
  }

  // Add time filter to the where clause
  const now = new Date();
  if (timeFilter === 'last15min') {
    where.createdAt = {
      gte: subMinutes(now, 15),
    };
  } else if (timeFilter === 'last1hour') {
    where.createdAt = {
      gte: subHours(now, 1),
    };
  } else if (timeFilter === 'last4hour') {
    where.createdAt = {
      gte: subHours(now, 4),
    };
  } else if (timeFilter === 'today') {
    where.createdAt = {
      gte: startOfDay(now),
      lte: endOfDay(now),
    };
  } else if (timeFilter === 'thisWeek') {
    where.createdAt = {
      gte: startOfWeek(now),
    };
  } else if (timeFilter === 'thisMonth') {
    where.createdAt = {
      gte: startOfMonth(now),
    };
  } else if (timeFilter === 'lastMonth') {
    const lastMonthStart = startOfMonth(subMonths(now, 1));
    const lastMonthEnd = endOfMonth(subMonths(now, 1));
    where.createdAt = {
      gte: lastMonthStart,
      lte: lastMonthEnd,
    };
  }
  try {
    const [practices, totalCount] = await Promise.all([
      db.leaderboard.groupBy({
        by: ['userId'],
        where: {
          ...where,
          eventType: {
            in: [
              'CODING_PRACTICE',
              'FRONTEND_PRACTICE',
              'MOCK_INTERVIEW',
              'CAREER_PRACTICE',
              'TAILORED_PRACTICE',
              'INTERVIEWATHON',
            ],
          },
        },
        _count: {
          userId: true,
        },
        orderBy: {
          _count: {
            userId: 'desc',
          },
        },
        skip,
        take,
      }),
      db.leaderboard.groupBy({
        by: ['userId'],
        where: {
          ...where,
          eventType: {
            in: [
              'CODING_PRACTICE',
              'FRONTEND_PRACTICE',
              'MOCK_INTERVIEW',
              'CAREER_PRACTICE',
              'TAILORED_PRACTICE',
              'INTERVIEWATHON',
            ],
          },
        },
        _count: {
          userId: true,
        },
      }),
    ]);

    const userIds = practices?.map((user) => user?.userId);

    const [interviewathons, userDetails] = await Promise.all([
      db.leaderboard.groupBy({
        by: ['userId'],
        where: {
          ...where,
          userId: { in: userIds },
          eventType: {
            in: ['INTERVIEWATHON'],
          },
        },
        _count: {
          userId: true, // Count only INTERVIEWATHON events
        },
      }),
      db.userProfile.findMany({
        where: {
          userId: {
            in: userIds,
          },
        },
        select: {
          fullName: true,
          userId: true,
          user: {
            select: {
              email: true,
            },
          },
          // Add other user fields you want to include
        },
      }),
    ]);
    const combinedResults = practices.map((user) => {
      const interviewathonCount =
        interviewathons.find((entry) => entry.userId === user.userId)?._count.userId || 0;
      const userDetail = userDetails.find((entry) => entry.userId === user.userId);

      return {
        userId: user.userId,
        fullName: userDetail?.fullName,
        email: userDetail?.user?.email,
        totalEvents: user._count.userId,
        interviewathonEvents: interviewathonCount,
      };
    });

    return NextResponse.json(
      { userDetails: combinedResults, totalCount: Math.ceil(totalCount?.length / pageSize) },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'error fetching interview counts',
      },
      { status: 500 },
    );
  }
}
