import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import {
  endOfDay,
  endOfMonth,
  startOfDay,
  startOfMonth,
  startOfWeek,
  subHours,
  subMinutes,
  subMonths,
} from 'date-fns';

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const organizationId = searchParams.get('organizationId') ?? '';
  const membershipId = searchParams.get('membershipId') ?? '';
  let field = searchParams.get('field') ?? '';
  const page = searchParams.get('page') ?? 1;
  const role = searchParams.get('role') ?? '';
  const timeFilter =
    searchParams.get('filter') !== 'undefined' ? searchParams.get('filter') : 'last1hour';
  const where: any = {
    eventDetails: {
      organizationId,
      isPlacement: false,
    },
  };
  if (!['admin', 'owner']?.includes(role?.toLowerCase())) {
    where.eventDetails = {
      ...where.eventDetails,
      MembershipOnEventDetails: {
        some: {
          membershipId,
        },
      },
    };
  }
  if (field === 'feedbackNotSent') {
    where.resultStatus = null;
    where.interviewStatus = 'COMPLETED';
    field = 'createdAt';
  } else if (field === 'completedTime') {
    where.interviewStatus = 'COMPLETED';
  }
  // Add time filter to the where clause

  const now = new Date();
  if (timeFilter === 'last15min') {
    where.completedTime = {
      gte: subMinutes(now, 15),
    };
  } else if (timeFilter === 'last1hour') {
    where.completedTime = {
      gte: subHours(now, 1),
    };
  } else if (timeFilter === 'last4hour') {
    where.completedTime = {
      gte: subHours(now, 4),
    };
  } else if (timeFilter === 'today') {
    where.completedTime = {
      gte: startOfDay(now),
      lte: endOfDay(now),
    };
  } else if (timeFilter === 'thisWeek') {
    where.completedTime = {
      gte: startOfWeek(now),
    };
  } else if (timeFilter === 'thisMonth') {
    where.completedTime = {
      gte: startOfMonth(now),
    };
  } else if (timeFilter === 'lastMonth') {
    const lastMonthStart = startOfMonth(subMonths(now, 1));
    const lastMonthEnd = endOfMonth(subMonths(now, 1));
    where.completedTime = {
      gte: lastMonthStart,
      lte: lastMonthEnd,
    };
  }

  try {
    const [eventDetails, totalCount] = await Promise.all([
      db.careerPractice.findMany({
        select: {
          id: true,
          timing: true,
          interviewStatus: true,
          resultStatus: true,
          completedTime: true,
          feedback: true,
          user: {
            select: {
              email: true,
              userProfile: {
                select: {
                  fullName: true,
                  userId: true,
                },
              },
            },
          },
          eventDetails: {
            select: { name: true, id: true },
          },
        },
        where,
        take: Number(10),
        skip: Math.max(Number(page ?? 1) - 1, 0) * 10,
        orderBy: {
          [field]: 'desc',
        },
      }),
      db.careerPractice.count({ where }),
    ]);

    return NextResponse.json(
      { eventDetails, totalCount: Math.ceil(totalCount / 10) },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'error fetching interview counts',
      },
      { status: 500 },
    );
  }
}
