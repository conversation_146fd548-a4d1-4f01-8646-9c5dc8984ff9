import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  const { searchParams } = new URL(request.url);
  const role = searchParams.get('role') ?? '';
  const organizationId = searchParams.get('organizationId') ?? '';
  const isPlacement = searchParams.get('isPlacement') ?? '';
  const membershipId = searchParams.get('membershipId') ?? '';

  const where: any = {
    organizationId,
    isPlacement: isPlacement === 'true' ? true : false,
  };

  if (!['admin', 'owner']?.includes(role?.toLowerCase())) {
    where.MembershipOnEventDetails = {
      some: {
        membershipId,
      },
    };
  }

  try {
    const practices = await db.eventDetails.findMany({
      where,
    });

    return NextResponse.json(
      {
        count: {
          total: practices.length,
          activeInterview: practices?.filter((practice: any) => practice.status === 'ACTIVE')
            .length,
          completedInterview: practices?.filter((practice: any) => practice.status === 'COMPLETED')
            .length,
          archivedInterview: practices?.filter((practice: any) => practice.status === 'ARCHIVED')
            .length,
          draftInterview: practices?.filter((practice: any) => practice.status === 'DRAFT').length,
        },
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'error fetching interview counts',
      },
      { status: 500 },
    );
  }
}
