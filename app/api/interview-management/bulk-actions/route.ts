import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  const { action, interviewIds, data, userId, organizationId } = await request.json();

  if (!action || !interviewIds || !Array.isArray(interviewIds) || interviewIds.length === 0) {
    return NextResponse.json({ error: 'Invalid request parameters' }, { status: 400 });
  }

  try {
    let result;

    switch (action) {
      case 'reschedule':
        if (!data.newDate || !data.newTime) {
          return NextResponse.json({ error: 'New date and time are required for rescheduling' }, { status: 400 });
        }

        const newDateTime = new Date(`${data.newDate}T${data.newTime}`);
        
        result = await db.videoCallInterview.updateMany({
          where: {
            id: { in: interviewIds },
            organizationId: organizationId,
          },
          data: {
            scheduleTime: newDateTime,
            updatedAt: new Date(),
          },
        });

        // Log the reschedule action (optional)
        await Promise.all(
          interviewIds.map(interviewId =>
            db.videoCallInterview.update({
              where: { id: interviewId },
              data: {
                meetingDetails: {
                  ...data,
                  rescheduleReason: data.reason,
                  rescheduledBy: userId,
                  rescheduledAt: new Date(),
                },
              },
            })
          )
        );

        break;

      case 'cancel':
        if (!data.cancelReason) {
          return NextResponse.json({ error: 'Cancellation reason is required' }, { status: 400 });
        }

        result = await db.videoCallInterview.updateMany({
          where: {
            id: { in: interviewIds },
            organizationId: organizationId,
          },
          data: {
            meetingStatus: 'CANCELLED',
            updatedAt: new Date(),
          },
        });

        // Log the cancellation details
        await Promise.all(
          interviewIds.map(interviewId =>
            db.videoCallInterview.update({
              where: { id: interviewId },
              data: {
                meetingDetails: {
                  ...data,
                  cancelledBy: userId,
                  cancelledAt: new Date(),
                  cancelReason: data.cancelReason,
                  notes: data.notes,
                },
              },
            })
          )
        );

        break;

      case 'status':
        if (!data.newStatus) {
          return NextResponse.json({ error: 'New status is required' }, { status: 400 });
        }

        const statusMapping = {
          'scheduled': 'PENDING',
          'in-progress': 'PENDING', // We don't have an in-progress status in the DB
          'completed': 'COMPLETED',
          'cancelled': 'CANCELLED',
          'no-show': 'CANCELLED', // Treat no-show as cancelled
        };

        const dbStatus = statusMapping[data.newStatus] || 'PENDING';

        result = await db.videoCallInterview.updateMany({
          where: {
            id: { in: interviewIds },
            organizationId: organizationId,
          },
          data: {
            meetingStatus: dbStatus,
            updatedAt: new Date(),
          },
        });

        // Log the status change
        await Promise.all(
          interviewIds.map(interviewId =>
            db.videoCallInterview.update({
              where: { id: interviewId },
              data: {
                meetingDetails: {
                  statusChangedBy: userId,
                  statusChangedAt: new Date(),
                  statusChangeReason: data.statusNotes,
                  previousStatus: 'PENDING', // We'd need to fetch this if we want to track it properly
                  newStatus: dbStatus,
                },
              },
            })
          )
        );

        break;

      case 'email':
        if (!data.subject || !data.message) {
          return NextResponse.json({ error: 'Email subject and message are required' }, { status: 400 });
        }

        // Get interview details for email sending
        const interviews = await db.videoCallInterview.findMany({
          where: {
            id: { in: interviewIds },
            organizationId: organizationId,
          },
          include: {
            user: {
              include: {
                userProfile: true,
              },
            },
            careerPractice: {
              include: {
                eventDetails: true,
              },
            },
          },
        });

        // Here you would integrate with your email service
        // For now, we'll just log the email action
        const emailPromises = interviews.map(async (interview) => {
          // Mock email sending - replace with actual email service
          console.log(`Sending email to ${interview.user?.email}:`, {
            subject: data.subject,
            message: data.message,
            candidateName: interview.user?.userProfile?.fullName || interview.user?.email,
            interviewDetails: {
              role: interview.careerPractice?.role,
              scheduleTime: interview.scheduleTime,
              interviewName: interview.careerPractice?.eventDetails?.name,
            },
          });

          // Update interview with email log
          return db.videoCallInterview.update({
            where: { id: interview.id },
            data: {
              meetingDetails: {
                emailSent: true,
                emailSentAt: new Date(),
                emailSentBy: userId,
                emailSubject: data.subject,
                emailTemplate: data.template,
              },
            },
          });
        });

        await Promise.all(emailPromises);
        result = { count: interviews.length };

        break;

      case 'export':
        // Get interview details for export
        const exportInterviews = await db.videoCallInterview.findMany({
          where: {
            id: { in: interviewIds },
            organizationId: organizationId,
          },
          include: {
            user: {
              include: {
                userProfile: true,
              },
            },
            careerPractice: {
              include: {
                eventDetails: true,
              },
            },
          },
        });

        // Get interviewer details
        const allInterviewerIds = exportInterviews.flatMap(interview => interview.interviewerId || []);
        const interviewers = await db.user.findMany({
          where: {
            id: { in: allInterviewerIds },
          },
          include: {
            userProfile: true,
          },
        });

        // Format data for export
        const exportData = exportInterviews.map(interview => {
          const interviewerDetails = interviewers.filter(interviewer => 
            interview.interviewerId?.includes(interviewer.id)
          );
          
          const interviewerNames = interviewerDetails.map(interviewer => 
            interviewer.userProfile?.fullName || interviewer.email
          ).join(', ');

          return {
            'Interview ID': interview.id,
            'Candidate Name': interview.user?.userProfile?.fullName || interview.user?.email,
            'Candidate Email': interview.user?.email,
            'Role': interview.careerPractice?.role || interview.careerPractice?.eventDetails?.role,
            'Level': interview.careerPractice?.level || interview.careerPractice?.eventDetails?.level,
            'Interview Name': interview.careerPractice?.eventDetails?.name,
            'Schedule Time': interview.scheduleTime.toISOString(),
            'Meeting Type': interview.meetingType,
            'Status': interview.meetingStatus,
            'Interviewer(s)': interviewerNames,
            'Duration': interview.interviewDuration || '60',
            'Address': interview.address || '',
            'Created At': interview.createdAt.toISOString(),
          };
        });

        return NextResponse.json({ 
          success: true, 
          data: exportData,
          message: `Exported ${exportData.length} interviews` 
        }, { status: 200 });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    return NextResponse.json({ 
      success: true, 
      affectedCount: result?.count || interviewIds.length,
      message: `Successfully performed ${action} on ${interviewIds.length} interview(s)` 
    }, { status: 200 });

  } catch (error) {
    console.error(`Failed to perform bulk action ${action}:`, error);
    return NextResponse.json({ 
      error: `Failed to perform ${action}`,
      details: error.message 
    }, { status: 500 });
  }
}
