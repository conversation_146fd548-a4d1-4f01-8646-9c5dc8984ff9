import { NextRequest, NextResponse } from 'next/server';
import { startOfMonth, endOfMonth, subMonths } from 'date-fns';

import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  const { organizationId, userId, userRole, filters = {} } = await request.json();

  if (!organizationId) {
    return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
  }

  try {
    // Build base where clause
    let baseWhere: any = {
      organizationId: organizationId,
    };

    // Apply role-based filtering
    const isSuperUser = ['admin', 'owner'].includes(userRole?.toLowerCase());
    if (!isSuperUser) {
      baseWhere.interviewerId = {
        has: userId,
      };
    }

    // Get current and previous month data for trends
    const now = new Date();
    const currentMonthStart = startOfMonth(now);
    const currentMonthEnd = endOfMonth(now);
    const previousMonthStart = startOfMonth(subMonths(now, 1));
    const previousMonthEnd = endOfMonth(subMonths(now, 1));

    // Fetch all interviews with related data
    const [currentMonthInterviews, previousMonthInterviews, allInterviews] = await Promise.all([
      db.videoCallInterview.findMany({
        where: {
          ...baseWhere,
          scheduleTime: {
            gte: currentMonthStart,
            lte: currentMonthEnd,
          },
        },
        include: {
          careerPractice: {
            include: {
              eventDetails: {
                select: {
                  role: true,
                  level: true,
                },
              },
            },
          },
        },
      }),
      db.videoCallInterview.findMany({
        where: {
          ...baseWhere,
          scheduleTime: {
            gte: previousMonthStart,
            lte: previousMonthEnd,
          },
        },
        include: {
          careerPractice: {
            include: {
              eventDetails: {
                select: {
                  role: true,
                  level: true,
                },
              },
            },
          },
        },
      }),
      db.videoCallInterview.findMany({
        where: baseWhere,
        include: {
          careerPractice: {
            include: {
              eventDetails: {
                select: {
                  role: true,
                  level: true,
                },
              },
            },
          },
        },
      }),
    ]);

    // Get interviewer details
    const allInterviewerIds = allInterviews.flatMap(interview => interview.interviewerId || []);
    const uniqueInterviewerIds = [...new Set(allInterviewerIds)];
    
    const interviewers = await db.user.findMany({
      where: {
        id: { in: uniqueInterviewerIds },
      },
      include: {
        userProfile: {
          select: { fullName: true },
        },
      },
    });

    // Helper function to calculate metrics
    const calculateMetrics = (interviews: any[]) => {
      const completed = interviews.filter(i => i.meetingStatus === 'COMPLETED');
      const total = interviews.length;
      const completionRate = total > 0 ? (completed.length / total) * 100 : 0;
      
      const scoresWithFeedback = completed.filter(i => 
        i.feedback && typeof i.feedback === 'object' && i.feedback.score
      );
      const averageScore = scoresWithFeedback.length > 0
        ? scoresWithFeedback.reduce((sum, i) => sum + i.feedback.score, 0) / scoresWithFeedback.length
        : 0;

      return {
        total,
        completed: completed.length,
        completionRate,
        averageScore,
      };
    };

    // Calculate overview metrics
    const currentMetrics = calculateMetrics(currentMonthInterviews);
    const previousMetrics = calculateMetrics(previousMonthInterviews);
    const allMetrics = calculateMetrics(allInterviews);

    // Performance by role
    const roleGroups = allInterviews.reduce((acc, interview) => {
      const role = interview.careerPractice?.role || interview.careerPractice?.eventDetails?.role || 'Unknown';
      if (!acc[role]) {
        acc[role] = [];
      }
      acc[role].push(interview);
      return acc;
    }, {} as Record<string, any[]>);

    const byRole = Object.entries(roleGroups).map(([role, interviews]) => {
      const metrics = calculateMetrics(interviews);
      return {
        role,
        count: metrics.total,
        averageScore: Math.round(metrics.averageScore * 10) / 10,
        completionRate: Math.round(metrics.completionRate * 10) / 10,
      };
    }).sort((a, b) => b.count - a.count);

    // Performance by interviewer
    const interviewerGroups = allInterviews.reduce((acc, interview) => {
      const interviewerIds = interview.interviewerId || [];
      interviewerIds.forEach(interviewerId => {
        if (!acc[interviewerId]) {
          acc[interviewerId] = [];
        }
        acc[interviewerId].push(interview);
      });
      return acc;
    }, {} as Record<string, any[]>);

    const byInterviewer = Object.entries(interviewerGroups).map(([interviewerId, interviews]) => {
      const interviewer = interviewers.find(i => i.id === interviewerId);
      const interviewerName = interviewer?.userProfile?.fullName || interviewer?.email || 'Unknown';
      const metrics = calculateMetrics(interviews);
      
      return {
        interviewer: interviewerName,
        count: metrics.total,
        averageScore: Math.round(metrics.averageScore * 10) / 10,
        completionRate: Math.round(metrics.completionRate * 10) / 10,
      };
    }).sort((a, b) => b.count - a.count).slice(0, 10); // Top 10 interviewers

    // Status distribution
    const statusCounts = allInterviews.reduce((acc, interview) => {
      let status = 'Scheduled';
      switch (interview.meetingStatus) {
        case 'COMPLETED':
          status = 'Completed';
          break;
        case 'CANCELLED':
          status = 'Cancelled';
          break;
        case 'PENDING':
          status = 'Scheduled';
          break;
        default:
          status = 'Scheduled';
      }
      
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byStatus = Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count,
      percentage: Math.round((count / allInterviews.length) * 100 * 10) / 10,
    }));

    // Time distribution (by hour)
    const timeDistribution = allInterviews.reduce((acc, interview) => {
      const hour = new Date(interview.scheduleTime).getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const timeDistributionArray = Object.entries(timeDistribution)
      .map(([hour, count]) => ({
        hour: parseInt(hour),
        count,
      }))
      .sort((a, b) => a.hour - b.hour);

    // Calculate no-show rate
    const noShowCount = allInterviews.filter(i => 
      i.meetingStatus === 'PENDING' && new Date(i.scheduleTime) < new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours past
    ).length;
    const noShowRate = allInterviews.length > 0 ? (noShowCount / allInterviews.length) * 100 : 0;

    // Average duration (mock data since we don't store actual duration)
    const averageDuration = 45; // Default 45 minutes

    const response = {
      overview: {
        totalInterviews: allMetrics.total,
        completedInterviews: allMetrics.completed,
        averageScore: Math.round(allMetrics.averageScore * 10) / 10,
        completionRate: Math.round(allMetrics.completionRate * 10) / 10,
        noShowRate: Math.round(noShowRate * 10) / 10,
        averageDuration,
      },
      trends: {
        interviewsThisMonth: currentMetrics.total,
        interviewsLastMonth: previousMetrics.total,
        scoresThisMonth: Math.round(currentMetrics.averageScore * 10) / 10,
        scoresLastMonth: Math.round(previousMetrics.averageScore * 10) / 10,
        completionThisMonth: Math.round(currentMetrics.completionRate * 10) / 10,
        completionLastMonth: Math.round(previousMetrics.completionRate * 10) / 10,
      },
      byRole,
      byInterviewer,
      byStatus,
      timeDistribution: timeDistributionArray,
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Failed to fetch interview analytics:', error);
    return NextResponse.json({ error: 'Failed to fetch analytics data' }, { status: 500 });
  }
}
