import { NextRequest, NextResponse } from 'next/server';
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, subWeeks, subMonths } from 'date-fns';

import { db } from '@/prisma/db';

export async function POST(request: NextRequest) {
  const { organizationId, userId, userRole, membershipId, filters = {} } = await request.json();

  if (!organizationId) {
    return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
  }

  if (!membershipId || !userRole) {
    return NextResponse.json({ error: 'Missing required access control parameters' }, { status: 400 });
  }

  try {
    // Build base where clause
    let baseWhere: any = {
      organizationId: organizationId,
    };

    // Apply role-based filtering
    const isSuperUser = ['admin', 'owner'].includes(userRole?.toLowerCase());

    if (!isSuperUser) {
      // Non-admin users see interviews where they are interviewers
      baseWhere.interviewerId = {
        has: userId,
      };
    }
    // Admin/Owner users see all interviews in their organization (no additional filtering needed)

    // Apply filters
    if (filters.status && filters.status.length > 0) {
      baseWhere.meetingStatus = {
        in: filters.status.map((s: string) => s.toUpperCase()),
      };
    }

    if (filters.meetingType && filters.meetingType.length > 0) {
      baseWhere.meetingType = {
        in: filters.meetingType,
      };
    }

    if (filters.dateRange?.start || filters.dateRange?.end) {
      baseWhere.scheduleTime = {};
      if (filters.dateRange.start) {
        baseWhere.scheduleTime.gte = new Date(filters.dateRange.start);
      }
      if (filters.dateRange.end) {
        baseWhere.scheduleTime.lte = new Date(filters.dateRange.end);
      }
    }

    if (filters.searchQuery) {
      baseWhere.OR = [
        {
          user: {
            userProfile: {
              fullName: {
                contains: filters.searchQuery,
                mode: 'insensitive',
              },
            },
          },
        },
        {
          user: {
            email: {
              contains: filters.searchQuery,
              mode: 'insensitive',
            },
          },
        },
        {
          careerPractice: {
            role: {
              contains: filters.searchQuery,
              mode: 'insensitive',
            },
          },
        },
      ];
    }

    // Fetch all interviews with related data
    const interviews = await db.videoCallInterview.findMany({
      where: baseWhere,
      include: {
        careerPractice: {
          include: {
            eventDetails: {
              select: {
                id: true,
                name: true,
                role: true,
                level: true,
                isAiQuestion: true,
              },
            },
          },
        },
        user: {
          include: {
            userProfile: {
              select: {
                fullName: true,
              },
            },
          },
        },
      },
      orderBy: {
        scheduleTime: 'desc',
      },
    });



    // Get interviewer details
    const interviewerIds = interviews.flatMap(interview => interview.interviewerId || []);
    const interviewers = await db.user.findMany({
      where: {
        id: { in: interviewerIds },
      },
      include: {
        userProfile: {
          select: { fullName: true },
        },
      },
    });

    // Transform data for frontend
    const transformedInterviews = interviews.map(interview => {
      const interviewerDetails = interviewers.filter(interviewer => 
        interview.interviewerId?.includes(interviewer.id)
      );
      
      const interviewerNames = interviewerDetails.map(interviewer => 
        interviewer.userProfile?.fullName || interviewer.email
      );

      // Determine priority based on schedule time and status
      let priority = 'medium';
      const now = new Date();
      const scheduleTime = new Date(interview.scheduleTime);
      const timeDiff = scheduleTime.getTime() - now.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);

      if (interview.meetingStatus === 'PENDING') {
        if (hoursDiff <= 2 && hoursDiff >= 0) {
          priority = 'high';
        } else if (hoursDiff <= 24 && hoursDiff >= 0) {
          priority = 'medium';
        } else {
          priority = 'low';
        }
      }

      // Map meeting status to our status format
      let status = 'scheduled';
      switch (interview.meetingStatus) {
        case 'PENDING':
          status = 'scheduled';
          break;
        case 'COMPLETED':
          status = 'completed';
          break;
        case 'CANCELLED':
          status = 'cancelled';
          break;
        default:
          status = 'scheduled';
      }

      return {
        id: interview.id,
        candidateName: interview.user?.userProfile?.fullName || interview.user?.email || 'Unknown',
        candidateEmail: interview.user?.email || '',
        candidateAvatar: null,
        role: interview.careerPractice?.role || interview.careerPractice?.eventDetails?.role || 'Unknown Role',
        level: interview.careerPractice?.level || interview.careerPractice?.eventDetails?.level || 'Unknown Level',
        interviewName: interview.careerPractice?.eventDetails?.name || 'Interview',
        scheduleTime: interview.scheduleTime.toISOString(),
        meetingType: interview.meetingType === 'videoCall' ? 'video' : interview.meetingType,
        status,
        interviewer: interviewerNames.join(', ') || 'Unassigned',
        score: interview.feedback ? (() => {
          try {
            const feedbackData = typeof interview.feedback === 'string' ? JSON.parse(interview.feedback) : interview.feedback;
            return feedbackData?.overall_score || feedbackData?.score || null;
          } catch (e) {
            return interview.feedback?.overall_score || interview.feedback?.score || null;
          }
        })() : null,
        feedback: interview.feedback ? (typeof interview.feedback === 'string' ? interview.feedback : JSON.stringify(interview.feedback)) : null,
        eventId: interview.careerPractice?.eventDetails?.id,
        careerPracticeId: interview.careerPracticeId,
        priority,
        tags: [
          interview.careerPractice?.eventDetails?.isAiQuestion ? 'AI Interview' : 'Manual',
          interview.careerPractice?.level || 'General'
        ].filter(Boolean),
        createdAt: interview.createdAt.toISOString(),
        address: interview.address,
        interviewDuration: interview.interviewDuration,
      };
    });

    // Calculate metrics
    const now = new Date();
    const today = {
      start: startOfDay(now),
      end: endOfDay(now),
    };
    const thisWeek = {
      start: startOfWeek(now),
      end: endOfWeek(now),
    };

    const todayInterviews = transformedInterviews.filter(interview => {
      const scheduleTime = new Date(interview.scheduleTime);
      return scheduleTime >= today.start && scheduleTime <= today.end;
    });

    const thisWeekCompleted = transformedInterviews.filter(interview => {
      const scheduleTime = new Date(interview.scheduleTime);
      return scheduleTime >= thisWeek.start && scheduleTime <= thisWeek.end && interview.status === 'completed';
    });

    const pendingFeedback = transformedInterviews.filter(interview => 
      interview.status === 'completed' && !interview.feedback
    );

    const completedWithScores = transformedInterviews.filter(interview => 
      interview.status === 'completed' && interview.score !== null
    );

    const averageScore = completedWithScores.length > 0 
      ? completedWithScores.reduce((sum, interview) => sum + (interview.score || 0), 0) / completedWithScores.length
      : 0;

    const completionRate = transformedInterviews.length > 0
      ? (transformedInterviews.filter(interview => interview.status === 'completed').length / transformedInterviews.length) * 100
      : 0;

    // Separate interviews by status for pipeline view
    const interviewsByStatus = {
      scheduled: transformedInterviews.filter(interview => interview.status === 'scheduled'),
      'in-progress': transformedInterviews.filter(interview => interview.status === 'in-progress'),
      completed: transformedInterviews.filter(interview => interview.status === 'completed'),
      cancelled: transformedInterviews.filter(interview => ['cancelled', 'no-show'].includes(interview.status)),
    };

    // Get upcoming scheduled interviews
    const upcomingInterviews = transformedInterviews
      .filter(interview => interview.status === 'scheduled' && new Date(interview.scheduleTime) > now)
      .sort((a, b) => new Date(a.scheduleTime).getTime() - new Date(b.scheduleTime).getTime())
      .slice(0, 10);

    const response = {
      metrics: {
        totalScheduled: transformedInterviews.filter(interview => interview.status === 'scheduled').length,
        todayInterviews: todayInterviews.length,
        completedThisWeek: thisWeekCompleted.length,
        averageScore: Math.round(averageScore * 10) / 10,
        completionRate: Math.round(completionRate * 10) / 10,
        pendingFeedback: pendingFeedback.length,
      },
      interviews: transformedInterviews,
      interviewsByStatus,
      upcomingInterviews,
      summary: {
        totalInterviews: transformedInterviews.length,
        completedInterviews: transformedInterviews.filter(interview => interview.status === 'completed').length,
        successRate: Math.round(completionRate * 10) / 10,
        timeFilter: 'all',
      },
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Failed to fetch interview dashboard data:', error);
    return NextResponse.json({ error: 'Failed to fetch dashboard data' }, { status: 500 });
  }
}
