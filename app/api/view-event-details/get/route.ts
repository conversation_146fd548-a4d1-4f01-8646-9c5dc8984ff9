import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { id, searchParams } = await request.json();
  const { page = 1, per_page = 50, resultStatus: status, user, sort } = searchParams;
  let field = 'createdAt',
    order: any = 'desc';

  if (sort) {
    [field, order] = sort.split('.');
  }

  if (!id) {
    return NextResponse.json({ error: 'EventId Not Found' }, { status: 404 });
  }

  let where: any = { eventId: id };
  let orderBy: any = { [field]: order };

  if (status) {
    const statusArray = Array.isArray(status?.split('.'))
      ? status?.split('.')
      : [status?.split('.')];
    where.resultStatus = {
      in: statusArray,
    };
  }
  if (['finalScore', 'resumeScore']?.includes(field)) {
    orderBy = {
      [field]: {
        sort: order,
        nulls: 'last',
      },
    };
  }

  if (user) {
    where.user = {
      email: {
        contains: user,
        mode: 'insensitive',
      },
    };
  }
  const parsedLimit = parseInt(per_page, 50);
  let careerPractices: any = await db.careerPractice.findMany({
    select: {
      id: true,
      event: true,
      resultStatus: true,
      finalScore: true,
      resumeScore: true,
      user: {
        select: {
          email: true,
          userProfile: {
            select: {
              fullName: true,
              userId: true,
            },
          },
        },
      },
    },
    where,
    skip: Math.max(Number(page ?? 1) - 1, 0) * Number(per_page ?? 50),
    take: Number(per_page ?? 50),
    orderBy,
  });
  const count = await db.careerPractice.count({
    where: { ...where, eventId: id },
  });
  let updatedCareerPractices;
  if (careerPractices && careerPractices?.length > 0) {
    updatedCareerPractices = careerPractices.map((item) => {
      const { user, ...newItem } = item; // Destructuring to exclude 'conversation'
      let newUser: any = {};
      newUser['name'] = user?.userProfile?.fullName;
      newUser['userId'] = user?.userProfile?.userId;
      newUser['email'] = user?.email;
      // Create a new object with only the desired properties
      return {
        ...newItem, // Include other properties
        user: newUser,
        comments: null,
      };
    });
  }

  return NextResponse.json(
    {
      items: updatedCareerPractices,
      count: count,
      currentPage: Math.max(page, 1),
      limit: parsedLimit,
      totalPages: Math.ceil(count / Number(per_page)),
    },
    { status: 200 },
  );
}
