import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { organizationId, searchParams, eventId } = await request.json();

  const { page = 1, academicsStartDate, academicsStartMonth, per_page = 50 } = searchParams;

  const where: any = {
    user: {
      memberships: { some: { role: 'STUDENT', organizationId } },
    },
  };

  if (academicsStartDate && academicsStartMonth) {
    const startMonth = new Date(`${academicsStartDate}-${academicsStartMonth}-01`);
    where.academicsStartDate = {
      gte: startMonth, // Start of the month
      lt: new Date(startMonth.getFullYear(), startMonth.getMonth() + 1, 1), // Start of the next month
    };
  }
  try {
    const invitedStudents = await db.membershipOnEventDetails.findMany({ where: { eventId } });
    const membershipIds: any = invitedStudents?.map((item) => item?.membershipId);
    const [student, count] = await Promise.all([
      db.userProfile.findMany({
        where: {
          user: {
            memberships: {
              some: {
                role: 'STUDENT',
                organizationId,
                id: {
                  in: membershipIds ?? [],
                },
              },
            },
          },
        },
        select: {
          fullName: true,
          department: true,
          userId: true,
          specialization: true,
          degree: true,
          academicsScore: true,
          academicsStartDate: true,
          academicsEndDate: true,
          user: {
            select: {
              email: true,
              memberships: {
                select: {
                  id: true,
                  DepartmentMembershipMapping: {
                    select: {
                      department: {
                        select: {
                          name: true,
                        },
                      },
                    },
                  },
                  GroupMembershipMapping: {
                    select: {
                      group: {
                        select: {
                          name: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        skip: Math.max(Number(page ?? 1) - 1, 0) * Number(per_page ?? 50),
        take: Number(per_page ?? 50),
        orderBy: {
          createdAt: 'desc',
        },
      }),
      db.userProfile.count({
        where: {
          user: {
            memberships: {
              some: {
                role: 'STUDENT',
                organizationId,
                id: {
                  in: membershipIds ?? [],
                },
              },
            },
          },
        },
      }),
    ]);

    return NextResponse.json(
      {
        student,
        totalPages: Math.ceil((count ?? 0) / Number(per_page)),
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'student Not Found' }, { status: 500 });
  }
}
