import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { organizationId, searchParams, eventId } = await request.json();

  const {
    page = 1,
    academicsStartDate,
    academicsStartMonth,
    per_page = 50,
    email,
    department,
    group,
    memberShipId,
  } = searchParams;
  const where: any = {};
  let adminDepartments: any = [];
  let memberDepartments: any = [];
  let memberGroups: any = [];
  if (memberShipId) {
    const memberShip: any = await db.membership.findUnique({
      where: {
        id: memberShipId,
      },
      include: {
        DepartmentMembershipMapping: {
          include: {
            department: true,
          },
        },
        GroupMembershipMapping: {
          include: {
            group: true,
          },
        },
      },
    });

    adminDepartments = memberShip.DepartmentMembershipMapping.filter((m) => m.role === 'ADMIN').map(
      (m) => m.department.id,
    );

    memberDepartments = memberShip.DepartmentMembershipMapping.filter(
      (m) => m.role === 'MEMBER',
    ).map((m) => m.department.id);

    memberGroups = memberShip.GroupMembershipMapping.map((m) => m.group.id);
  }

  try {
    const invitedStudents = await db.membershipOnEventDetails.findMany({ where: { eventId } });
    const membershipIds: any = invitedStudents?.map((item) => item?.membershipId);

    where.user = {
      memberships: {
        some: {
          role: 'STUDENT',
          organizationId,
          id: {
            notIn: membershipIds ?? [],
          },
          ...(memberShipId &&
            !department &&
            !group && {
              OR: [
                {
                  DepartmentMembershipMapping: {
                    some: {
                      department: {
                        id: {
                          in: [...adminDepartments],
                        },
                      },
                    },
                  },
                },
                {
                  GroupMembershipMapping: {
                    some: {
                      group: {
                        id: {
                          in: memberGroups,
                        },
                      },
                    },
                  },
                },
              ],
            }),
          ...(department && {
            DepartmentMembershipMapping: {
              some: {
                department: {
                  id: {
                    in: [department],
                  },
                },
              },
            },
            ...(!adminDepartments.includes(department) &&
              memberShipId && {
                GroupMembershipMapping: {
                  some: {
                    group: {
                      id: {
                        in: memberGroups,
                      },
                    },
                  },
                },
              }),
          }),
          ...(group && {
            GroupMembershipMapping: {
              some: {
                group: {
                  id: {
                    in: [group],
                  },
                },
              },
            },
          }),
          ...(email && {
            user: {
              email: {
                contains: email,
                mode: 'insensitive',
              },
            },
          }),
        },
      },
    };

    const [student, count] = await Promise.all([
      db.userProfile.findMany({
        where,
        select: {
          fullName: true,
          department: true,
          userId: true,
          degree: true,
          user: {
            select: {
              email: true,
              memberships: {
                select: {
                  id: true,
                  DepartmentMembershipMapping: {
                    select: {
                      department: {
                        select: {
                          name: true,
                        },
                      },
                    },
                  },
                  GroupMembershipMapping: {
                    select: {
                      group: {
                        select: {
                          name: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        skip: Math.max(Number(page ?? 1) - 1, 0) * Number(per_page ?? 50),
        take: Number(per_page ?? 50),
        orderBy: {
          createdAt: 'desc',
        },
      }),

      db.userProfile.count({
        where,
      }),
    ]);

    return NextResponse.json(
      {
        student,
        totalPages: Math.ceil((count ?? 0) / Number(per_page)),
      },
      { status: 200 },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'student Not Found' }, { status: 500 });
  }
}
