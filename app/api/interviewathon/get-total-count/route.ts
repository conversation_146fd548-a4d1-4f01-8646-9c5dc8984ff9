import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(request: any) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');
    const membershipId = searchParams.get('membershipId');
    const role = searchParams.get('role');

    if (!membershipId || !role) {
      return NextResponse.json(
        { error: 'Missing required access control parameters' },
        { status: 400 }
      );
    }

    // Build access control where clause
    const isSuperUser = ['admin', 'owner'].includes(role?.toLowerCase());
    let eventAccessWhere: any = {
      organizationId: organizationId ?? '',
      isPlacement: true,
    };

    if (!isSuperUser) {
      eventAccessWhere.MembershipOnEventDetails = {
        some: {
          membershipId,
        },
      };
    }

    const eventCounts = await db.eventDetails.count({
      where: eventAccessWhere,
    });

    return NextResponse.json({ eventCounts }, { status: 200 });
  } catch (error) {
    console.log({ error });
    return NextResponse.json(
      {
        error: 'Unable to fetch leadership data',
      },
      { status: 500 },
    );
  }
}
