import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import axios from 'axios';

export async function POST(req: NextRequest) {
  const {
    date: startTime,
    organizationId,
    user,
    eventId,
    subject,
    body,
    endTime,
  } = await req.json();

  const integration = await db.integration.findFirst({
    where: { organizationId, platform: 'GOOGLE' },
  });

  try {
    const calendar = await createCalenderEvent({
      integration,
      user,
      startTime,
      endTime,
      transactionId: eventId,
      subject,
      body,
    });
    if (calendar?.error) {
      return NextResponse.json({ calendar }, { status: 500 });
    }
    return NextResponse.json({ calendar });
  } catch (error) {
    console.log({ error: error?.response?.data?.error?.errors });
    if (error?.response?.data?.error?.status === 'UNAUTHENTICATED') {
      const response = await axios.get(
        `${process.env.NEXTAUTH_URL}/api/integration/google/get-access-token-by-refresh-token?id=${integration?.id}&refresh_token=${integration?.refreshToken}`,
      );
      const calendar = await createCalenderEvent({
        integration: response.data?.integration,
        user,
        startTime,
        endTime,
        transactionId: eventId,
        subject,
        body,
      });
      return NextResponse.json({ calendar });
    }
    return NextResponse.json({ error: 'Failed to get calendar data' }, { status: 500 });
  }
}

const createCalenderEvent = async ({
  integration,
  transactionId,
  user,
  startTime,
  endTime,
  subject,
  body,
}) => {
  const response = await axios.post(
    'https://www.googleapis.com/calendar/v3/calendars/primary/events',
    {
      summary: subject,

      start: {
        dateTime: startTime,
        timeZone: 'Asia/Kolkata',
      },
      end: {
        dateTime: endTime,
        timeZone: 'Asia/Kolkata',
      },
      attendees: [{ email: user }],
    },
    {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${integration?.accessToken}`,
      },
    },
  );
  if (response?.data) {
    return response?.data;
  } else {
    return { error: 'error creating data' };
  }
};
