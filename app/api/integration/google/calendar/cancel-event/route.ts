import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import axios from 'axios';

export async function POST(req: NextRequest) {
  const { organizationId, calendarEventId } = await req.json();

  const integration = await db.integration.findFirst({
    where: { organizationId, platform: 'GOOGLE' },
  });

  try {
    const calendar = await cancelCalenderEvent({
      integration,

      calendarEventId,
    });
    if (calendar?.error) {
      return NextResponse.json({ calendar }, { status: 500 });
    }
    return NextResponse.json({ calendar });
  } catch (error) {
    console.log({ error: error?.response?.data?.error?.errors });
    if (error?.response?.data?.error?.status === 'UNAUTHENTICATED') {
      const response = await axios.get(
        `${process.env.NEXTAUTH_URL}/api/integration/google/get-access-token-by-refresh-token?id=${integration?.id}&refresh_token=${integration?.refreshToken}`,
      );
      const calendar = await cancelCalenderEvent({
        integration: response.data?.integration,
        calendarEventId,
      });
      return NextResponse.json({ calendar });
    }
    return NextResponse.json({ error: 'Failed to get calendar data' }, { status: 500 });
  }
}

const cancelCalenderEvent = async ({ integration, calendarEventId }) => {
  const response = await axios.delete(
    `https://www.googleapis.com/calendar/v3/calendars/primary/events/${calendarEventId}`,
    {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${integration?.accessToken}`,
      },
    },
  );
  return response.data;
};
