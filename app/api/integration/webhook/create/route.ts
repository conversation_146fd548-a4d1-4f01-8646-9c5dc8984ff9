import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { data } = await request.json();
  try {
    const webhook = await db.webhook.create({
      data,
    });
    return NextResponse.json(
      {
        webhook,
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'webhook create failed' }, { status: 500 });
  }
}
