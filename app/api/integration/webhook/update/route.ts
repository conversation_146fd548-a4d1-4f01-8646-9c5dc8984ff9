import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { id, data } = await request.json();
  try {
    let webhook = await db.webhook.update({
      where: { id },
      data,
    });
    return NextResponse.json(
      {
        webhook,
      },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Error updating webhook' }, { status: 500 });
  }
}
