import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import axios from 'axios';

const APP_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
export async function GET(req: NextRequest) {
  const url = req.url;
  const urlParams = new URLSearchParams(url.split('?')[1]);
  const code = urlParams.get('code');
  const state = urlParams.get('state');
  const error = urlParams.get('error');

  if (!code) {
    return NextResponse.json({ message: 'Authorization code is missing' }, { status: 400 });
  }

  const tokenEndpoint = `https://login.microsoftonline.com/common/oauth2/v2.0/token`;
  const params = new URLSearchParams();

  params.append('client_id', process.env.NEXT_PUBLIC_MICROSOFT_CLIENT_ID ?? '');
  params.append(
    'scope',
    'Calendars.ReadWrite Calendars.Read Calendars.Read.Shared Calendars.ReadWrite.Shared',
  ); // Requested scopes
  params.append('code', code);
  params.append('redirect_uri', `${APP_URL}/api/integration/outlook/callback`);
  params.append('grant_type', 'authorization_code');
  params.append('client_secret', process.env.MICROSOFT_CLIENT_SECRET ?? '');

  try {
    const response = await axios.post(tokenEndpoint, params, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
    if (response.data?.access_token) {
      const payload: any = {
        platform: 'OUTLOOK',
        accountId: ' ',
        accessToken: response.data?.access_token,
        refreshToken: response.data?.refresh_token,
        organizationId: state,
        config: response.data,
      };
      const integration = await db.integration.create({ data: payload });
      if (integration.id) {
        return Response.redirect(`${APP_URL}/tenant/integration`);
      } else {
        return Response.redirect(`${APP_URL}/tenant/integration?error=${error}`);
      }
    } else {
      return Response.redirect(`${APP_URL}/tenant/integration?error=${error}`);
    }
  } catch (error) {
    console.error('Token acquisition failed:', error?.response?.data);
    return Response.redirect(`${APP_URL}/tenant/integration?error=${error}`);
  }
}
