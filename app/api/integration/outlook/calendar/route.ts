import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import { getFreeIntervals } from '@/utils/calculate-freetime';
import axios from 'axios';
import { getToken } from 'next-auth/jwt';

export async function GET(req: NextRequest) {
  const token = await getToken({ req });
  if (!token)
    return NextResponse.json({
      error: 'Unauthorized',
      status: 401,
    });
  const url = req.url;
  const urlParams = new URLSearchParams(url.split('?')[1]);
  const organizationId = urlParams.get('organizationId') ?? '';
  const date = urlParams.get('date') ?? '';
  const users = urlParams.get('user')?.split(',') ?? []; // Modified to handle comma-separated users
  const duration = Number(urlParams.get('duration') ?? 60);

  const inputDate = new Date(date);

  // Extract the date components
  const year = inputDate.getFullYear();
  const month = String(inputDate.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const day = String(inputDate.getDate()).padStart(2, '0');

  const startOfDay = `${year}-${month}-${day}T03:30:00`;
  const endOfDay = `${year}-${month}-${day}T15:30:00`;
  const integration = await db.integration.findFirst({
    where: { organizationId, platform: 'OUTLOOK' },
  });
  try {
    const calendar: any = await fetchCalender({
      integration,
      users, // Pass array of users
      startOfDay,
      endOfDay,
      duration,
    });
    if (calendar?.error) {
      return NextResponse.json({ calendar }, { status: 500 });
    }
    return NextResponse.json({ calendar });
  } catch (error) {
    if (error?.response?.data?.error?.code === 'InvalidAuthenticationToken') {
      const response = await axios.get(
        `${process.env.NEXTAUTH_URL}/api/integration/outlook/get-access-token-by-refresh-token?id=${integration?.id}&refresh_token=${integration?.refreshToken}`,
      );
      const calendar = await fetchCalender({
        integration: response.data?.integration,
        users,
        startOfDay,
        endOfDay,
        duration,
      });

      return NextResponse.json({ calendar });
    }
    console.log({ error: error?.response?.data?.error });
    console.log({ error });
    return NextResponse.json({ error: 'Failed to get calendar data' }, { status: 500 });
  }
}

const fetchCalender = async ({ integration, users, startOfDay, endOfDay, duration }) => {
  const response = await axios.post(
    'https://graph.microsoft.com/v1.0/me/calendar/getSchedule',
    {
      schedules: users, // Pass array of users directly
      startTime: {
        dateTime: startOfDay,
        timeZone: 'Etc/GMT',
      },
      endTime: {
        dateTime: endOfDay,
        timeZone: 'Etc/GMT',
      },
      availabilityViewInterval: duration,
    },
    {
      headers: {
        'Content-Type': 'application/json',
        Prefer: 'outlook.timezone="Etc/GMT"',
        Authorization: `Bearer ${integration?.accessToken}`,
      },
    },
  );

  if (response?.data) {
    // Get busy times for all users
    const allBusyTimes = response.data.value.map(
      (schedule) =>
        schedule.scheduleItems
          ?.filter((item) => ['tentative', 'busy'].includes(item.status))
          ?.map((item) => ({
            start: item.start.dateTime,
            end: item.end.dateTime,
          })) || [],
    );

    // Combine all busy times into a single array
    const combinedBusyTimes = allBusyTimes.flat();

    // Sort and merge overlapping busy times
    const mergedBusyTimes = combinedBusyTimes
      .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime())
      .reduce((acc, curr) => {
        if (acc.length === 0) return [curr];
        const last = acc[acc.length - 1];
        if (new Date(curr.start) <= new Date(last.end)) {
          last.end = new Date(last.end) > new Date(curr.end) ? last.end : curr.end;
          return acc;
        }
        return [...acc, curr];
      }, []);

    const freeTime = getFreeIntervals(
      mergedBusyTimes,
      new Date(startOfDay),
      new Date(endOfDay),
      duration,
    );
    return freeTime;
  } else {
    return { error: 'error fetching data' };
  }
};
