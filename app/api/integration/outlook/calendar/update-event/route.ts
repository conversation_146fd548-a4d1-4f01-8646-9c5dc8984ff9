import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import axios from 'axios';

export async function POST(req: NextRequest) {
  const {
    date: startTime,
    organizationId,
    user,
    eventId,
    subject,
    body,
    endTime,
    calendarEventId,
  } = await req.json();
  const integration = await db.integration.findFirst({
    where: { organizationId, platform: 'OUTLOOK' },
  });

  try {
    const calendar = await updateCalenderEvent({
      integration,
      user,
      startTime,
      endTime,
      transactionId: eventId,
      subject,
      body,
      calendarEventId,
    });
    if (calendar?.error) {
      return NextResponse.json({ calendar }, { status: 500 });
    }
    return NextResponse.json({ calendar });
  } catch (error) {
    if (error?.response?.data?.error?.code === 'InvalidAuthenticationToken') {
      const response = await axios.get(
        `${process.env.NEXTAUTH_URL}/api/integration/outlook/get-access-token-by-refresh-token?id=${integration?.id}&refresh_token=${integration?.refreshToken}`,
      );
      const calendar = await updateCalenderEvent({
        integration: response.data?.integration,
        user,
        startTime,
        endTime,
        transactionId: eventId,
        subject,
        body,
        calendarEventId,
      });
      return NextResponse.json({ calendar });
    }
    console.log({ error: error?.response?.data?.error });
    console.log({ error });
    return NextResponse.json({ error: 'Failed to get calendar data' }, { status: 500 });
  }
}

const updateCalenderEvent = async ({
  integration,
  transactionId,
  user,
  startTime,
  endTime,
  subject,
  body,
  calendarEventId,
}) => {
  const response = await axios.patch(
    `https://graph.microsoft.com/v1.0/me/calendar/events/${calendarEventId}`,
    {
      subject: subject,
      body: {
        contentType: 'HTML',
        content: body,
      },
      start: {
        dateTime: startTime,
        timeZone: 'India Standard Time',
      },
      end: {
        dateTime: endTime,
        timeZone: 'India Standard Time',
      },
      location: {},
      attendees: [
        {
          emailAddress: {
            address: user,
          },
          type: 'required',
        },
      ],
      allowNewTimeProposals: true,
    },
    {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${integration?.accessToken}`,
      },
    },
  );
  if (response?.data) {
    return response?.data;
  } else {
    return { error: 'error creating data' };
  }
};
