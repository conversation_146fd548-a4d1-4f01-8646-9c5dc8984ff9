import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import axios from 'axios';

export async function POST(req: NextRequest) {
  const {
    date: startTime,
    organizationId,
    user,
    eventId,
    subject,
    body,
    endTime,
  } = await req.json();
  const integration = await db.integration.findFirst({
    where: { organizationId, platform: 'OUTLOOK' },
  });

  try {
    const calendar = await createCalenderEvent({
      integration,
      user,
      startTime,
      endTime,
      transactionId: eventId,
      subject,
      body,
    });
    if (calendar?.error) {
      return NextResponse.json({ calendar }, { status: 500 });
    }
    return NextResponse.json({ calendar });
  } catch (error) {
    if (error?.response?.data?.error?.code === 'InvalidAuthenticationToken') {
      const response = await axios.get(
        `${process.env.NEXTAUTH_URL}/api/integration/outlook/get-access-token-by-refresh-token?id=${integration?.id}&refresh_token=${integration?.refreshToken}`,
      );
      const calendar = await createCalenderEvent({
        integration: response.data?.integration,
        user,
        startTime,
        endTime,
        transactionId: eventId,
        subject,
        body,
      });
      return NextResponse.json({ calendar });
    }
    console.log({ error: error?.response?.data?.error });
    console.log({ error });
    return NextResponse.json({ error: 'Failed to get calendar data' }, { status: 500 });
  }
}

const createCalenderEvent = async ({
  integration,
  transactionId,
  user,
  startTime,
  endTime,
  subject,
  body,
}) => {
  const response = await axios.post(
    'https://graph.microsoft.com/v1.0/me/calendar/events',
    {
      subject: subject,
      body: {
        contentType: 'HTML',
        content: body,
      },
      start: {
        dateTime: startTime,
        timeZone: 'India Standard Time',
      },
      end: {
        dateTime: endTime,
        timeZone: 'India Standard Time',
      },
      location: {},
      attendees: [
        {
          emailAddress: {
            address: user,
          },
          type: 'required',
        },
      ],
      allowNewTimeProposals: true,
      transactionId,
    },
    {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${integration?.accessToken}`,
      },
    },
  );
  if (response?.data) {
    return response?.data;
  } else {
    return { error: 'error creating data' };
  }
};
