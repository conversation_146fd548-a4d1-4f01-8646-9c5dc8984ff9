import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';
import axios from 'axios';

export async function POST(req: NextRequest) {
  const { organizationId, calendarEventId } = await req.json();
  const integration = await db.integration.findFirst({
    where: { organizationId, platform: 'OUTLOOK' },
  });

  try {
    const calendar = await cancelCalenderEvent({
      integration,

      calendarEventId,
    });
    if (calendar?.error) {
      return NextResponse.json({ calendar }, { status: 500 });
    }
    return NextResponse.json({ calendar });
  } catch (error) {
    if (error?.response?.data?.error?.code === 'InvalidAuthenticationToken') {
      const response = await axios.get(
        `${process.env.NEXTAUTH_URL}/api/integration/outlook/get-access-token-by-refresh-token?id=${integration?.id}&refresh_token=${integration?.refreshToken}`,
      );
      const calendar = await cancelCalenderEvent({
        integration: response.data?.integration,
        calendarEventId,
      });
      return NextResponse.json({ calendar });
    }
    console.log({ error: error?.response?.data?.error });
    console.log({ error });
    return NextResponse.json({ error: 'Failed to get calendar data' }, { status: 500 });
  }
}

const cancelCalenderEvent = async ({
  integration,

  calendarEventId,
}) => {
  const response = await axios.post(
    `https://graph.microsoft.com/v1.0/me/calendar/events/${calendarEventId}/cancel`,
    {},
    {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${integration?.accessToken}`,
      },
    },
  );

  return response?.data;
};
