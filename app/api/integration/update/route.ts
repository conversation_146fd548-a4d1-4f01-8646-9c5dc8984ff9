import { NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(request: any) {
  const { id, data } = await request.json();
  try {
    let integration = await db.integration.update({
      where: { id },
      data,
    });
    return NextResponse.json({
      integration,
      status: 200,
    });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: 'Interview Not Found', status: 500 });
  }
}
