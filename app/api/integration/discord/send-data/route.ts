import { NextRequest, NextResponse } from 'next/server';

import { formatDateAndTime } from '@/utils/utc-to-ist';

const app_base_url = process.env.NEXTAUTH_URL;

async function writeToDiscord(url, interview) {
  const now = new Date();
  try {
    await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content: `Candidate **${interview?.user?.email}** has completed the interview.\n\n**Interview Details:**\n**Name:** ${interview?.event}\n**Role:** ${interview?.role}\n**Level:** ${interview?.level}`,
        embeds: [
          {
            title: 'Check Results',
            url: `${app_base_url}/interviews/${interview?.eventDetails?.id}/${interview?.id}`,
            description: `Click above to check results`,
            color: 7458673,
          },
        ],
      }),
    });
  } catch (error) {
    throw error;
  }
}

export async function POST(req: NextRequest) {
  try {
    const { url, interview } = await req.json();
    await writeToDiscord(url, interview);
    return NextResponse.json({ message: 'Success' });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to send data to Discord' }, { status: 500 });
  }
}
