import { NextRequest, NextResponse } from 'next/server';

import { formatDateAndTime } from '@/utils/utc-to-ist';

const app_base_url = process.env.NEXTAUTH_URL;

async function writeToSlack(access_token, channelId, interview) {
  const now = new Date();
  try {
    let blockResponse = [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `Interview Details:\nName: ${interview?.event}\nRole: ${interview?.role}\nLevel: ${interview?.level}`,
        },
      },
      {
        type: 'divider',
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `Candidate ${interview?.user?.email} has completed the interview.\n\nURL:${app_base_url}/interviews/${interview?.eventDetails?.id}/${interview?.id}`,
        },
      },
    ];

    const response = await fetch('https://slack.com/api/chat.postMessage', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channel: channelId,
        blocks: blockResponse,
      }),
    });

    if (!response.ok) {
      throw new Error('Network response was not ok');
    }

    const data = await response.json();

    if (!data.ok) {
      throw new Error(data.error);
    }
  } catch (error) {
    throw error;
  }
}

export async function POST(req: NextRequest) {
  try {
    const { access_token, channelId, interview } = await req.json();
    await writeToSlack(access_token, channelId, interview);
    return NextResponse.json({ message: 'Success' });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to send data to Discord' }, { status: 500 });
  }
}
