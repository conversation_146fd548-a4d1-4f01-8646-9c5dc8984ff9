import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

const SLACK_CLIENT_ID = process.env.NEXT_PUBLIC_SLACK_CLIENT_ID;
const SLACK_CLIENT_SECRET = process.env.SLACK_CLIENT_SECRET;
const APP_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export async function GET(req: NextRequest) {
  const url = req.url;
  const queryParams = new URLSearchParams(url.split('?')[1]); // Split the URL and get the query parameters
  const code = queryParams.get('code');
  const state = queryParams.get('state');
  const error = queryParams.get('error');
  const redirectUrl = `${APP_URL}/api/integration/slack/callback`;

  if (code && typeof code !== 'string') {
    return NextResponse.json({ message: '`code` must be a string' }, { status: 500 });
  }
  const formData = new FormData();
  formData.append('code', code ?? '');
  formData.append('client_id', SLACK_CLIENT_ID ?? '');
  formData.append('client_secret', SLACK_CLIENT_SECRET ?? '');
  formData.append('redirect_uri', redirectUrl ?? '');

  if (code) {
    const response = await fetch('https://slack.com/api/oauth.v2.access', {
      method: 'POST',
      body: formData,
    });

    const data = await response.json();
    //update database
    if (data.ok) {
      const payload: any = {
        platform: 'SLACK',
        accountId: data?.authed_user?.id,
        accessToken: data?.access_token,
        organizationId: state,
        config: data,
      };
      const integration = await db.integration.create({ data: payload });
      if (integration.id) {
        return Response.redirect(`${APP_URL}/tenant/integration`);
      } else {
        return Response.redirect(`${APP_URL}/tenant/integration?error=${error}`);
      }
    } else {
      return Response.redirect(`${APP_URL}/tenant/integration?error=${data.message}`);
    }
  } else if (error) {
    return Response.redirect(`${APP_URL}/tenant/integration?error=${error}`);
  }
}
