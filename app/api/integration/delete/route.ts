import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function GET(req: NextRequest) {
  const url = req.url;
  const queryParams = new URLSearchParams(url.split('?')[1]); // Split the URL and get the query parameters
  const id = queryParams.get('id') ?? '';
  try {
    const integration = await db.integration.delete({
      where: {
        id,
      },
    });
    return NextResponse.json({ integration }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error }, { status: 200 });
  }
}
