import { NextRequest, NextResponse } from 'next/server';

import { formatDateAndTime } from '@/utils/utc-to-ist';

const app_base_url = process.env.NEXTAUTH_URL;

async function writeToMSTeams(url, interview) {
  const now = new Date();
  try {
    await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        '@type': 'MessageCard',
        '@context': 'http://schema.org/extensions',
        themeColor: '0076D7',
        summary: `Candidate **${interview?.user?.email}** has completed the interview.`,
        sections: [
          {
            activityTitle: `Candidate **${interview?.user?.email}** has completed the interview.`,
            facts: [
              {
                name: 'Interview Name',
                value: interview?.event,
              },
              {
                name: 'Role',
                value: interview?.role,
              },
              {
                name: 'Level',
                value: interview?.level,
              },
            ],
            markdown: true,
          },
        ],
        potentialAction: [
          {
            '@type': 'OpenUri',
            name: 'View Results',
            targets: [
              {
                os: 'default',
                uri: `${app_base_url}/interviews/${interview?.eventDetails?.id}/${interview?.id}`,
              },
            ],
          },
        ],
      }),
    });
  } catch (error) {
    throw error;
  }
}

export async function POST(req: NextRequest) {
  try {
    const { url, interview } = await req.json();
    await writeToMSTeams(url, interview);
    return NextResponse.json({ message: 'Success' });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to send data to Discord' }, { status: 500 });
  }
}
