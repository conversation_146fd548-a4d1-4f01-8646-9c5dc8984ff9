import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/prisma/db';

export async function POST(req: NextRequest) {
  const { organizationId, searchParams } = await req.json();
  const { page = 1, per_page = 10 } = searchParams;

  try {
    const [integration, count] = await Promise.all([
      db.webhook.findMany({
        where: {
          organizationId,
          source: 'MS_TEAMS',
        },
        skip: Math.max(Number(page ?? 1) - 1, 0) * Number(per_page ?? 10),
        take: Number(per_page ?? 10),
        orderBy: {
          createdAt: 'desc',
        },
      }),
      db.webhook.count({
        where: {
          organizationId,
          source: 'MS_TEAMS',
        },
      }),
    ]);
    return NextResponse.json(
      { integration, pageCount: Math.ceil((count ?? 0) / Number(per_page)) },
      { status: 200 },
    );
  } catch (error) {
    return NextResponse.json({ error }, { status: 200 });
  }
}
