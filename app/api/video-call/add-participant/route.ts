import { NextResponse } from 'next/server';

import base64 from 'base-64';

export const maxDuration = 299;

const dyteBaseURL = process.env.DYTE_BASE_URL;
const dyteOrgId = process.env.DYTE_ORGANIZATION_ID;
const dyteAPIKey = process.env.DYTE_API_KEY;

export async function POST(request: any) {
  const { meetingId, userId, preset_name } = await request.json();
  const authorization = base64.encode(`${dyteOrgId}:${dyteAPIKey}`);
  let data;
  try {
    const response = await fetch(`${dyteBaseURL}/meetings/${meetingId}/participants`, {
      method: 'POST',
      body: JSON.stringify({
        preset_name,
        custom_participant_id: userId,
      }),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Basic ${authorization}`,
      },
    });
    data = await response?.json();

    if (data?.success) {
      return NextResponse.json(
        {
          id: data,
        },
        {
          status: 200,
        },
      );
    } else {
      return NextResponse.json({ error: 'Failed to add participant' }, { status: 500 });
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      {
        id: data,
      },
      { status: 500 },
    );
  }
}
