'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import { processCheckoutSession } from '@/app/actions/stripe';
import { useSession } from 'next-auth/react';

import { buttonVariants } from '@camped-ui/button';

export default function ResultPage({ searchParams }: { searchParams: { session_id: string } }) {
  const { data } = useSession();
  const [paymentIntent, setPaymentIntent] = useState<
    | {
        status: string;
      }
    | undefined
  >();
  const [loading, setLoading] = useState(true);
  if (!searchParams.session_id)
    throw new Error('Please provide a valid session_id (`cs_test_...`)');

  useEffect(() => {
    if (!searchParams?.session_id || !data?.user) return;
    (async () => {
      setLoading(true);
      const response = await processCheckoutSession(searchParams.session_id, data?.user);
      setLoading(false);
      setPaymentIntent(response?.paymentIntent);
    })();
  }, [searchParams?.session_id, data?.user]);

  if (loading) return;

  if (paymentIntent?.status !== 'succeeded')
    return (
      <>
        <div className="text-center">
          <h3 className="text-center text-base font-semibold text-gray-900 md:text-2xl">
            Payment Failure!
          </h3>
          <div className="py-10 text-center">
            <Link
              href={'/'}
              className="hover:[linear-gradient(0deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)), #0D2247] group flex scale-100 items-center justify-center gap-x-2 rounded-full bg-primary px-6 py-2 text-[16px] font-bold text-white no-underline transition-all duration-75 active:scale-95"
              style={{ alignSelf: 'baseline' }}
            >
              Go Home
            </Link>
          </div>
        </div>
      </>
    );

  return (
    <>
      <svg viewBox="0 0 24 24" className="mx-auto my-6 h-16 w-16 text-green-600">
        <path
          fill="currentColor"
          d="M12,0A12,12,0,1,0,24,12,12.014,12.014,0,0,0,12,0Zm6.927,8.2-6.845,9.289a1.011,1.011,0,0,1-1.43.188L5.764,13.769a1,1,0,1,1,1.25-1.562l4.076,3.261,6.227-8.451A1,1,0,1,1,18.927,8.2Z"
        ></path>
      </svg>

      <div className="text-center">
        <h3 className="text-center text-base font-semibold text-gray-900 md:text-2xl">
          Payment Done!
        </h3>
        <p className="my-2 text-gray-600">Thank you for completing your secure online payment.</p>
        <p> Have a great day! </p>
        <div className="py-10 text-center">
          <Link
            href="/"
            className={`${buttonVariants({
              variant: 'default',
            })} w-full`}
          >
            GO HOME
          </Link>
        </div>
      </div>
    </>
  );
}
