'use client';

import { SessionProvider } from 'next-auth/react';

export default function ResultLayout({ children }: { children: React.ReactNode }): JSX.Element {
  return (
    <div className="flex w-full flex-col md:flex-row">
      <div className="flex min-h-[60vh] w-full flex-col justify-center px-4 pb-8 pt-2 md:h-screen md:px-0 md:py-2">
        <div className="flex h-full w-full flex-col items-center justify-center">
          <div className="page-container">
            <div className="bg-background p-6 md:mx-auto">
              <SessionProvider>{children}</SessionProvider>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
