import { redirect } from 'next/navigation';

export default async function Onboarding(props) {
  const searchParams = props?.searchParams || {};
  const id = searchParams?.id;

  // Redirect to new onboarding path with all query parameters preserved
  if (id) {
    const queryString = new URLSearchParams(searchParams).toString();
    return redirect(`/interview/onboarding?${queryString}`);
  } else {
    // If no ID provided, redirect to 404
    return redirect('/404');
  }
}
