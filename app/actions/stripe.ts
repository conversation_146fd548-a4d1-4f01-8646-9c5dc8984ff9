'use server';

import { stripe } from '@/lib/stripe';
import { db } from '@/prisma/db';
import type { Stripe } from 'stripe';

export async function processCheckoutSession(session_id, user) {
  const checkoutSession: Stripe.Checkout.Session = await stripe.checkout.sessions.retrieve(
    session_id,
    {
      expand: ['line_items', 'payment_intent'],
    },
  );
  const paymentIntent = checkoutSession.payment_intent as Stripe.PaymentIntent;
  if (paymentIntent?.status === 'succeeded') {
    await db.user.update({
      data: {
        premium: true,
      },
      where: {
        email: user['email']?.toLowerCase(),
      },
    });
  }

  return { paymentIntent };
}
