'use client';

import { useCallback, useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import SimpleVoiceAssistant from '@/components/SimpleVoiceAssistant';
import { RoomContext } from '@livekit/components-react';
import '@livekit/components-styles';
import { Room, RoomEvent, Track } from 'livekit-client';

export default function InterviewPage({ params }: { params: { id: string } }) {
  const [room] = useState(() => new Room());
  const [token, setToken] = useState('');
  const [url, setUrl] = useState('');
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const fetchConnectionDetails = useCallback(async () => {
    try {
      const res = await fetch(`/api/livekit-connect`);
      if (!res.ok) throw new Error('Failed to fetch connection details');
      const data = await res.json();
      setToken(data.participantToken);
      setUrl(data.serverUrl);
    } catch (err) {
      console.error('Connection error:', err);
      setError('Failed to connect to interview room');
      router.push('/'); // Redirect if room doesn't exist
    }
  }, [router]);

  useEffect(() => {
    fetchConnectionDetails();

    const handleDeviceError = (error: Error) => onDeviceFailure(error);
    room.on(RoomEvent.MediaDevicesError, handleDeviceError);

    return () => {
      room.off(RoomEvent.MediaDevicesError, handleDeviceError);
      if (isConnected) {
        room.disconnect();
      }
    };
  }, [room, fetchConnectionDetails, isConnected]);

  const onConnectButtonClicked = async () => {
    if (!token || !url) {
      setError('Missing connection credentials');
      return;
    }

    try {
      setStartTime(new Date());
      await room.connect(url, token, { autoSubscribe: true });
      await Promise.all([
        room.localParticipant.setMicrophoneEnabled(true),
        room.localParticipant.setCameraEnabled(true),
      ]);
      setIsConnected(true);
    } catch (err) {
      console.error('Connection failed:', err);
      setError('Failed to join the interview room');
    }
  };

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="rounded-lg bg-red-50 p-4 text-lg text-red-500">{error}</div>
      </div>
    );
  }

  if (!url) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-lg">Loading interview room...</div>
      </div>
    );
  }

  return (
    <main data-lk-theme="default" className="grid h-full content-center bg-[var(--lk-bg)]">
      <RoomContext.Provider value={room}>
        <div className="lk-room-container mx-auto max-h-[90vh] w-[90vw] max-w-[1024px]">
          <SimpleVoiceAssistant
            onConnectButtonClicked={onConnectButtonClicked}
            roomName={params.id}
            startTime={startTime}
            // isConnected={isConnected}
          />
        </div>
      </RoomContext.Provider>
    </main>
  );
}

function onDeviceFailure(error: Error) {
  console.error('Device error:', error);
  alert(
    'Error acquiring camera or microphone permissions. Please:\n\n' +
      '1. Check your browser permissions\n' +
      '2. Ensure no other app is using your camera/mic\n' +
      '3. Reload the page',
  );
}
