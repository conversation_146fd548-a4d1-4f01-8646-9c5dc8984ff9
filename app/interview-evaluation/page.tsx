import Header from '@/components/Header';
import PageHeader from '@/components/page-header';
import { InterviewEvaluationScreen } from '@/components/wrapper-screen/interview-evaluation-screen';
import { VideoInterviewDetailScreen } from '@/components/wrapper-screen/interview/video-interview-detail-screen';
import { ViewInterviewDetailScreen } from '@/components/wrapper-screen/interview/view-interview-detail-screen';
import { authOptions } from '@/lib/auth';
import { getInterviewDetails, getUserInterviewDetails, getUserProfile } from '@/services/apicall';
import { getServerSession } from 'next-auth';

import { Card, CardDescription, CardTitle } from '@camped-ui/card';

export default async function DemoPage(props) {
  const session = await getServerSession(authOptions);

  const id = props?.searchParams?.id;
  const hasBack = props?.searchParams?.hasBack === 'true';
  const isAdmin = props?.searchParams?.isAdmin === 'true';
  let response;
  if (isAdmin) {
    response = await getInterviewDetails(id);

    return (
      <ViewInterviewDetailScreen
        careerPractice={response?.result}
        userProfile={response?.userProfile}
        user={response?.user}
        userId={session?.userId}
        members={[]}
        platform=""
        isPublic={isAdmin}
      />
    );
  }

  response = await getUserInterviewDetails(id, session?.userId);

  // Debug logging to understand the data structure
  console.log('🔍 Interview Evaluation Debug:', {
    id,
    eventDetails: response?.items?.eventDetails,
    isAiQuestion: response?.items?.eventDetails?.isAiQuestion,
    conversation: response?.items?.conversation,
    conversationLength: response?.items?.conversation?.length,
    hasS3Ids: response?.items?.conversation?.map((item: any) => ({ s3Id: item?.s3Id, room_name: item?.room_name }))
  });

  // Determine if this is an AI interview based on multiple indicators
  const isAiInterviewByFlag = response?.items?.eventDetails?.isAiQuestion === true;
  const isAiInterviewByConversation = response?.items?.conversation === null ||
    (Array.isArray(response?.items?.conversation) && response?.items?.conversation.length <= 1);

  // Use either indicator to determine if it's an AI interview
  const isAiInterview = isAiInterviewByFlag || isAiInterviewByConversation;

  // For AI interviews that are completed, always show VideoInterviewDetailScreen for HLS player
  const shouldShowVideoScreen = isAiInterview && response?.items?.interviewStatus === 'COMPLETED';

  // For regular interviews, check if there's a room_name (video meeting)
  const hasVideoMeeting = response?.items?.conversation?.[0]?.room_name;

  console.log('🎯 Decision Logic:', {
    isAiInterviewByFlag,
    isAiInterviewByConversation,
    isAiInterview,
    shouldShowVideoScreen,
    hasVideoMeeting,
    interviewStatus: response?.items?.interviewStatus,
    willShowVideoScreen: shouldShowVideoScreen || (!isAiInterview && hasVideoMeeting)
  });

  return (
    <>
      <Header data={session} showMenus={false} />
      <div className="mx-auto mt-[85px] flex w-full flex-col gap-4 p-4 lg:flex-row lg:justify-center">
        <div className="flex w-full max-w-[900px] flex-col gap-4">
          <PageHeader
            hasBack={hasBack}
            title={`Performance Analysis - ${
              response?.items?.user?.userProfile?.fullName === '' ||
              !response?.items?.user?.userProfile?.fullName
                ? response?.items?.user?.email
                : response?.items?.user?.userProfile?.fullName
            }`}
          />
          {/* Show VideoInterviewDetailScreen for AI interviews that are completed OR regular interviews with video meetings */}
          {shouldShowVideoScreen || (!isAiInterview && hasVideoMeeting) ? (
            <VideoInterviewDetailScreen
              meeting={{
                ...response.items,
                s3RecordingId: response.items.conversation?.[0]?.s3Id,
                role: response.items?.role,
                level: response.items?.level,
                organizationId: response.items?.eventDetails?.organizationId
              }}
              userId={''}
              isAiInterview={isAiInterview}
              candidateId={response.items?.userId}
              organizationId={response.items?.eventDetails?.organizationId}
              currentOrgId={response.items?.eventDetails?.organizationId}
              currentOrgRole={''}
              showRecordings={false}
            />
          ) : (
            <InterviewEvaluationScreen careerPractice={response.items} session={{ ...session }} />
          )}
        </div>
       
      </div>
    </>
  );
}
